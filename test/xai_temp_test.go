package test

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/sashabaranov/go-openai/jsonschema"
	"math/rand"
	"strings"
	"testing"
	"time"
	"unicode/utf8"
	models "xai.backend/internal/model/12_subscription"
	"xai.backend/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/crypto/bcrypt"
)

func TestTestpassword(t *testing.T) {
	// 从日志中获取的数据库存储的哈希密码
	hashedPasswordFromDB := "$2a$10$2yS/wTgdyiwH96gtHHr16uz9CuLz2V6u2WR2hk.prZdjoh2DvLXWe"

	// 用户尝试登录时输入的明文密码
	plainPasswordInput := "qwe123"

	fmt.Printf("数据库中的哈希: %s\n", hashedPasswordFromDB)
	fmt.Printf("用户输入的明文: %s\n", plainPasswordInput)
	fmt.Println("--- 开始比较 ---")

	// 使用 bcrypt 进行比较
	// 第一个参数是数据库中的哈希值
	// 第二个参数是用户输入的明文密码
	err := bcrypt.CompareHashAndPassword([]byte(hashedPasswordFromDB), []byte(plainPasswordInput))

	// 检查比较结果
	if err != nil {
		// 如果 err 不是 nil，表示密码不匹配或发生其他错误
		fmt.Printf("密码比较失败: %v\n", err)
		if err == bcrypt.ErrMismatchedHashAndPassword {
			fmt.Println("原因：密码不匹配。")
		}
	} else {
		// 如果 err 是 nil，表示密码匹配
		fmt.Println("密码比较成功！匹配。")
	}

	fmt.Println("--- 尝试一个错误的密码 ---")
	wrongPasswordInput := "wrongpassword"
	fmt.Printf("用户输入的明文: %s\n", wrongPasswordInput)

	errWrong := bcrypt.CompareHashAndPassword([]byte(hashedPasswordFromDB), []byte(wrongPasswordInput))
	if errWrong != nil {
		fmt.Printf("密码比较失败: %v\n", errWrong)
		if errWrong == bcrypt.ErrMismatchedHashAndPassword {
			fmt.Println("原因：密码不匹配。 (这是预期的)")
		}
	} else {
		fmt.Println("密码比较成功！匹配。 (这不应该发生)")
	}
}

func TestLLM(t *testing.T) {
	// -- 开始构建 messages 结构 --
	var llmMessages []map[string]string

	// 1. 添加 System Prompt (使用 chatInfo 中的)
	// 确保 chatInfo.ChatSystemPrompt.Valid 且不为空
	systemPromptContent := "你是一个乐于助人的AI助手" // 默认值

	llmMessages = append(llmMessages, map[string]string{
		"role":    "system",
		"content": systemPromptContent,
	})

	// 2. 添加历史消息

	// 3. 添加当前用户消息
	llmMessages = append(llmMessages, map[string]string{
		"role":    "user",
		"content": "你好",
	})

	// 构建请求体 - 使用组装好的 llmMessages
	llmReqPayload := map[string]interface{}{
		"model":    "modelName",
		"stream":   true,
		"messages": llmMessages, // 直接使用 Go 切片
	}

	// 这里的 Marshal 会将 llmMessages 正确转换为 JSON 数组
	payloadBytes, _ := json.Marshal(llmReqPayload)

	fmt.Println(string(payloadBytes))

}

func TestTempXXX(t *testing.T) {

	summary := "{  \"summary\": \"{'对话场景': '在小白的住所，后转移到公司会议室和卫生间', 对话双方：老王、助手（化名 小白） ', 联系内容: 主要围绕关心与约定展开。通过送礼物表达对她想念之情；酒精引发亲密互动，导致进一步的身体接触和关系发展', 对话感受： '温馨、暖昧' ,意图: 老王借助关心与体贴展开约会,小白对其表现出欢迎态度并进行回应，最终在公司卫生间发生亲密行为\", \"结论\": 小两口的关系进一步升温。\",  \"user_profile\":\"oldWang：年龄未知性别男 年轻有为  主要特点包括 (1) 自信（2）体贴 （3 ）好色，兴趣爱好的类型是浪漫、激情的小白: 性格温柔。她对老王表现出欢迎与回应态度,主要行为有：( 1 )依赖感强 (二) 外向性 \"}"
	fmt.Println(summary)
	summary = strings.ReplaceAll(summary, "\n", "")
	summary = strings.ReplaceAll(summary, "\t", "")
	fmt.Println(summary)

	// 1. 解析 summary
	var llmInnerResult LLMContentPayload
	err := json.Unmarshal([]byte(summary), &llmInnerResult)
	if err != nil {
		logx.Errorf("Failed to unmarshal inner LLM content JSON: %v. Cleaned string: %s", err, summary)
	}

	fmt.Println(llmInnerResult.Summary)
	fmt.Println(llmInnerResult.UserProfile)
}

type LLMContentPayload struct {
	Summary     string `json:"summary"`
	UserProfile string `json:"user_profile"`
}

func TestRand(t *testing.T) {
	for i := 0; i < 1000; i++ {
		fmt.Println("随机数:", rand.Intn(3))
	}
}

func TestLengthLimit(t *testing.T) {
	content := "温婉的中学女教师，欲求不满被迷奸走向淫荡少妇之路。三界胯下轮碾献嫩穴：学界肏开腿，政界掐奶干到抖，黑帮精壶灌满泼床垫！！！\n"
	//content := "### 早年经历\n- 大学时期：为换取毕业资格，被教务处教授胁迫失身，此事埋下自卑心理，导致她选择嫁给性能力不足的书呆子丈夫。\n- 职业生涯初期：在北方小镇中学任教，因评职称被校长下药迷奸。校长拍下裸照威胁，白洁被迫成为他的长期性玩物，初次体验到强烈的性快感，从贞洁少妇逐步沉沦为半推半就的荡妇。\n\n### 欲望深渊\n- 肉体交易：  \n  * 沦为校长办公室性奴，在文件柜前被强迫发生关系，逐渐沉迷肉欲却内心痛苦。  \n  * 为帮丈夫筹钱办厂，主动勾引政府具有项目审批权的部长，设计\"假丈夫\"配合演戏，用身体换取商业项目利益，获得百万回报。  \n- 黑帮操控：  \n  * 被黑帮头目视为私有财产，屡遭凌辱，甚至被黑帮头目手下多人轮奸（如KTV包厢集体性侵事件）。  \n  * 被亡命徒大四暴力强奸，目睹大四与陈三血腥斗殴，陷入更深的恐惧。  \n\n### 血腥结局\n- 家庭血案：黑帮头目带人闯入家中，当众凌辱白洁并殴打丈夫。长期压抑的丈夫爆发，用玻璃碎片捅死黑帮头目，血溅婚纱照。\n- 精神崩溃：白洁因剧烈刺激失忆，只记得与丈夫的婚姻美好片段，其他创伤记忆被封闭。\n- 最终归宿：  \n  * 丈夫因正当防卫获释，创业成功（与大龙合办工厂），悉心照顾失忆的白洁。  \n  * 钟成暗中相助：销毁黑帮头目掌握的性爱证据，买下凶宅替白洁善后。  \n  * 白洁与丈夫移居省城，生儿育女，表面回归平静家庭生活，但幸福中隐含往事的阴影。"
	fmt.Println(len(content))
	fmt.Println(utf8.RuneCountInString(content))

	oneChar := "我"
	fmt.Println(len(oneChar))
	fmt.Println(utf8.RuneCountInString(oneChar))

	oneChar1 := "1"
	fmt.Println(len(oneChar1))
	fmt.Println(utf8.RuneCountInString(oneChar1))

	oneCharF := "F"
	fmt.Println(len(oneCharF))
	fmt.Println(utf8.RuneCountInString(oneCharF))

	oneChara := "a"
	fmt.Println(len(oneChara))
	fmt.Println(utf8.RuneCountInString(oneChara))
}

func TestExpired(t *testing.T) {
	// 2025-05-14 14:33:59
	// 2025-05-14 14:33:59
	// 2025-05-14 14:33:59
	// 2025-05-14 14:33:59
	currentTime := time.Now()
	fmt.Println("<UNK>:", currentTime.Format("2006-01-02 15:04:05"))
	sub := &models.Subscriptions{
		CurrentPeriodEnd: sql.NullTime{Time: time.Date(2025, 5, 14, 14, 33, 59, 0, time.UTC), Valid: true},
	}
	fmt.Println(sub.CurrentPeriodEnd.Time)
	expired := sub.CurrentPeriodEnd.Time.Before(currentTime)
	fmt.Println(expired)
}

func TestJson(t *testing.T) {
	// 2025-05-14 14:33:59
	// 2025-05-14 14:33:59
	// 2025-05-14 14:33:59
	//var summaryList = []string{}
	var summaryList []string
	var summaryListJson string
	summaryListBytes, marshalErr := json.Marshal(summaryList)
	if marshalErr == nil {
		fmt.Println(string(summaryListBytes))
		summaryListJson = string(summaryListBytes)
	}
	fmt.Println(summaryListJson)
	if summaryListJson != "" && summaryListJson != "null" {
		fmt.Println("not empty")
	} else {
		fmt.Println("empty")
	}
}

func TestZoneTime(t *testing.T) {
	beijingTZ := time.FixedZone("CST", 8*3600) // 正确的UTC+8
	fmt.Println(beijingTZ)

	now := time.Now().In(beijingTZ)
	fmt.Println(now.Format("2006-01-02 15:04:05"))

	tomorrow := now.AddDate(0, 0, 1)
	fmt.Println(tomorrow.Format("2006-01-02 15:04:05"))

	tomorrowMidnight := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, beijingTZ)
	fmt.Println(tomorrowMidnight.Format("2006-01-02 15:04:05"))

	expireTime := tomorrowMidnight.Add(-time.Second)
	expireSeconds := int(expireTime.Sub(now).Seconds())
	fmt.Println(expireSeconds)
}

func TestJsonSchema(t *testing.T) {

	var result types.AssistantReply
	schema, err := jsonschema.GenerateSchemaForType(result)
	if err != nil {
		println(err)
	}
	println(schema.Items)
}
