{"local": {"addresses": ["http://localhost:9200"], "username": "elastic", "password": "your_password_here", "timeout": 30}, "cloud": {"cloud_id": "your_cloud_id_here", "api_key": "your_api_key_here", "timeout": 30}, "ssl": {"addresses": ["https://localhost:9200"], "username": "elastic", "password": "your_password_here", "cert_file": "/path/to/client.crt", "key_file": "/path/to/client.key", "ca_file": "/path/to/ca.crt", "timeout": 30}}