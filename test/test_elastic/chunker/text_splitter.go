package chunker

import (
	"fmt"
	"regexp"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/google/uuid"
	"rag-elastic/models"
)

// TextSplitter 文本分块器
type TextSplitter struct {
	ChunkSize    int
	ChunkOverlap int
}

// NewTextSplitter 创建新的文本分块器
func NewTextSplitter(chunkSize, chunkOverlap int) *TextSplitter {
	return &TextSplitter{
		ChunkSize:    chunkSize,
		ChunkOverlap: chunkOverlap,
	}
}

// SplitText 分割文本为块
func (ts *TextSplitter) SplitText(text string) []string {
	if len(text) == 0 {
		return []string{}
	}

	// 首先尝试按段落分割
	paragraphs := ts.splitByParagraphs(text)
	
	var chunks []string
	var currentChunk strings.Builder
	var currentSize int

	for _, paragraph := range paragraphs {
		paragraphSize := utf8.RuneCountInString(paragraph)
		
		// 如果当前段落太大，需要进一步分割
		if paragraphSize > ts.ChunkSize {
			// 保存当前块（如果有内容）
			if currentSize > 0 {
				chunks = append(chunks, strings.TrimSpace(currentChunk.String()))
				currentChunk.Reset()
				currentSize = 0
			}
			
			// 分割大段落
			subChunks := ts.splitLargeParagraph(paragraph)
			chunks = append(chunks, subChunks...)
			continue
		}
		
		// 检查是否可以添加到当前块
		if currentSize+paragraphSize <= ts.ChunkSize {
			if currentSize > 0 {
				currentChunk.WriteString("\n\n")
				currentSize += 2
			}
			currentChunk.WriteString(paragraph)
			currentSize += paragraphSize
		} else {
			// 保存当前块
			if currentSize > 0 {
				chunks = append(chunks, strings.TrimSpace(currentChunk.String()))
			}
			
			// 开始新块
			currentChunk.Reset()
			currentChunk.WriteString(paragraph)
			currentSize = paragraphSize
		}
	}
	
	// 保存最后一个块
	if currentSize > 0 {
		chunks = append(chunks, strings.TrimSpace(currentChunk.String()))
	}
	
	// 应用重叠
	return ts.applyOverlap(chunks)
}

// splitByParagraphs 按段落分割文本
func (ts *TextSplitter) splitByParagraphs(text string) []string {
	// 按双换行符分割段落
	paragraphs := regexp.MustCompile(`\n\s*\n`).Split(text, -1)
	
	var result []string
	for _, paragraph := range paragraphs {
		paragraph = strings.TrimSpace(paragraph)
		if paragraph != "" {
			result = append(result, paragraph)
		}
	}
	
	return result
}

// splitLargeParagraph 分割大段落
func (ts *TextSplitter) splitLargeParagraph(paragraph string) []string {
	// 首先尝试按句子分割
	sentences := ts.splitBySentences(paragraph)
	
	var chunks []string
	var currentChunk strings.Builder
	var currentSize int
	
	for _, sentence := range sentences {
		sentenceSize := utf8.RuneCountInString(sentence)
		
		// 如果单个句子就超过块大小，按字符强制分割
		if sentenceSize > ts.ChunkSize {
			if currentSize > 0 {
				chunks = append(chunks, strings.TrimSpace(currentChunk.String()))
				currentChunk.Reset()
				currentSize = 0
			}
			
			subChunks := ts.splitByCharacters(sentence)
			chunks = append(chunks, subChunks...)
			continue
		}
		
		// 检查是否可以添加到当前块
		if currentSize+sentenceSize <= ts.ChunkSize {
			if currentSize > 0 {
				currentChunk.WriteString(" ")
				currentSize += 1
			}
			currentChunk.WriteString(sentence)
			currentSize += sentenceSize
		} else {
			// 保存当前块
			if currentSize > 0 {
				chunks = append(chunks, strings.TrimSpace(currentChunk.String()))
			}
			
			// 开始新块
			currentChunk.Reset()
			currentChunk.WriteString(sentence)
			currentSize = sentenceSize
		}
	}
	
	// 保存最后一个块
	if currentSize > 0 {
		chunks = append(chunks, strings.TrimSpace(currentChunk.String()))
	}
	
	return chunks
}

// splitBySentences 按句子分割文本
func (ts *TextSplitter) splitBySentences(text string) []string {
	// 简单的句子分割正则表达式
	sentenceRegex := regexp.MustCompile(`[.!?]+\s+`)
	sentences := sentenceRegex.Split(text, -1)
	
	var result []string
	for _, sentence := range sentences {
		sentence = strings.TrimSpace(sentence)
		if sentence != "" {
			result = append(result, sentence)
		}
	}
	
	return result
}

// splitByCharacters 按字符强制分割
func (ts *TextSplitter) splitByCharacters(text string) []string {
	var chunks []string
	runes := []rune(text)
	
	for i := 0; i < len(runes); i += ts.ChunkSize {
		end := i + ts.ChunkSize
		if end > len(runes) {
			end = len(runes)
		}
		chunk := string(runes[i:end])
		chunks = append(chunks, strings.TrimSpace(chunk))
	}
	
	return chunks
}

// applyOverlap 应用重叠
func (ts *TextSplitter) applyOverlap(chunks []string) []string {
	if len(chunks) <= 1 || ts.ChunkOverlap <= 0 {
		return chunks
	}
	
	var result []string
	
	for i, chunk := range chunks {
		var finalChunk strings.Builder
		
		// 添加前一个块的重叠部分
		if i > 0 {
			prevChunk := chunks[i-1]
			overlap := ts.getOverlapText(prevChunk, ts.ChunkOverlap)
			if overlap != "" {
				finalChunk.WriteString(overlap)
				finalChunk.WriteString(" ")
			}
		}
		
		// 添加当前块
		finalChunk.WriteString(chunk)
		
		result = append(result, strings.TrimSpace(finalChunk.String()))
	}
	
	return result
}

// getOverlapText 获取重叠文本
func (ts *TextSplitter) getOverlapText(text string, overlapSize int) string {
	runes := []rune(text)
	if len(runes) <= overlapSize {
		return text
	}
	
	start := len(runes) - overlapSize
	return string(runes[start:])
}

// SplitDocument 分割文档为块
func (ts *TextSplitter) SplitDocument(doc *models.Document) ([]*models.Chunk, error) {
	textChunks := ts.SplitText(doc.Content)
	
	var chunks []*models.Chunk
	for i, content := range textChunks {
		chunk := &models.Chunk{
			ID:         generateChunkID(doc.ID, i),
			DocumentID: doc.ID,
			Content:    content,
			Position:   i,
			StartChar:  ts.findStartChar(doc.Content, content, i),
			EndChar:    ts.findEndChar(doc.Content, content, i),
			Metadata: map[string]interface{}{
				"document_title":  doc.Title,
				"document_source": doc.Source,
				"chunk_index":     i,
				"total_chunks":    len(textChunks),
			},
			CreatedAt: time.Now(),
		}
		chunks = append(chunks, chunk)
	}
	
	return chunks, nil
}

// generateChunkID 生成块ID
func generateChunkID(documentID string, position int) string {
	return fmt.Sprintf("%s_chunk_%d_%s", documentID, position, uuid.New().String()[:8])
}

// findStartChar 查找块在原文档中的开始字符位置
func (ts *TextSplitter) findStartChar(fullText, chunkText string, position int) int {
	// 简化实现：基于位置估算
	if position == 0 {
		return 0
	}
	
	// 这里可以实现更精确的查找逻辑
	estimatedPos := position * ts.ChunkSize
	if estimatedPos > len(fullText) {
		return len(fullText)
	}
	
	return estimatedPos
}

// findEndChar 查找块在原文档中的结束字符位置
func (ts *TextSplitter) findEndChar(fullText, chunkText string, position int) int {
	startChar := ts.findStartChar(fullText, chunkText, position)
	endChar := startChar + len(chunkText)
	
	if endChar > len(fullText) {
		return len(fullText)
	}
	
	return endChar
}
