package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/elastic/go-elasticsearch/v8/esapi"
)

// CreateDocumentIndex 创建文档索引
func (c *Client) CreateDocumentIndex(ctx context.Context, indexName string) error {
	mapping := `{
		"mappings": {
			"properties": {
				"id": {"type": "keyword"},
				"title": {
					"type": "text",
					"analyzer": "standard",
					"fields": {
						"keyword": {"type": "keyword"}
					}
				},
				"content": {
					"type": "text",
					"analyzer": "standard"
				},
				"source": {"type": "keyword"},
				"source_type": {"type": "keyword"},
				"metadata": {"type": "object"},
				"created_at": {"type": "date"},
				"updated_at": {"type": "date"},
				"size": {"type": "long"},
				"chunk_count": {"type": "integer"},
				"status": {"type": "keyword"}
			}
		},
		"settings": {
			"number_of_shards": 1,
			"number_of_replicas": 0,
			"analysis": {
				"analyzer": {
					"standard": {
						"type": "standard"
					}
				}
			}
		}
	}`

	return c.createIndex(ctx, indexName, mapping)
}

// CreateChunkIndex 创建文档块索引
func (c *Client) CreateChunkIndex(ctx context.Context, indexName string, embeddingDim int) error {
	mapping := fmt.Sprintf(`{
		"mappings": {
			"properties": {
				"id": {"type": "keyword"},
				"document_id": {"type": "keyword"},
				"content": {
					"type": "text",
					"analyzer": "standard"
				},
				"position": {"type": "integer"},
				"start_char": {"type": "integer"},
				"end_char": {"type": "integer"},
				"embedding": {
					"type": "dense_vector",
					"dims": %d,
					"index": true,
					"similarity": "cosine"
				},
				"metadata": {"type": "object"},
				"created_at": {"type": "date"}
			}
		},
		"settings": {
			"number_of_shards": 1,
			"number_of_replicas": 0,
			"analysis": {
				"analyzer": {
					"standard": {
						"type": "standard"
					}
				}
			}
		}
	}`, embeddingDim)

	return c.createIndex(ctx, indexName, mapping)
}

// createIndex 创建索引的通用方法
func (c *Client) createIndex(ctx context.Context, indexName, mapping string) error {
	req := esapi.IndicesCreateRequest{
		Index: indexName,
		Body:  strings.NewReader(mapping),
	}

	res, err := req.Do(ctx, c.client)
	if err != nil {
		return fmt.Errorf("create index failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		// 如果索引已存在，不报错
		if res.StatusCode == 400 {
			var errorResp map[string]interface{}
			if err := c.decodeResponse(res, &errorResp); err == nil {
				if errorInfo, ok := errorResp["error"].(map[string]interface{}); ok {
					if errorType, ok := errorInfo["type"].(string); ok && errorType == "resource_already_exists_exception" {
						return nil // 索引已存在，不报错
					}
				}
			}
		}
		return fmt.Errorf("create index error: %s", res.Status())
	}

	return nil
}

// IndexExists 检查索引是否存在
func (c *Client) IndexExists(ctx context.Context, indexName string) (bool, error) {
	req := esapi.IndicesExistsRequest{
		Index: []string{indexName},
	}

	res, err := req.Do(ctx, c.client)
	if err != nil {
		return false, fmt.Errorf("check index existence failed: %w", err)
	}
	defer res.Body.Close()

	return res.StatusCode == 200, nil
}

// DeleteIndex 删除索引
func (c *Client) DeleteIndex(ctx context.Context, indexName string) error {
	req := esapi.IndicesDeleteRequest{
		Index: []string{indexName},
	}

	res, err := req.Do(ctx, c.client)
	if err != nil {
		return fmt.Errorf("delete index failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() && res.StatusCode != 404 {
		return fmt.Errorf("delete index error: %s", res.Status())
	}

	return nil
}

// RefreshIndex 刷新索引
func (c *Client) RefreshIndex(ctx context.Context, indexName string) error {
	req := esapi.IndicesRefreshRequest{
		Index: []string{indexName},
	}

	res, err := req.Do(ctx, c.client)
	if err != nil {
		return fmt.Errorf("refresh index failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("refresh index error: %s", res.Status())
	}

	return nil
}

// decodeResponse 解码响应
func (c *Client) decodeResponse(res *esapi.Response, target interface{}) error {
	if err := json.NewDecoder(res.Body).Decode(target); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}
	return nil
}
