package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/elastic/go-elasticsearch/v8/esapi"
	"rag-elastic/models"
)

// IndexChunk 索引文档块
func (c *Client) IndexChunk(ctx context.Context, indexName string, chunk *models.Chunk) error {
	chunkJSON, err := json.Marshal(chunk)
	if err != nil {
		return fmt.Errorf("failed to marshal chunk: %w", err)
	}

	req := esapi.IndexRequest{
		Index:      indexName,
		DocumentID: chunk.ID,
		Body:       strings.NewReader(string(chunkJSON)),
		Refresh:    "true",
	}

	res, err := req.Do(ctx, c.client)
	if err != nil {
		return fmt.Errorf("index chunk failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("index chunk error: %s", res.Status())
	}

	return nil
}

// GetChunk 获取文档块
func (c *Client) GetChunk(ctx context.Context, indexName, chunkID string) (*models.Chunk, error) {
	req := esapi.GetRequest{
		Index:      indexName,
		DocumentID: chunkID,
	}

	res, err := req.Do(ctx, c.client)
	if err != nil {
		return nil, fmt.Errorf("get chunk failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		if res.StatusCode == 404 {
			return nil, fmt.Errorf("chunk not found")
		}
		return nil, fmt.Errorf("get chunk error: %s", res.Status())
	}

	var result map[string]interface{}
	if err := c.decodeResponse(res, &result); err != nil {
		return nil, err
	}

	source, ok := result["_source"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid chunk format")
	}

	sourceJSON, err := json.Marshal(source)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal source: %w", err)
	}

	var chunk models.Chunk
	if err := json.Unmarshal(sourceJSON, &chunk); err != nil {
		return nil, fmt.Errorf("failed to unmarshal chunk: %w", err)
	}

	return &chunk, nil
}

// SearchChunks 搜索文档块
func (c *Client) SearchChunks(ctx context.Context, indexName string, query map[string]interface{}) ([]*models.SearchResult, int64, error) {
	queryJSON, err := json.Marshal(query)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to marshal query: %w", err)
	}

	req := esapi.SearchRequest{
		Index: []string{indexName},
		Body:  strings.NewReader(string(queryJSON)),
	}

	res, err := req.Do(ctx, c.client)
	if err != nil {
		return nil, 0, fmt.Errorf("search failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, 0, fmt.Errorf("search error: %s", res.Status())
	}

	var result map[string]interface{}
	if err := c.decodeResponse(res, &result); err != nil {
		return nil, 0, err
	}

	hits, ok := result["hits"].(map[string]interface{})
	if !ok {
		return nil, 0, fmt.Errorf("invalid search result format")
	}

	total, ok := hits["total"].(map[string]interface{})
	if !ok {
		return nil, 0, fmt.Errorf("invalid total format")
	}

	totalValue, ok := total["value"].(float64)
	if !ok {
		return nil, 0, fmt.Errorf("invalid total value format")
	}

	hitsList, ok := hits["hits"].([]interface{})
	if !ok {
		return nil, int64(totalValue), nil
	}

	var searchResults []*models.SearchResult
	for _, hit := range hitsList {
		hitMap, ok := hit.(map[string]interface{})
		if !ok {
			continue
		}

		source, ok := hitMap["_source"].(map[string]interface{})
		if !ok {
			continue
		}

		score, ok := hitMap["_score"].(float64)
		if !ok {
			score = 0.0
		}

		sourceJSON, err := json.Marshal(source)
		if err != nil {
			continue
		}

		var chunk models.Chunk
		if err := json.Unmarshal(sourceJSON, &chunk); err != nil {
			continue
		}

		searchResults = append(searchResults, &models.SearchResult{
			Chunk: &chunk,
			Score: score,
		})
	}

	return searchResults, int64(totalValue), nil
}

// VectorSearch 向量搜索
func (c *Client) VectorSearch(ctx context.Context, indexName string, embedding []float64, topK int, scoreThreshold float64, filters map[string]interface{}) ([]*models.SearchResult, error) {
	query := map[string]interface{}{
		"size": topK,
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					{
						"script_score": map[string]interface{}{
							"query": map[string]interface{}{
								"match_all": map[string]interface{}{},
							},
							"script": map[string]interface{}{
								"source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
								"params": map[string]interface{}{
									"query_vector": embedding,
								},
							},
						},
					},
				},
			},
		},
		"min_score": scoreThreshold,
	}

	// 添加过滤条件
	if len(filters) > 0 {
		boolQuery := query["query"].(map[string]interface{})["bool"].(map[string]interface{})
		boolQuery["filter"] = []map[string]interface{}{}
		
		for field, value := range filters {
			filter := map[string]interface{}{
				"term": map[string]interface{}{
					field: value,
				},
			}
			boolQuery["filter"] = append(boolQuery["filter"].([]map[string]interface{}), filter)
		}
	}

	results, _, err := c.SearchChunks(ctx, indexName, query)
	if err != nil {
		return nil, err
	}

	// 过滤低分结果
	var filteredResults []*models.SearchResult
	for _, result := range results {
		if result.Score >= scoreThreshold {
			filteredResults = append(filteredResults, result)
		}
	}

	return filteredResults, nil
}

// BulkIndexChunks 批量索引文档块
func (c *Client) BulkIndexChunks(ctx context.Context, indexName string, chunks []*models.Chunk) error {
	var bulkBody strings.Builder

	for _, chunk := range chunks {
		// 添加索引操作元数据
		meta := map[string]interface{}{
			"index": map[string]interface{}{
				"_index": indexName,
				"_id":    chunk.ID,
			},
		}
		metaJSON, _ := json.Marshal(meta)
		bulkBody.WriteString(string(metaJSON))
		bulkBody.WriteString("\n")

		// 添加文档块内容
		chunkJSON, _ := json.Marshal(chunk)
		bulkBody.WriteString(string(chunkJSON))
		bulkBody.WriteString("\n")
	}

	req := esapi.BulkRequest{
		Index:   indexName,
		Body:    strings.NewReader(bulkBody.String()),
		Refresh: "true",
	}

	res, err := req.Do(ctx, c.client)
	if err != nil {
		return fmt.Errorf("bulk index chunks failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("bulk index chunks error: %s", res.Status())
	}

	return nil
}

// DeleteChunksByDocumentID 根据文档ID删除所有相关的文档块
func (c *Client) DeleteChunksByDocumentID(ctx context.Context, indexName, documentID string) error {
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"term": map[string]interface{}{
				"document_id": documentID,
			},
		},
	}

	queryJSON, err := json.Marshal(query)
	if err != nil {
		return fmt.Errorf("failed to marshal query: %w", err)
	}

	req := esapi.DeleteByQueryRequest{
		Index: []string{indexName},
		Body:  strings.NewReader(string(queryJSON)),
	}

	res, err := req.Do(ctx, c.client)
	if err != nil {
		return fmt.Errorf("delete chunks by document ID failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("delete chunks by document ID error: %s", res.Status())
	}

	return nil
}
