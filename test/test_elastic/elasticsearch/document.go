package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/elastic/go-elasticsearch/v8/esapi"
	"rag-elastic/models"
)

// IndexDocument 索引文档
func (c *Client) IndexDocument(ctx context.Context, indexName string, doc *models.Document) error {
	docJSON, err := json.Marshal(doc)
	if err != nil {
		return fmt.Errorf("failed to marshal document: %w", err)
	}

	req := esapi.IndexRequest{
		Index:      indexName,
		DocumentID: doc.ID,
		Body:       strings.NewReader(string(docJSON)),
		Refresh:    "true",
	}

	res, err := req.Do(ctx, c.client)
	if err != nil {
		return fmt.Errorf("index document failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("index document error: %s", res.Status())
	}

	return nil
}

// GetDocument 获取文档
func (c *Client) GetDocument(ctx context.Context, indexName, docID string) (*models.Document, error) {
	req := esapi.GetRequest{
		Index:      indexName,
		DocumentID: docID,
	}

	res, err := req.Do(ctx, c.client)
	if err != nil {
		return nil, fmt.Errorf("get document failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		if res.StatusCode == 404 {
			return nil, fmt.Errorf("document not found")
		}
		return nil, fmt.Errorf("get document error: %s", res.Status())
	}

	var result map[string]interface{}
	if err := c.decodeResponse(res, &result); err != nil {
		return nil, err
	}

	source, ok := result["_source"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid document format")
	}

	sourceJSON, err := json.Marshal(source)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal source: %w", err)
	}

	var doc models.Document
	if err := json.Unmarshal(sourceJSON, &doc); err != nil {
		return nil, fmt.Errorf("failed to unmarshal document: %w", err)
	}

	return &doc, nil
}

// UpdateDocument 更新文档
func (c *Client) UpdateDocument(ctx context.Context, indexName, docID string, update map[string]interface{}) error {
	updateBody := map[string]interface{}{
		"doc": update,
	}

	updateJSON, err := json.Marshal(updateBody)
	if err != nil {
		return fmt.Errorf("failed to marshal update: %w", err)
	}

	req := esapi.UpdateRequest{
		Index:      indexName,
		DocumentID: docID,
		Body:       strings.NewReader(string(updateJSON)),
		Refresh:    "true",
	}

	res, err := req.Do(ctx, c.client)
	if err != nil {
		return fmt.Errorf("update document failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("update document error: %s", res.Status())
	}

	return nil
}

// DeleteDocument 删除文档
func (c *Client) DeleteDocument(ctx context.Context, indexName, docID string) error {
	req := esapi.DeleteRequest{
		Index:      indexName,
		DocumentID: docID,
		Refresh:    "true",
	}

	res, err := req.Do(ctx, c.client)
	if err != nil {
		return fmt.Errorf("delete document failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() && res.StatusCode != 404 {
		return fmt.Errorf("delete document error: %s", res.Status())
	}

	return nil
}

// SearchDocuments 搜索文档
func (c *Client) SearchDocuments(ctx context.Context, indexName string, query map[string]interface{}) ([]*models.Document, int64, error) {
	queryJSON, err := json.Marshal(query)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to marshal query: %w", err)
	}

	req := esapi.SearchRequest{
		Index: []string{indexName},
		Body:  strings.NewReader(string(queryJSON)),
	}

	res, err := req.Do(ctx, c.client)
	if err != nil {
		return nil, 0, fmt.Errorf("search failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, 0, fmt.Errorf("search error: %s", res.Status())
	}

	var result map[string]interface{}
	if err := c.decodeResponse(res, &result); err != nil {
		return nil, 0, err
	}

	hits, ok := result["hits"].(map[string]interface{})
	if !ok {
		return nil, 0, fmt.Errorf("invalid search result format")
	}

	total, ok := hits["total"].(map[string]interface{})
	if !ok {
		return nil, 0, fmt.Errorf("invalid total format")
	}

	totalValue, ok := total["value"].(float64)
	if !ok {
		return nil, 0, fmt.Errorf("invalid total value format")
	}

	hitsList, ok := hits["hits"].([]interface{})
	if !ok {
		return nil, int64(totalValue), nil
	}

	var documents []*models.Document
	for _, hit := range hitsList {
		hitMap, ok := hit.(map[string]interface{})
		if !ok {
			continue
		}

		source, ok := hitMap["_source"].(map[string]interface{})
		if !ok {
			continue
		}

		sourceJSON, err := json.Marshal(source)
		if err != nil {
			continue
		}

		var doc models.Document
		if err := json.Unmarshal(sourceJSON, &doc); err != nil {
			continue
		}

		documents = append(documents, &doc)
	}

	return documents, int64(totalValue), nil
}

// BulkIndexDocuments 批量索引文档
func (c *Client) BulkIndexDocuments(ctx context.Context, indexName string, documents []*models.Document) error {
	var bulkBody strings.Builder

	for _, doc := range documents {
		// 添加索引操作元数据
		meta := map[string]interface{}{
			"index": map[string]interface{}{
				"_index": indexName,
				"_id":    doc.ID,
			},
		}
		metaJSON, _ := json.Marshal(meta)
		bulkBody.WriteString(string(metaJSON))
		bulkBody.WriteString("\n")

		// 添加文档内容
		docJSON, _ := json.Marshal(doc)
		bulkBody.WriteString(string(docJSON))
		bulkBody.WriteString("\n")
	}

	req := esapi.BulkRequest{
		Index:   indexName,
		Body:    strings.NewReader(bulkBody.String()),
		Refresh: "true",
	}

	res, err := req.Do(ctx, c.client)
	if err != nil {
		return fmt.Errorf("bulk index failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("bulk index error: %s", res.Status())
	}

	return nil
}
