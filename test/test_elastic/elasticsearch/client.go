package elasticsearch

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"time"

	"github.com/elastic/go-elasticsearch/v8"
	"rag-elastic/config"
)

// Client Elasticsearch 客户端封装
type Client struct {
	client *elasticsearch.Client
	config *config.ElasticsearchConfig
}

// NewClient 创建新的 Elasticsearch 客户端
func NewClient(cfg *config.ElasticsearchConfig) (*Client, error) {
	esConfig := elasticsearch.Config{
		Addresses: cfg.Addresses,
		Username:  cfg.Username,
		Password:  cfg.Password,
		APIKey:    cfg.APIKey,
		CloudID:   cfg.CloudID,
	}

	// 设置超时和 TLS 配置
	if cfg.Timeout > 0 {
		esConfig.Transport = &http.Transport{
			ResponseHeaderTimeout: time.Duration(cfg.Timeout) * time.Second,
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: false, // 生产环境应该设置为 false
			},
		}
	}

	client, err := elasticsearch.NewClient(esConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create elasticsearch client: %w", err)
	}

	return &Client{
		client: client,
		config: cfg,
	}, nil
}

// Ping 测试连接
func (c *Client) Ping(ctx context.Context) error {
	res, err := c.client.Ping(
		c.client.Ping.WithContext(ctx),
	)
	if err != nil {
		return fmt.Errorf("ping failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("ping error: %s", res.Status())
	}

	return nil
}

// GetClient 获取原始 Elasticsearch 客户端
func (c *Client) GetClient() *elasticsearch.Client {
	return c.client
}

// Health 检查集群健康状态
func (c *Client) Health(ctx context.Context) (map[string]interface{}, error) {
	res, err := c.client.Cluster.Health(
		c.client.Cluster.Health.WithContext(ctx),
	)
	if err != nil {
		return nil, fmt.Errorf("health check failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("health check error: %s", res.Status())
	}

	var health map[string]interface{}
	if err := c.decodeResponse(res, &health); err != nil {
		return nil, err
	}

	return health, nil
}

// Info 获取集群信息
func (c *Client) Info(ctx context.Context) (map[string]interface{}, error) {
	res, err := c.client.Info(
		c.client.Info.WithContext(ctx),
	)
	if err != nil {
		return nil, fmt.Errorf("get info failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("get info error: %s", res.Status())
	}

	var info map[string]interface{}
	if err := c.decodeResponse(res, &info); err != nil {
		return nil, err
	}

	return info, nil
}
