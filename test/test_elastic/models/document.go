package models

import (
	"time"
)

// Document 文档模型
type Document struct {
	ID          string            `json:"id"`
	Title       string            `json:"title"`
	Content     string            `json:"content"`
	Source      string            `json:"source"`
	SourceType  string            `json:"source_type"` // file, url, text
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
	Size        int64             `json:"size"`
	ChunkCount  int               `json:"chunk_count"`
	Status      string            `json:"status"` // pending, processing, completed, failed
}

// Chunk 文档块模型
type Chunk struct {
	ID         string                 `json:"id"`
	DocumentID string                 `json:"document_id"`
	Content    string                 `json:"content"`
	Position   int                    `json:"position"`
	StartChar  int                    `json:"start_char"`
	EndChar    int                    `json:"end_char"`
	Embedding  []float64              `json:"embedding,omitempty"`
	Metadata   map[string]interface{} `json:"metadata"`
	CreatedAt  time.Time              `json:"created_at"`
}

// SearchResult 搜索结果
type SearchResult struct {
	Chunk *Chunk  `json:"chunk"`
	Score float64 `json:"score"`
}

// SearchRequest 搜索请求
type SearchRequest struct {
	Query          string            `json:"query"`
	TopK           int               `json:"top_k"`
	ScoreThreshold float64           `json:"score_threshold"`
	Filters        map[string]interface{} `json:"filters,omitempty"`
	DocumentIDs    []string          `json:"document_ids,omitempty"`
}

// SearchResponse 搜索响应
type SearchResponse struct {
	Results   []*SearchResult `json:"results"`
	Total     int64           `json:"total"`
	TimeTaken string          `json:"time_taken"`
}

// RAGRequest RAG 请求
type RAGRequest struct {
	Question       string            `json:"question"`
	TopK           int               `json:"top_k"`
	ScoreThreshold float64           `json:"score_threshold"`
	DocumentIDs    []string          `json:"document_ids,omitempty"`
	Filters        map[string]interface{} `json:"filters,omitempty"`
	IncludeSource  bool              `json:"include_source"`
}

// RAGResponse RAG 响应
type RAGResponse struct {
	Answer        string          `json:"answer"`
	Sources       []*SearchResult `json:"sources"`
	Question      string          `json:"question"`
	TimeTaken     string          `json:"time_taken"`
	TokensUsed    int             `json:"tokens_used,omitempty"`
}

// DocumentStatus 文档状态常量
const (
	DocumentStatusPending    = "pending"
	DocumentStatusProcessing = "processing"
	DocumentStatusCompleted  = "completed"
	DocumentStatusFailed     = "failed"
)

// SourceType 来源类型常量
const (
	SourceTypeFile = "file"
	SourceTypeURL  = "url"
	SourceTypeText = "text"
)
