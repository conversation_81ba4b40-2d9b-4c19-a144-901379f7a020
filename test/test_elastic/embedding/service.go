package embedding

import (
	"context"
	"crypto/md5"
	"math"
	"math/rand"
	"strings"
	"time"
)

// Service 嵌入服务接口
type Service interface {
	GetEmbedding(ctx context.Context, text string) ([]float64, error)
	GetEmbeddings(ctx context.Context, texts []string) ([][]float64, error)
	GetDimension() int
}

// MockEmbeddingService 模拟嵌入服务
type MockEmbeddingService struct {
	dimension int
	model     string
}

// NewMockEmbeddingService 创建模拟嵌入服务
func NewMockEmbeddingService(dimension int, model string) *MockEmbeddingService {
	return &MockEmbeddingService{
		dimension: dimension,
		model:     model,
	}
}

// GetEmbedding 获取单个文本的嵌入向量
func (s *MockEmbeddingService) GetEmbedding(ctx context.Context, text string) ([]float64, error) {
	// 模拟网络延迟
	time.Sleep(10 * time.Millisecond)

	// 基于文本内容生成确定性的向量
	return s.generateDeterministicEmbedding(text), nil
}

// GetEmbeddings 获取多个文本的嵌入向量
func (s *MockEmbeddingService) GetEmbeddings(ctx context.Context, texts []string) ([][]float64, error) {
	// 模拟批量处理延迟
	time.Sleep(time.Duration(len(texts)*5) * time.Millisecond)

	var embeddings [][]float64
	for _, text := range texts {
		embedding := s.generateDeterministicEmbedding(text)
		embeddings = append(embeddings, embedding)
	}

	return embeddings, nil
}

// GetDimension 获取向量维度
func (s *MockEmbeddingService) GetDimension() int {
	return s.dimension
}

// generateDeterministicEmbedding 生成确定性的嵌入向量
func (s *MockEmbeddingService) generateDeterministicEmbedding(text string) []float64 {
	// 使用文本的 MD5 哈希作为种子，确保相同文本生成相同向量
	hash := md5.Sum([]byte(text))
	seed := int64(0)
	for i := 0; i < 8; i++ {
		seed = seed<<8 + int64(hash[i])
	}

	rng := rand.New(rand.NewSource(seed))

	// 生成随机向量
	embedding := make([]float64, s.dimension)
	for i := 0; i < s.dimension; i++ {
		embedding[i] = rng.NormFloat64()
	}

	// 添加一些基于文本特征的偏置
	s.addTextFeatures(embedding, text)

	// 归一化向量
	return s.normalizeVector(embedding)
}

// addTextFeatures 基于文本特征调整向量
func (s *MockEmbeddingService) addTextFeatures(embedding []float64, text string) {
	text = strings.ToLower(text)

	// 基于文本长度的特征
	lengthFeature := math.Log(float64(len(text)+1)) / 10.0
	if len(embedding) > 0 {
		embedding[0] += lengthFeature
	}

	// 基于单词数量的特征
	wordCount := len(strings.Fields(text))
	wordFeature := math.Log(float64(wordCount+1)) / 5.0
	if len(embedding) > 1 {
		embedding[1] += wordFeature
	}

	// 基于特定关键词的特征
	keywords := []string{"技术", "科学", "商业", "教育", "健康", "娱乐"}
	for i, keyword := range keywords {
		if strings.Contains(text, keyword) && i+2 < len(embedding) {
			embedding[i+2] += 0.5
		}
	}

	// 基于标点符号密度的特征
	punctCount := strings.Count(text, ".") + strings.Count(text, "!") + strings.Count(text, "?")
	punctFeature := float64(punctCount) / float64(len(text)+1)
	if len(embedding) > 10 {
		embedding[10] += punctFeature
	}
}

// normalizeVector 归一化向量
func (s *MockEmbeddingService) normalizeVector(vector []float64) []float64 {
	var norm float64
	for _, v := range vector {
		norm += v * v
	}
	norm = math.Sqrt(norm)

	if norm == 0 {
		return vector
	}

	normalized := make([]float64, len(vector))
	for i, v := range vector {
		normalized[i] = v / norm
	}

	return normalized
}

// OpenAIEmbeddingService OpenAI 嵌入服务（示例实现）
type OpenAIEmbeddingService struct {
	apiKey    string
	model     string
	dimension int
}

// NewOpenAIEmbeddingService 创建 OpenAI 嵌入服务
func NewOpenAIEmbeddingService(apiKey, model string, dimension int) *OpenAIEmbeddingService {
	return &OpenAIEmbeddingService{
		apiKey:    apiKey,
		model:     model,
		dimension: dimension,
	}
}

// GetEmbedding 获取单个文本的嵌入向量
func (s *OpenAIEmbeddingService) GetEmbedding(ctx context.Context, text string) ([]float64, error) {
	// TODO: 实现真实的 OpenAI API 调用
	// 这里暂时返回模拟数据
	mockService := NewMockEmbeddingService(s.dimension, s.model)
	return mockService.GetEmbedding(ctx, text)
}

// GetEmbeddings 获取多个文本的嵌入向量
func (s *OpenAIEmbeddingService) GetEmbeddings(ctx context.Context, texts []string) ([][]float64, error) {
	// TODO: 实现真实的 OpenAI API 调用
	// 这里暂时返回模拟数据
	mockService := NewMockEmbeddingService(s.dimension, s.model)
	return mockService.GetEmbeddings(ctx, texts)
}

// GetDimension 获取向量维度
func (s *OpenAIEmbeddingService) GetDimension() int {
	return s.dimension
}

// HuggingFaceEmbeddingService Hugging Face 嵌入服务（示例实现）
type HuggingFaceEmbeddingService struct {
	apiKey    string
	model     string
	dimension int
}

// NewHuggingFaceEmbeddingService 创建 Hugging Face 嵌入服务
func NewHuggingFaceEmbeddingService(apiKey, model string, dimension int) *HuggingFaceEmbeddingService {
	return &HuggingFaceEmbeddingService{
		apiKey:    apiKey,
		model:     model,
		dimension: dimension,
	}
}

// GetEmbedding 获取单个文本的嵌入向量
func (s *HuggingFaceEmbeddingService) GetEmbedding(ctx context.Context, text string) ([]float64, error) {
	// TODO: 实现真实的 Hugging Face API 调用
	// 这里暂时返回模拟数据
	mockService := NewMockEmbeddingService(s.dimension, s.model)
	return mockService.GetEmbedding(ctx, text)
}

// GetEmbeddings 获取多个文本的嵌入向量
func (s *HuggingFaceEmbeddingService) GetEmbeddings(ctx context.Context, texts []string) ([][]float64, error) {
	// TODO: 实现真实的 Hugging Face API 调用
	// 这里暂时返回模拟数据
	mockService := NewMockEmbeddingService(s.dimension, s.model)
	return mockService.GetEmbeddings(ctx, texts)
}

// GetDimension 获取向量维度
func (s *HuggingFaceEmbeddingService) GetDimension() int {
	return s.dimension
}

// CreateEmbeddingService 创建嵌入服务工厂函数
func CreateEmbeddingService(serviceType, apiKey, model string, dimension int) Service {
	switch serviceType {
	case "openai":
		return NewOpenAIEmbeddingService(apiKey, model, dimension)
	case "huggingface":
		return NewHuggingFaceEmbeddingService(apiKey, model, dimension)
	case "mock":
		fallthrough
	default:
		return NewMockEmbeddingService(dimension, model)
	}
}
