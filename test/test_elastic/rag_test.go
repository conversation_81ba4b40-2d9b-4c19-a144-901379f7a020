package main

import (
	"context"
	"log"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"rag-elastic/config"
	"rag-elastic/models"
	"rag-elastic/rag"
)

func TestRAGService(t *testing.T) {
	// 加载配置
	cfg := config.LoadConfig()

	// 创建 RAG 服务
	ragService, err := rag.NewService(cfg)
	require.NoError(t, err)

	ctx := context.Background()

	// 初始化服务
	err = ragService.Initialize(ctx)
	if err != nil {
		t.Skipf("Failed to initialize RAG service (Elasticsearch may not be available): %v", err)
		return
	}

	// 测试健康检查
	t.Run("Health Check", func(t *testing.T) {
		health, err := ragService.GetHealth(ctx)
		require.NoError(t, err)
		assert.Equal(t, "healthy", health["status"])
		log.Printf("Health status: %+v", health)
	})

	// 测试添加文档
	var testDoc *models.Document
	t.Run("Add Document", func(t *testing.T) {
		title := "测试文档"
		content := `这是一个测试文档。它包含了多个段落来测试文本分块功能。

第一段：人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。这些任务包括学习、推理、问题解决、感知和语言理解。

第二段：机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。通过算法和统计模型，机器学习系统可以从数据中识别模式并做出预测。

第三段：深度学习是机器学习的一个更专门的子集，它使用人工神经网络来模拟人脑的工作方式。这种方法在图像识别、自然语言处理和语音识别等领域取得了显著成功。

第四段：自然语言处理（NLP）是人工智能的另一个重要分支，专注于使计算机能够理解、解释和生成人类语言。NLP 技术被广泛应用于搜索引擎、翻译服务和聊天机器人中。`

		source := "test_source"
		sourceType := models.SourceTypeText
		metadata := map[string]interface{}{
			"author":   "测试作者",
			"category": "技术",
			"tags":     []string{"AI", "机器学习", "深度学习"},
		}

		doc, err := ragService.AddDocument(ctx, title, content, source, sourceType, metadata)
		require.NoError(t, err)
		assert.NotEmpty(t, doc.ID)
		assert.Equal(t, title, doc.Title)
		assert.Equal(t, content, doc.Content)
		assert.Equal(t, models.DocumentStatusProcessing, doc.Status)

		testDoc = doc
		log.Printf("Added document: %s", doc.ID)

		// 等待文档处理完成
		time.Sleep(3 * time.Second)
	})

	// 测试获取文档
	t.Run("Get Document", func(t *testing.T) {
		require.NotNil(t, testDoc)

		doc, err := ragService.GetDocument(ctx, testDoc.ID)
		require.NoError(t, err)
		assert.Equal(t, testDoc.ID, doc.ID)
		assert.Equal(t, testDoc.Title, doc.Title)
		// 状态可能已经更新为 completed
		log.Printf("Document status: %s, chunks: %d", doc.Status, doc.ChunkCount)
	})

	// 测试搜索
	t.Run("Search", func(t *testing.T) {
		require.NotNil(t, testDoc)

		// 等待索引刷新
		time.Sleep(2 * time.Second)

		searchReq := &models.SearchRequest{
			Query:          "人工智能",
			TopK:           5,
			ScoreThreshold: 0.1,
		}

		response, err := ragService.Search(ctx, searchReq)
		require.NoError(t, err)
		assert.Greater(t, len(response.Results), 0)
		assert.Greater(t, response.Total, int64(0))

		log.Printf("Search results: %d, time taken: %s", len(response.Results), response.TimeTaken)
		for i, result := range response.Results {
			log.Printf("Result %d: score=%.4f, content=%s...",
				i+1, result.Score, result.Chunk.Content[:min(50, len(result.Chunk.Content))])
		}
	})

	// 测试特定查询
	t.Run("Specific Search", func(t *testing.T) {
		require.NotNil(t, testDoc)

		searchReq := &models.SearchRequest{
			Query:          "深度学习神经网络",
			TopK:           3,
			ScoreThreshold: 0.1,
		}

		response, err := ragService.Search(ctx, searchReq)
		require.NoError(t, err)

		log.Printf("Specific search results: %d", len(response.Results))
		for i, result := range response.Results {
			log.Printf("Result %d: score=%.4f, document_id=%s",
				i+1, result.Score, result.Chunk.DocumentID)
		}
	})

	// 测试文档ID过滤
	t.Run("Search with Document Filter", func(t *testing.T) {
		require.NotNil(t, testDoc)

		searchReq := &models.SearchRequest{
			Query:       "机器学习",
			TopK:        5,
			DocumentIDs: []string{testDoc.ID},
		}

		response, err := ragService.Search(ctx, searchReq)
		require.NoError(t, err)

		// 所有结果都应该来自指定的文档
		for _, result := range response.Results {
			assert.Equal(t, testDoc.ID, result.Chunk.DocumentID)
		}

		log.Printf("Filtered search results: %d", len(response.Results))
	})

	// 测试列出文档
	t.Run("List Documents", func(t *testing.T) {
		docs, total, err := ragService.ListDocuments(ctx, 10, 0)
		require.NoError(t, err)
		assert.Greater(t, total, int64(0))
		assert.Greater(t, len(docs), 0)

		log.Printf("Total documents: %d, returned: %d", total, len(docs))
		for i, doc := range docs {
			log.Printf("Document %d: %s (%s)", i+1, doc.Title, doc.Status)
		}
	})

	// 测试删除文档
	t.Run("Delete Document", func(t *testing.T) {
		require.NotNil(t, testDoc)

		err := ragService.DeleteDocument(ctx, testDoc.ID)
		require.NoError(t, err)

		// 验证文档已被删除
		_, err = ragService.GetDocument(ctx, testDoc.ID)
		assert.Error(t, err)

		log.Printf("Document %s deleted successfully", testDoc.ID)
	})
}

func TestRAGServiceBatch(t *testing.T) {
	// 加载配置
	cfg := config.LoadConfig()

	// 创建 RAG 服务
	ragService, err := rag.NewService(cfg)
	require.NoError(t, err)

	ctx := context.Background()

	// 初始化服务
	err = ragService.Initialize(ctx)
	if err != nil {
		t.Skipf("Failed to initialize RAG service (Elasticsearch may not be available): %v", err)
		return
	}

	// 批量添加文档
	t.Run("Batch Add Documents", func(t *testing.T) {
		documents := []struct {
			title   string
			content string
		}{
			{
				"Go 编程语言",
				"Go 是由 Google 开发的开源编程语言。它具有简洁的语法、高效的并发处理能力和快速的编译速度。Go 特别适合构建网络服务、分布式系统和云原生应用。",
			},
			{
				"Python 数据科学",
				"Python 是数据科学领域最受欢迎的编程语言之一。它拥有丰富的库生态系统，如 NumPy、Pandas、Scikit-learn 和 TensorFlow，使得数据分析和机器学习变得更加容易。",
			},
			{
				"JavaScript 前端开发",
				"JavaScript 是 Web 开发的核心语言。现代 JavaScript 框架如 React、Vue 和 Angular 使得构建复杂的单页应用变得更加简单。Node.js 的出现也让 JavaScript 可以用于后端开发。",
			},
		}

		var docIDs []string
		for _, docData := range documents {
			doc, err := ragService.AddDocument(
				ctx,
				docData.title,
				docData.content,
				"batch_test",
				models.SourceTypeText,
				map[string]interface{}{"batch": true},
			)
			require.NoError(t, err)
			docIDs = append(docIDs, doc.ID)
		}

		// 等待处理完成
		time.Sleep(5 * time.Second)

		// 测试跨文档搜索
		searchReq := &models.SearchRequest{
			Query:          "编程语言",
			TopK:           10,
			ScoreThreshold: 0.1,
		}

		response, err := ragService.Search(ctx, searchReq)
		require.NoError(t, err)
		assert.Greater(t, len(response.Results), 0)

		log.Printf("Cross-document search results: %d", len(response.Results))

		// 统计来自不同文档的结果
		docCount := make(map[string]int)
		for _, result := range response.Results {
			docCount[result.Chunk.DocumentID]++
		}

		log.Printf("Results from %d different documents", len(docCount))
		for docID, count := range docCount {
			log.Printf("Document %s: %d results", docID[:8], count)
		}

		// 清理测试文档
		for _, docID := range docIDs {
			err := ragService.DeleteDocument(ctx, docID)
			require.NoError(t, err)
		}
	})
}

// 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// 基准测试
func BenchmarkRAGSearch(b *testing.B) {
	cfg := config.LoadConfig()
	ragService, err := rag.NewService(cfg)
	if err != nil {
		b.Skipf("Failed to create RAG service: %v", err)
		return
	}

	ctx := context.Background()
	if err := ragService.Initialize(ctx); err != nil {
		b.Skipf("Failed to initialize RAG service: %v", err)
		return
	}

	// 添加测试文档
	doc, err := ragService.AddDocument(
		ctx,
		"基准测试文档",
		"这是一个用于基准测试的文档。它包含了足够的内容来测试搜索性能。人工智能、机器学习、深度学习、自然语言处理等技术正在快速发展。",
		"benchmark",
		models.SourceTypeText,
		map[string]interface{}{"benchmark": true},
	)
	if err != nil {
		b.Fatalf("Failed to add document: %v", err)
	}

	// 等待处理完成
	time.Sleep(2 * time.Second)

	searchReq := &models.SearchRequest{
		Query:          "人工智能",
		TopK:           5,
		ScoreThreshold: 0.1,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := ragService.Search(ctx, searchReq)
		if err != nil {
			b.Fatalf("Search failed: %v", err)
		}
	}

	// 清理
	ragService.DeleteDocument(ctx, doc.ID)
}
