package main

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/elastic/go-elasticsearch/v8"
	"github.com/elastic/go-elasticsearch/v8/esapi"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// ESConfig Elasticsearch 配置
type ESConfig struct {
	Addresses []string `json:"addresses"`
	Username  string   `json:"username"`
	Password  string   `json:"password"`
	APIKey    string   `json:"api_key"`
	CertFile  string   `json:"cert_file"`
	KeyFile   string   `json:"key_file"`
	CAFile    string   `json:"ca_file"`
	CloudID   string   `json:"cloud_id"`
	Timeout   int      `json:"timeout"` // 秒
}

// ESClient Elasticsearch 客户端封装
type ESClient struct {
	client *elasticsearch.Client
	config *ESConfig
}

// NewESClient 创建新的 Elasticsearch 客户端
func NewESClient(config *ESConfig) (*ESClient, error) {
	cfg := elasticsearch.Config{
		Addresses: config.Addresses,
		Username:  config.Username,
		Password:  config.Password,
		APIKey:    config.APIKey,
		CloudID:   config.CloudID,
	}

	// 设置超时
	if config.Timeout > 0 {
		cfg.Transport = &http.Transport{
			ResponseHeaderTimeout: time.Duration(config.Timeout) * time.Second,
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true, // 仅用于测试，生产环境请设置为 false
			},
		}
	}

	// 如果提供了证书文件
	if config.CertFile != "" && config.KeyFile != "" {
		cert, err := tls.LoadX509KeyPair(config.CertFile, config.KeyFile)
		if err != nil {
			return nil, fmt.Errorf("failed to load client certificates: %w", err)
		}
		cfg.Transport = &http.Transport{
			TLSClientConfig: &tls.Config{
				Certificates: []tls.Certificate{cert},
			},
		}
	}

	client, err := elasticsearch.NewClient(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create elasticsearch client: %w", err)
	}

	return &ESClient{
		client: client,
		config: config,
	}, nil
}

// Ping 测试连接
func (es *ESClient) Ping(ctx context.Context) error {
	res, err := es.client.Ping(
		es.client.Ping.WithContext(ctx),
	)
	if err != nil {
		return fmt.Errorf("ping failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("ping error: %s", res.Status())
	}

	return nil
}

// GetInfo 获取集群信息
func (es *ESClient) GetInfo(ctx context.Context) (map[string]interface{}, error) {
	res, err := es.client.Info(
		es.client.Info.WithContext(ctx),
	)
	if err != nil {
		return nil, fmt.Errorf("get info failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("get info error: %s", res.Status())
	}

	var info map[string]interface{}
	if err := json.NewDecoder(res.Body).Decode(&info); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return info, nil
}

// CreateIndex 创建索引
func (es *ESClient) CreateIndex(ctx context.Context, indexName string, mapping string) error {
	req := esapi.IndicesCreateRequest{
		Index: indexName,
		Body:  strings.NewReader(mapping),
	}

	res, err := req.Do(ctx, es.client)
	if err != nil {
		return fmt.Errorf("create index failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("create index error: %s", res.Status())
	}

	return nil
}

// IndexDocument 索引文档
func (es *ESClient) IndexDocument(ctx context.Context, indexName, docID string, document interface{}) error {
	docJSON, err := json.Marshal(document)
	if err != nil {
		return fmt.Errorf("failed to marshal document: %w", err)
	}

	req := esapi.IndexRequest{
		Index:      indexName,
		DocumentID: docID,
		Body:       strings.NewReader(string(docJSON)),
		Refresh:    "true",
	}

	res, err := req.Do(ctx, es.client)
	if err != nil {
		return fmt.Errorf("index document failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("index document error: %s", res.Status())
	}

	return nil
}

// SearchDocuments 搜索文档
func (es *ESClient) SearchDocuments(ctx context.Context, indexName string, query map[string]interface{}) (map[string]interface{}, error) {
	queryJSON, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal query: %w", err)
	}

	req := esapi.SearchRequest{
		Index: []string{indexName},
		Body:  strings.NewReader(string(queryJSON)),
	}

	res, err := req.Do(ctx, es.client)
	if err != nil {
		return nil, fmt.Errorf("search failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("search error: %s", res.Status())
	}

	var result map[string]interface{}
	if err := json.NewDecoder(res.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode search response: %w", err)
	}

	return result, nil
}

// TestESConnection 测试 Elasticsearch 连接
func TestESConnection(t *testing.T) {
	// 配置 Elasticsearch 连接
	config := &ESConfig{
		Addresses: []string{"http://localhost:9200"},
		Username:  "elastic",
		Password:  "your_password", // 替换为实际密码
		Timeout:   30,
	}

	// 创建客户端
	client, err := NewESClient(config)
	require.NoError(t, err)

	ctx := context.Background()

	// 测试连接
	err = client.Ping(ctx)
	if err != nil {
		t.Skipf("Elasticsearch not available: %v", err)
		return
	}

	// 获取集群信息
	info, err := client.GetInfo(ctx)
	require.NoError(t, err)
	assert.NotEmpty(t, info)

	log.Printf("Elasticsearch Info: %+v", info)
}

// TestESCloudConnection 测试 Elasticsearch Cloud 连接
func TestESCloudConnection(t *testing.T) {
	// 使用 Cloud ID 和 API Key 连接
	config := &ESConfig{
		CloudID: "your_cloud_id", // 替换为实际的 Cloud ID
		APIKey:  "your_api_key",  // 替换为实际的 API Key
		Timeout: 30,
	}

	client, err := NewESClient(config)
	require.NoError(t, err)

	ctx := context.Background()

	// 测试连接
	err = client.Ping(ctx)
	if err != nil {
		t.Skipf("Elasticsearch Cloud not available: %v", err)
		return
	}

	// 获取集群信息
	info, err := client.GetInfo(ctx)
	require.NoError(t, err)
	assert.NotEmpty(t, info)

	log.Printf("Elasticsearch Cloud Info: %+v", info)
}

// TestESIndexOperations 测试索引操作
func TestESIndexOperations(t *testing.T) {
	config := &ESConfig{
		Addresses: []string{"http://localhost:9200"},
		Username:  "elastic",
		Password:  "your_password",
		Timeout:   30,
	}

	client, err := NewESClient(config)
	require.NoError(t, err)

	ctx := context.Background()

	// 测试连接
	err = client.Ping(ctx)
	if err != nil {
		t.Skipf("Elasticsearch not available: %v", err)
		return
	}

	indexName := "test_index"

	// 创建索引映射
	mapping := `{
		"mappings": {
			"properties": {
				"title": {
					"type": "text",
					"analyzer": "standard"
				},
				"content": {
					"type": "text",
					"analyzer": "standard"
				},
				"created_at": {
					"type": "date"
				},
				"tags": {
					"type": "keyword"
				}
			}
		}
	}`

	// 创建索引
	err = client.CreateIndex(ctx, indexName, mapping)
	if err != nil && !strings.Contains(err.Error(), "resource_already_exists_exception") {
		require.NoError(t, err)
	}

	// 准备测试文档
	testDoc := map[string]interface{}{
		"title":      "Test Document",
		"content":    "This is a test document for Elasticsearch",
		"created_at": time.Now().Format(time.RFC3339),
		"tags":       []string{"test", "elasticsearch", "go"},
	}

	// 索引文档
	err = client.IndexDocument(ctx, indexName, "1", testDoc)
	require.NoError(t, err)

	// 等待索引刷新
	time.Sleep(1 * time.Second)

	// 搜索文档
	searchQuery := map[string]interface{}{
		"query": map[string]interface{}{
			"match": map[string]interface{}{
				"title": "Test",
			},
		},
	}

	result, err := client.SearchDocuments(ctx, indexName, searchQuery)
	require.NoError(t, err)
	assert.NotEmpty(t, result)

	// 验证搜索结果
	hits, ok := result["hits"].(map[string]interface{})
	require.True(t, ok)

	total, ok := hits["total"].(map[string]interface{})
	require.True(t, ok)

	value, ok := total["value"].(float64)
	require.True(t, ok)
	assert.Greater(t, value, float64(0))

	log.Printf("Search result: %+v", result)
}

// BulkIndexDocuments 批量索引文档
func (es *ESClient) BulkIndexDocuments(ctx context.Context, indexName string, documents []map[string]interface{}) error {
	var bulkBody strings.Builder

	for i, doc := range documents {
		// 添加索引操作元数据
		meta := map[string]interface{}{
			"index": map[string]interface{}{
				"_index": indexName,
				"_id":    fmt.Sprintf("bulk_%d", i),
			},
		}
		metaJSON, _ := json.Marshal(meta)
		bulkBody.WriteString(string(metaJSON))
		bulkBody.WriteString("\n")

		// 添加文档内容
		docJSON, _ := json.Marshal(doc)
		bulkBody.WriteString(string(docJSON))
		bulkBody.WriteString("\n")
	}

	req := esapi.BulkRequest{
		Index: indexName,
		Body:  strings.NewReader(bulkBody.String()),
	}

	res, err := req.Do(ctx, es.client)
	if err != nil {
		return fmt.Errorf("bulk index failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("bulk index error: %s", res.Status())
	}

	return nil
}

// DeleteDocument 删除文档
func (es *ESClient) DeleteDocument(ctx context.Context, indexName, docID string) error {
	req := esapi.DeleteRequest{
		Index:      indexName,
		DocumentID: docID,
	}

	res, err := req.Do(ctx, es.client)
	if err != nil {
		return fmt.Errorf("delete document failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() && res.StatusCode != 404 {
		return fmt.Errorf("delete document error: %s", res.Status())
	}

	return nil
}

// UpdateDocument 更新文档
func (es *ESClient) UpdateDocument(ctx context.Context, indexName, docID string, update map[string]interface{}) error {
	updateBody := map[string]interface{}{
		"doc": update,
	}

	updateJSON, err := json.Marshal(updateBody)
	if err != nil {
		return fmt.Errorf("failed to marshal update: %w", err)
	}

	req := esapi.UpdateRequest{
		Index:      indexName,
		DocumentID: docID,
		Body:       strings.NewReader(string(updateJSON)),
	}

	res, err := req.Do(ctx, es.client)
	if err != nil {
		return fmt.Errorf("update document failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("update document error: %s", res.Status())
	}

	return nil
}

// GetDocument 获取文档
func (es *ESClient) GetDocument(ctx context.Context, indexName, docID string) (map[string]interface{}, error) {
	req := esapi.GetRequest{
		Index:      indexName,
		DocumentID: docID,
	}

	res, err := req.Do(ctx, es.client)
	if err != nil {
		return nil, fmt.Errorf("get document failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		if res.StatusCode == 404 {
			return nil, fmt.Errorf("document not found")
		}
		return nil, fmt.Errorf("get document error: %s", res.Status())
	}

	var result map[string]interface{}
	if err := json.NewDecoder(res.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return result, nil
}

// DeleteIndex 删除索引
func (es *ESClient) DeleteIndex(ctx context.Context, indexName string) error {
	req := esapi.IndicesDeleteRequest{
		Index: []string{indexName},
	}

	res, err := req.Do(ctx, es.client)
	if err != nil {
		return fmt.Errorf("delete index failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() && res.StatusCode != 404 {
		return fmt.Errorf("delete index error: %s", res.Status())
	}

	return nil
}

// TestESAdvancedOperations 测试高级操作
func TestESAdvancedOperations(t *testing.T) {
	config := &ESConfig{
		Addresses: []string{"http://localhost:9200"},
		Username:  "elastic",
		Password:  "your_password",
		Timeout:   30,
	}

	client, err := NewESClient(config)
	require.NoError(t, err)

	ctx := context.Background()

	// 测试连接
	err = client.Ping(ctx)
	if err != nil {
		t.Skipf("Elasticsearch not available: %v", err)
		return
	}

	indexName := "test_advanced_index"

	// 清理可能存在的索引
	_ = client.DeleteIndex(ctx, indexName)

	// 创建索引
	mapping := `{
		"mappings": {
			"properties": {
				"title": {"type": "text"},
				"content": {"type": "text"},
				"category": {"type": "keyword"},
				"created_at": {"type": "date"},
				"views": {"type": "integer"}
			}
		}
	}`

	err = client.CreateIndex(ctx, indexName, mapping)
	require.NoError(t, err)

	// 测试批量索引
	documents := []map[string]interface{}{
		{
			"title":      "Document 1",
			"content":    "Content of document 1",
			"category":   "tech",
			"created_at": time.Now().Format(time.RFC3339),
			"views":      100,
		},
		{
			"title":      "Document 2",
			"content":    "Content of document 2",
			"category":   "science",
			"created_at": time.Now().Format(time.RFC3339),
			"views":      200,
		},
		{
			"title":      "Document 3",
			"content":    "Content of document 3",
			"category":   "tech",
			"created_at": time.Now().Format(time.RFC3339),
			"views":      150,
		},
	}

	err = client.BulkIndexDocuments(ctx, indexName, documents)
	require.NoError(t, err)

	// 等待索引刷新
	time.Sleep(2 * time.Second)

	// 测试复杂搜索查询
	complexQuery := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					{
						"match": map[string]interface{}{
							"content": "document",
						},
					},
				},
				"filter": []map[string]interface{}{
					{
						"term": map[string]interface{}{
							"category": "tech",
						},
					},
					{
						"range": map[string]interface{}{
							"views": map[string]interface{}{
								"gte": 100,
							},
						},
					},
				},
			},
		},
		"sort": []map[string]interface{}{
			{
				"views": map[string]interface{}{
					"order": "desc",
				},
			},
		},
		"size": 10,
	}

	result, err := client.SearchDocuments(ctx, indexName, complexQuery)
	require.NoError(t, err)
	assert.NotEmpty(t, result)

	log.Printf("Complex search result: %+v", result)

	// 测试聚合查询
	aggregationQuery := map[string]interface{}{
		"size": 0,
		"aggs": map[string]interface{}{
			"categories": map[string]interface{}{
				"terms": map[string]interface{}{
					"field": "category",
				},
			},
			"avg_views": map[string]interface{}{
				"avg": map[string]interface{}{
					"field": "views",
				},
			},
		},
	}

	aggResult, err := client.SearchDocuments(ctx, indexName, aggregationQuery)
	require.NoError(t, err)
	assert.NotEmpty(t, aggResult)

	log.Printf("Aggregation result: %+v", aggResult)

	// 清理测试索引
	err = client.DeleteIndex(ctx, indexName)
	require.NoError(t, err)
}

// TestESAPIKeyConnection 测试使用 API Key 连接 Elasticsearch
func TestESAPIKeyConnection(t *testing.T) {
	// 方式1: 使用编码后的 API Key
	config1 := &ESConfig{
		Addresses: []string{"http://localhost:9200"},
		APIKey:    "VnVhQ2ZHY0JDZGJrUW0tZTVhT3g6dWkybHAyYXhUTm1zeWFrdzl0dk5udw==", // 替换为实际的 API Key
		Timeout:   30,
	}

	client1, err := NewESClient(config1)
	require.NoError(t, err)

	ctx := context.Background()

	// 测试连接
	err = client1.Ping(ctx)
	if err != nil {
		t.Skipf("Elasticsearch with API Key not available: %v", err)
		return
	}

	// 获取集群信息
	info, err := client1.GetInfo(ctx)
	require.NoError(t, err)
	assert.NotEmpty(t, info)

	log.Printf("Elasticsearch Info (API Key): %+v", info)

	// 方式2: 使用 ID:Key 格式的 API Key
	config2 := &ESConfig{
		Addresses: []string{"http://localhost:9200"},
		APIKey:    "VuaCfGcBCdbkQm-e5aOx:ui2lp2axTNmsyakw9tvNnw", // 替换为实际的 API Key
		Timeout:   30,
	}

	client2, err := NewESClient(config2)
	require.NoError(t, err)

	// 测试连接
	err = client2.Ping(ctx)
	if err != nil {
		t.Logf("Second API Key format test failed (expected): %v", err)
	} else {
		log.Printf("Second API Key format test passed")
	}
}

// TestESCloudAPIKeyConnection 测试使用 API Key 连接 Elasticsearch Cloud
func TestESCloudAPIKeyConnection(t *testing.T) {
	config := &ESConfig{
		CloudID: "my-deployment:dXMtZWFzdC0xLmF3cy5mb3VuZC5pbyRjZWM2ZjI2MWE3NGJmMjRjZTMzYmI4ODExYjg0Mjk0ZiRhYTFlYTlkMDZkMjY0OWFhODFjOGM5M2Q4N2Q3ZDEyOA==", // 替换为实际的 Cloud ID
		APIKey:  "VnVhQ2ZHY0JDZGJrUW0tZTVhT3g6dWkybHAyYXhUTm1zeWFrdzl0dk5udw==",                                                                           // 替换为实际的 API Key
		Timeout: 30,
	}

	client, err := NewESClient(config)
	require.NoError(t, err)

	ctx := context.Background()

	// 测试连接
	err = client.Ping(ctx)
	if err != nil {
		t.Skipf("Elasticsearch Cloud with API Key not available: %v", err)
		return
	}

	// 获取集群信息
	info, err := client.GetInfo(ctx)
	require.NoError(t, err)
	assert.NotEmpty(t, info)

	log.Printf("Elasticsearch Cloud Info (API Key): %+v", info)
}

// CreateAPIKey 创建 API Key 的辅助函数
func (es *ESClient) CreateAPIKey(ctx context.Context, name string, roleDescriptors map[string]interface{}) (map[string]interface{}, error) {
	apiKeyBody := map[string]interface{}{
		"name": name,
	}

	if roleDescriptors != nil {
		apiKeyBody["role_descriptors"] = roleDescriptors
	}

	bodyJSON, err := json.Marshal(apiKeyBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal API key request: %w", err)
	}

	req := esapi.SecurityCreateAPIKeyRequest{
		Body: strings.NewReader(string(bodyJSON)),
	}

	res, err := req.Do(ctx, es.client)
	if err != nil {
		return nil, fmt.Errorf("create API key failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("create API key error: %s", res.Status())
	}

	var result map[string]interface{}
	if err := json.NewDecoder(res.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode API key response: %w", err)
	}

	return result, nil
}

// GetAPIKeyInfo 获取 API Key 信息
func (es *ESClient) GetAPIKeyInfo(ctx context.Context) (map[string]interface{}, error) {
	req := esapi.SecurityGetAPIKeyRequest{}

	res, err := req.Do(ctx, es.client)
	if err != nil {
		return nil, fmt.Errorf("get API key info failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("get API key info error: %s", res.Status())
	}

	var result map[string]interface{}
	if err := json.NewDecoder(res.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode API key info response: %w", err)
	}

	return result, nil
}

// InvalidateAPIKey 使 API Key 失效
func (es *ESClient) InvalidateAPIKey(ctx context.Context, apiKeyID string) error {
	invalidateBody := map[string]interface{}{
		"ids": []string{apiKeyID},
	}

	bodyJSON, err := json.Marshal(invalidateBody)
	if err != nil {
		return fmt.Errorf("failed to marshal invalidate request: %w", err)
	}

	req := esapi.SecurityInvalidateAPIKeyRequest{
		Body: strings.NewReader(string(bodyJSON)),
	}

	res, err := req.Do(ctx, es.client)
	if err != nil {
		return fmt.Errorf("invalidate API key failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("invalidate API key error: %s", res.Status())
	}

	return nil
}

// TestAPIKeyManagement 测试 API Key 管理功能
func TestAPIKeyManagement(t *testing.T) {
	newConfig := &ESConfig{
		Addresses: []string{"https://**************/:9200"},
		APIKey:    "Z1dsMHNKY0JtSE5oNHI1TWo5STk6TE1BSzdfWUYzOXpfY3MxY1UyMHBsUQ==",
		Timeout:   30,
	}

	newClient, err := NewESClient(newConfig)
	require.NoError(t, err)

	ctx := context.Background()
	// 测试新 API Key 的连接
	err = newClient.Ping(ctx)
	require.NoError(t, err)

	log.Printf("Successfully connected with new API Key")

}
