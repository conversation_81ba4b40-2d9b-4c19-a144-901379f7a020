package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"rag-elastic/config"
	"rag-elastic/models"
	"rag-elastic/rag"
)

func main() {
	// 设置日志格式
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	fmt.Println("🚀 RAG with Elasticsearch 8 Demo")
	fmt.Println("==================================")

	// 加载配置
	cfg := config.LoadConfig()
	fmt.Printf("📋 Configuration loaded:\n")
	fmt.Printf("   Elasticsearch: %v\n", cfg.Elasticsearch.Addresses)
	fmt.Printf("   Document Index: %s\n", cfg.DocumentIndex)
	fmt.Printf("   Chunk Index: %s\n", cfg.ChunkIndex)
	fmt.Printf("   Embedding Dimension: %d\n", cfg.EmbeddingDim)
	fmt.Printf("   Chunk Size: %d\n", cfg.ChunkSize)
	fmt.Println()

	// 创建 RAG 服务
	ragService, err := rag.NewService(cfg)
	if err != nil {
		log.Fatalf("❌ Failed to create RAG service: %v", err)
	}

	ctx := context.Background()

	// 初始化服务
	fmt.Println("🔧 Initializing RAG service...")
	if err := ragService.Initialize(ctx); err != nil {
		log.Fatalf("❌ Failed to initialize RAG service: %v", err)
	}
	fmt.Println("✅ RAG service initialized successfully")

	// 检查健康状态
	fmt.Println("\n🏥 Checking service health...")
	health, err := ragService.GetHealth(ctx)
	if err != nil {
		log.Fatalf("❌ Health check failed: %v", err)
	}
	fmt.Printf("✅ Service is healthy: %s\n", health["status"])

	// 示例文档
	documents := []struct {
		title   string
		content string
		source  string
		metadata map[string]interface{}
	}{
		{
			title: "人工智能基础",
			content: `人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。

AI 的主要目标包括：
1. 学习（Learning）：从经验中获取知识和技能
2. 推理（Reasoning）：使用规则来达到近似确定的结论或概率结论
3. 问题解决（Problem Solving）：寻找解决复杂问题的方法
4. 感知（Perception）：理解环境中的信息
5. 语言理解（Language Understanding）：理解和生成自然语言

机器学习是 AI 的一个重要子集，它使计算机能够在没有明确编程的情况下学习和改进。通过算法和统计模型，机器学习系统可以从数据中识别模式并做出预测。

深度学习是机器学习的一个更专门的子集，它使用人工神经网络来模拟人脑的工作方式。这种方法在图像识别、自然语言处理和语音识别等领域取得了显著成功。`,
			source: "ai_textbook.pdf",
			metadata: map[string]interface{}{
				"author":   "AI 专家",
				"category": "教育",
				"tags":     []string{"AI", "机器学习", "深度学习"},
				"level":    "入门",
			},
		},
		{
			title: "自然语言处理技术",
			content: `自然语言处理（Natural Language Processing，NLP）是人工智能的一个重要分支，专注于使计算机能够理解、解释和生成人类语言。

NLP 的核心技术包括：

1. 文本预处理
   - 分词（Tokenization）
   - 词性标注（Part-of-Speech Tagging）
   - 命名实体识别（Named Entity Recognition）
   - 句法分析（Syntactic Parsing）

2. 语义理解
   - 词向量表示（Word Embeddings）
   - 语义角色标注（Semantic Role Labeling）
   - 情感分析（Sentiment Analysis）
   - 意图识别（Intent Recognition）

3. 文本生成
   - 机器翻译（Machine Translation）
   - 文本摘要（Text Summarization）
   - 对话生成（Dialogue Generation）
   - 创意写作（Creative Writing）

现代 NLP 技术广泛应用于搜索引擎、翻译服务、聊天机器人、智能助手等领域。Transformer 架构的出现，特别是 BERT、GPT 等预训练模型，极大地推动了 NLP 技术的发展。`,
			source: "nlp_handbook.pdf",
			metadata: map[string]interface{}{
				"author":   "NLP 研究员",
				"category": "技术",
				"tags":     []string{"NLP", "文本处理", "语言模型"},
				"level":    "中级",
			},
		},
		{
			title: "RAG 系统架构",
			content: `检索增强生成（Retrieval-Augmented Generation，RAG）是一种结合了信息检索和文本生成的 AI 架构。

RAG 系统的核心组件：

1. 文档存储与索引
   - 文档预处理和分块
   - 向量化表示
   - 向量数据库存储
   - 索引优化

2. 检索系统
   - 查询理解
   - 相似度计算
   - 结果排序
   - 多模态检索

3. 生成系统
   - 上下文整合
   - 提示工程
   - 生成控制
   - 结果后处理

RAG 的优势：
- 知识更新：可以动态更新知识库而无需重新训练模型
- 可解释性：可以追溯生成内容的来源
- 准确性：基于真实文档生成，减少幻觉
- 效率：相比于训练大型模型，RAG 更加高效

RAG 广泛应用于问答系统、知识助手、文档分析等场景。`,
			source: "rag_architecture.md",
			metadata: map[string]interface{}{
				"author":   "系统架构师",
				"category": "架构",
				"tags":     []string{"RAG", "检索", "生成", "架构"},
				"level":    "高级",
			},
		},
	}

	// 添加文档
	fmt.Println("\n📚 Adding documents...")
	var docIDs []string
	for i, docData := range documents {
		fmt.Printf("Adding document %d: %s\n", i+1, docData.title)
		
		doc, err := ragService.AddDocument(
			ctx,
			docData.title,
			docData.content,
			docData.source,
			models.SourceTypeFile,
			docData.metadata,
		)
		if err != nil {
			log.Printf("❌ Failed to add document: %v", err)
			continue
		}
		
		docIDs = append(docIDs, doc.ID)
		fmt.Printf("✅ Document added with ID: %s\n", doc.ID)
	}

	// 等待文档处理完成
	fmt.Println("\n⏳ Waiting for document processing...")
	time.Sleep(5 * time.Second)

	// 检查文档状态
	fmt.Println("\n📊 Document status:")
	for i, docID := range docIDs {
		doc, err := ragService.GetDocument(ctx, docID)
		if err != nil {
			log.Printf("❌ Failed to get document %s: %v", docID, err)
			continue
		}
		fmt.Printf("Document %d: %s - Status: %s, Chunks: %d\n", 
			i+1, doc.Title, doc.Status, doc.ChunkCount)
	}

	// 执行搜索查询
	fmt.Println("\n🔍 Performing searches...")
	
	queries := []string{
		"什么是人工智能？",
		"机器学习和深度学习的区别",
		"自然语言处理的核心技术",
		"RAG 系统的优势",
		"Transformer 架构",
	}

	for i, query := range queries {
		fmt.Printf("\n查询 %d: %s\n", i+1, query)
		fmt.Println(strings.Repeat("-", 50))
		
		searchReq := &models.SearchRequest{
			Query:          query,
			TopK:           3,
			ScoreThreshold: 0.1,
		}

		response, err := ragService.Search(ctx, searchReq)
		if err != nil {
			log.Printf("❌ Search failed: %v", err)
			continue
		}

		fmt.Printf("找到 %d 个相关结果 (耗时: %s)\n", len(response.Results), response.TimeTaken)
		
		for j, result := range response.Results {
			fmt.Printf("\n结果 %d (相似度: %.4f):\n", j+1, result.Score)
			fmt.Printf("文档ID: %s\n", result.Chunk.DocumentID)
			fmt.Printf("内容: %s...\n", truncateText(result.Chunk.Content, 200))
			
			if metadata, ok := result.Chunk.Metadata["document_title"].(string); ok {
				fmt.Printf("来源: %s\n", metadata)
			}
		}
	}

	// 演示文档过滤搜索
	if len(docIDs) > 0 {
		fmt.Println("\n🎯 Filtered search demo:")
		fmt.Println(strings.Repeat("-", 50))
		
		searchReq := &models.SearchRequest{
			Query:       "深度学习",
			TopK:        5,
			DocumentIDs: []string{docIDs[0]}, // 只在第一个文档中搜索
		}

		response, err := ragService.Search(ctx, searchReq)
		if err != nil {
			log.Printf("❌ Filtered search failed: %v", err)
		} else {
			fmt.Printf("在指定文档中找到 %d 个结果\n", len(response.Results))
			for j, result := range response.Results {
				fmt.Printf("结果 %d: %s...\n", j+1, truncateText(result.Chunk.Content, 100))
			}
		}
	}

	// 列出所有文档
	fmt.Println("\n📋 All documents:")
	fmt.Println(strings.Repeat("-", 50))
	docs, total, err := ragService.ListDocuments(ctx, 10, 0)
	if err != nil {
		log.Printf("❌ Failed to list documents: %v", err)
	} else {
		fmt.Printf("总共 %d 个文档:\n", total)
		for i, doc := range docs {
			fmt.Printf("%d. %s (%s) - %d chunks\n", 
				i+1, doc.Title, doc.Status, doc.ChunkCount)
		}
	}

	// 清理演示数据（可选）
	fmt.Println("\n🧹 Cleanup (optional):")
	fmt.Print("Do you want to delete the demo documents? (y/N): ")
	var input string
	fmt.Scanln(&input)
	
	if input == "y" || input == "Y" {
		fmt.Println("Deleting demo documents...")
		for i, docID := range docIDs {
			err := ragService.DeleteDocument(ctx, docID)
			if err != nil {
				log.Printf("❌ Failed to delete document %d: %v", i+1, err)
			} else {
				fmt.Printf("✅ Document %d deleted\n", i+1)
			}
		}
	}

	fmt.Println("\n🎉 RAG demo completed!")
}

// 辅助函数：截断文本
func truncateText(text string, maxLen int) string {
	if len(text) <= maxLen {
		return text
	}
	return text[:maxLen] + "..."
}


