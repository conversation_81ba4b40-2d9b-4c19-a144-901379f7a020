package rag

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	"rag-elastic/chunker"
	"rag-elastic/config"
	"rag-elastic/elasticsearch"
	"rag-elastic/embedding"
	"rag-elastic/models"
)

// Service RAG 服务
type Service struct {
	esClient        *elasticsearch.Client
	embeddingService embedding.Service
	textSplitter    *chunker.TextSplitter
	config          *config.RAGConfig
}

// NewService 创建新的 RAG 服务
func NewService(cfg *config.RAGConfig) (*Service, error) {
	// 创建 Elasticsearch 客户端
	esClient, err := elasticsearch.NewClient(&cfg.Elasticsearch)
	if err != nil {
		return nil, fmt.Errorf("failed to create elasticsearch client: %w", err)
	}

	// 创建嵌入服务
	embeddingService := embedding.CreateEmbeddingService("mock", "", cfg.EmbeddingModel, cfg.EmbeddingDim)

	// 创建文本分块器
	textSplitter := chunker.NewTextSplitter(cfg.ChunkSize, cfg.ChunkOverlap)

	return &Service{
		esClient:        esClient,
		embeddingService: embeddingService,
		textSplitter:    textSplitter,
		config:          cfg,
	}, nil
}

// Initialize 初始化 RAG 服务
func (s *Service) Initialize(ctx context.Context) error {
	// 测试 Elasticsearch 连接
	if err := s.esClient.Ping(ctx); err != nil {
		return fmt.Errorf("elasticsearch ping failed: %w", err)
	}

	// 创建文档索引
	if err := s.esClient.CreateDocumentIndex(ctx, s.config.DocumentIndex); err != nil {
		return fmt.Errorf("failed to create document index: %w", err)
	}

	// 创建文档块索引
	if err := s.esClient.CreateChunkIndex(ctx, s.config.ChunkIndex, s.config.EmbeddingDim); err != nil {
		return fmt.Errorf("failed to create chunk index: %w", err)
	}

	log.Printf("RAG service initialized successfully")
	return nil
}

// AddDocument 添加文档
func (s *Service) AddDocument(ctx context.Context, title, content, source, sourceType string, metadata map[string]interface{}) (*models.Document, error) {
	// 创建文档
	doc := &models.Document{
		ID:         uuid.New().String(),
		Title:      title,
		Content:    content,
		Source:     source,
		SourceType: sourceType,
		Metadata:   metadata,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		Size:       int64(len(content)),
		Status:     models.DocumentStatusProcessing,
	}

	// 索引文档
	if err := s.esClient.IndexDocument(ctx, s.config.DocumentIndex, doc); err != nil {
		return nil, fmt.Errorf("failed to index document: %w", err)
	}

	// 异步处理文档分块和向量化
	go func() {
		if err := s.processDocument(context.Background(), doc); err != nil {
			log.Printf("Failed to process document %s: %v", doc.ID, err)
		}
	}()

	return doc, nil
}

// processDocument 处理文档（分块和向量化）
func (s *Service) processDocument(ctx context.Context, doc *models.Document) error {
	// 分割文档为块
	chunks, err := s.textSplitter.SplitDocument(doc)
	if err != nil {
		s.updateDocumentStatus(ctx, doc.ID, models.DocumentStatusFailed)
		return fmt.Errorf("failed to split document: %w", err)
	}

	// 提取文本内容用于向量化
	var texts []string
	for _, chunk := range chunks {
		texts = append(texts, chunk.Content)
	}

	// 获取嵌入向量
	embeddings, err := s.embeddingService.GetEmbeddings(ctx, texts)
	if err != nil {
		s.updateDocumentStatus(ctx, doc.ID, models.DocumentStatusFailed)
		return fmt.Errorf("failed to get embeddings: %w", err)
	}

	// 为块添加嵌入向量
	for i, chunk := range chunks {
		if i < len(embeddings) {
			chunk.Embedding = embeddings[i]
		}
	}

	// 批量索引文档块
	if err := s.esClient.BulkIndexChunks(ctx, s.config.ChunkIndex, chunks); err != nil {
		s.updateDocumentStatus(ctx, doc.ID, models.DocumentStatusFailed)
		return fmt.Errorf("failed to index chunks: %w", err)
	}

	// 更新文档状态
	update := map[string]interface{}{
		"status":      models.DocumentStatusCompleted,
		"chunk_count": len(chunks),
		"updated_at":  time.Now(),
	}
	if err := s.esClient.UpdateDocument(ctx, s.config.DocumentIndex, doc.ID, update); err != nil {
		return fmt.Errorf("failed to update document status: %w", err)
	}

	log.Printf("Document %s processed successfully with %d chunks", doc.ID, len(chunks))
	return nil
}

// updateDocumentStatus 更新文档状态
func (s *Service) updateDocumentStatus(ctx context.Context, docID, status string) {
	update := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}
	if err := s.esClient.UpdateDocument(ctx, s.config.DocumentIndex, docID, update); err != nil {
		log.Printf("Failed to update document status: %v", err)
	}
}

// Search 搜索相关文档块
func (s *Service) Search(ctx context.Context, req *models.SearchRequest) (*models.SearchResponse, error) {
	startTime := time.Now()

	// 获取查询的嵌入向量
	queryEmbedding, err := s.embeddingService.GetEmbedding(ctx, req.Query)
	if err != nil {
		return nil, fmt.Errorf("failed to get query embedding: %w", err)
	}

	// 执行向量搜索
	results, err := s.esClient.VectorSearch(
		ctx,
		s.config.ChunkIndex,
		queryEmbedding,
		req.TopK,
		req.ScoreThreshold,
		req.Filters,
	)
	if err != nil {
		return nil, fmt.Errorf("vector search failed: %w", err)
	}

	// 如果指定了文档ID过滤
	if len(req.DocumentIDs) > 0 {
		results = s.filterByDocumentIDs(results, req.DocumentIDs)
	}

	timeTaken := time.Since(startTime)

	return &models.SearchResponse{
		Results:   results,
		Total:     int64(len(results)),
		TimeTaken: timeTaken.String(),
	}, nil
}

// filterByDocumentIDs 根据文档ID过滤结果
func (s *Service) filterByDocumentIDs(results []*models.SearchResult, documentIDs []string) []*models.SearchResult {
	docIDSet := make(map[string]bool)
	for _, id := range documentIDs {
		docIDSet[id] = true
	}

	var filtered []*models.SearchResult
	for _, result := range results {
		if docIDSet[result.Chunk.DocumentID] {
			filtered = append(filtered, result)
		}
	}

	return filtered
}

// GetDocument 获取文档
func (s *Service) GetDocument(ctx context.Context, docID string) (*models.Document, error) {
	return s.esClient.GetDocument(ctx, s.config.DocumentIndex, docID)
}

// DeleteDocument 删除文档
func (s *Service) DeleteDocument(ctx context.Context, docID string) error {
	// 删除文档块
	if err := s.esClient.DeleteChunksByDocumentID(ctx, s.config.ChunkIndex, docID); err != nil {
		return fmt.Errorf("failed to delete chunks: %w", err)
	}

	// 删除文档
	if err := s.esClient.DeleteDocument(ctx, s.config.DocumentIndex, docID); err != nil {
		return fmt.Errorf("failed to delete document: %w", err)
	}

	return nil
}

// ListDocuments 列出文档
func (s *Service) ListDocuments(ctx context.Context, limit, offset int) ([]*models.Document, int64, error) {
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"match_all": map[string]interface{}{},
		},
		"size": limit,
		"from": offset,
		"sort": []map[string]interface{}{
			{
				"created_at": map[string]interface{}{
					"order": "desc",
				},
			},
		},
	}

	return s.esClient.SearchDocuments(ctx, s.config.DocumentIndex, query)
}

// GetHealth 获取服务健康状态
func (s *Service) GetHealth(ctx context.Context) (map[string]interface{}, error) {
	// 检查 Elasticsearch 健康状态
	esHealth, err := s.esClient.Health(ctx)
	if err != nil {
		return nil, fmt.Errorf("elasticsearch health check failed: %w", err)
	}

	// 检查索引是否存在
	docIndexExists, err := s.esClient.IndexExists(ctx, s.config.DocumentIndex)
	if err != nil {
		return nil, fmt.Errorf("failed to check document index: %w", err)
	}

	chunkIndexExists, err := s.esClient.IndexExists(ctx, s.config.ChunkIndex)
	if err != nil {
		return nil, fmt.Errorf("failed to check chunk index: %w", err)
	}

	return map[string]interface{}{
		"status":              "healthy",
		"elasticsearch":       esHealth,
		"document_index":      docIndexExists,
		"chunk_index":         chunkIndexExists,
		"embedding_dimension": s.embeddingService.GetDimension(),
		"chunk_size":          s.config.ChunkSize,
		"chunk_overlap":       s.config.ChunkOverlap,
	}, nil
}
