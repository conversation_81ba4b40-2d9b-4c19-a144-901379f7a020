# Elasticsearch API Key 连接指南

## 为什么使用 API Key？

API Key 是 Elasticsearch 推荐的身份验证方式，相比用户名/密码有以下优势：

1. **更安全**：可以设置细粒度权限
2. **易于管理**：可以随时创建、删除、失效
3. **无密码泄露风险**：不需要在代码中存储密码
4. **支持过期时间**：可以设置自动过期
5. **审计友好**：每个 API Key 都有唯一标识

## 创建 API Key

### 方法1：使用 Kibana Dev Tools

```json
POST /_security/api_key
{
  "name": "my-api-key",
  "expiration": "1d",
  "role_descriptors": {
    "my_role": {
      "cluster": ["monitor"],
      "indices": [
        {
          "names": ["test_*"],
          "privileges": ["read", "write", "create_index"]
        }
      ]
    }
  }
}
```

### 方法2：使用 curl 命令

```bash
curl -X POST "localhost:9200/_security/api_key" \
  -H "Content-Type: application/json" \
  -u elastic:your_password \
  -d '{
    "name": "my-api-key",
    "role_descriptors": {
      "my_role": {
        "cluster": ["monitor"],
        "indices": [
          {
            "names": ["test_*"],
            "privileges": ["read", "write", "create_index"]
          }
        ]
      }
    }
  }'
```

### 方法3：使用 Go 代码

```go
// 使用超级用户账号创建 API Key
config := &ESConfig{
    Addresses: []string{"http://localhost:9200"},
    Username:  "elastic",
    Password:  "your_password",
}

client, _ := NewESClient(config)

roleDescriptors := map[string]interface{}{
    "my_role": map[string]interface{}{
        "cluster": []string{"monitor"},
        "indices": []interface{}{
            map[string]interface{}{
                "names":      []string{"test_*"},
                "privileges": []string{"read", "write", "create_index"},
            },
        },
    },
}

apiKeyResult, err := client.CreateAPIKey(ctx, "my-api-key", roleDescriptors)
```

## 使用 API Key 连接

### 方式1：使用编码后的 API Key

```go
config := &ESConfig{
    Addresses: []string{"http://localhost:9200"},
    APIKey:    "VnVhQ2ZHY0JDZGJrUW0tZTVhT3g6dWkybHAyYXhUTm1zeWFrdzl0dk5udw==",
    Timeout:   30,
}

client, err := NewESClient(config)
```

### 方式2：使用 ID:Key 格式

```go
config := &ESConfig{
    Addresses: []string{"http://localhost:9200"},
    APIKey:    "VuaCfGcBCdbkQm-e5aOx:ui2lp2axTNmsyakw9tvNnw",
    Timeout:   30,
}

client, err := NewESClient(config)
```

### 方式3：连接 Elasticsearch Cloud

```go
config := &ESConfig{
    CloudID: "my-deployment:dXMtZWFzdC0xLmF3cy5mb3VuZC5pbyRjZWM2ZjI2MWE3NGJmMjRjZTMzYmI4ODExYjg0Mjk0ZiRhYTFlYTlkMDZkMjY0OWFhODFjOGM5M2Q4N2Q3ZDEyOA==",
    APIKey:  "VnVhQ2ZHY0JDZGJrUW0tZTVhT3g6dWkybHAyYXhUTm1zeWFrdzl0dk5udw==",
    Timeout: 30,
}

client, err := NewESClient(config)
```

## API Key 管理

### 查看 API Key 信息

```go
apiKeyInfo, err := client.GetAPIKeyInfo(ctx)
fmt.Printf("API Keys: %+v", apiKeyInfo)
```

### 使 API Key 失效

```go
err := client.InvalidateAPIKey(ctx, "api_key_id")
```

### 查看当前 API Key 权限

```bash
curl -X GET "localhost:9200/_security/_authenticate" \
  -H "Authorization: ApiKey VnVhQ2ZHY0JDZGJrUW0tZTVhT3g6dWkybHAyYXhUTm1zeWFrdzl0dk5udw=="
```

## 权限配置示例

### 只读权限

```json
{
  "role_descriptors": {
    "read_only": {
      "cluster": ["monitor"],
      "indices": [
        {
          "names": ["logs-*", "metrics-*"],
          "privileges": ["read"]
        }
      ]
    }
  }
}
```

### 写入权限

```json
{
  "role_descriptors": {
    "write_access": {
      "cluster": ["monitor"],
      "indices": [
        {
          "names": ["app_logs-*"],
          "privileges": ["read", "write", "create_index", "delete_index"]
        }
      ]
    }
  }
}
```

### 管理员权限

```json
{
  "role_descriptors": {
    "admin": {
      "cluster": ["all"],
      "indices": [
        {
          "names": ["*"],
          "privileges": ["all"]
        }
      ]
    }
  }
}
```

## 安全最佳实践

1. **最小权限原则**：只授予必要的权限
2. **设置过期时间**：为 API Key 设置合理的过期时间
3. **定期轮换**：定期更新 API Key
4. **安全存储**：不要在代码中硬编码 API Key
5. **监控使用**：监控 API Key 的使用情况
6. **及时失效**：不再使用的 API Key 应及时失效

## 环境变量配置

```bash
# 设置环境变量
export ES_API_KEY="VnVhQ2ZHY0JDZGJrUW0tZTVhT3g6dWkybHAyYXhUTm1zeWFrdzl0dk5udw=="
export ES_ADDRESSES="http://localhost:9200"

# 在代码中读取
config := &ESConfig{
    Addresses: strings.Split(os.Getenv("ES_ADDRESSES"), ","),
    APIKey:    os.Getenv("ES_API_KEY"),
    Timeout:   30,
}
```

## 故障排除

### 常见错误

1. **401 Unauthorized**：API Key 无效或已过期
2. **403 Forbidden**：API Key 权限不足
3. **400 Bad Request**：API Key 格式错误

### 调试方法

```go
// 检查 API Key 是否有效
info, err := client.GetInfo(ctx)
if err != nil {
    log.Printf("API Key validation failed: %v", err)
}

// 检查权限
auth, err := client.GetAPIKeyInfo(ctx)
if err != nil {
    log.Printf("Cannot get API key info: %v", err)
}
```

## 运行测试

```bash
# 测试 API Key 连接
go test -v ./test/ -run TestESAPIKeyConnection

# 测试 API Key 管理
go test -v ./test/ -run TestAPIKeyManagement

# 测试 Cloud 连接
go test -v ./test/ -run TestESCloudAPIKeyConnection
```
