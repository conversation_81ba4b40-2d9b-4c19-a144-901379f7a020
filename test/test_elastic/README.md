# RAG with Elasticsearch 8

这是一个基于 Elasticsearch 8 实现的 RAG (Retrieval-Augmented Generation) 系统，仿照 [RAGFlow](https://github.com/infiniflow/ragflow) 的架构设计。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   文档输入      │    │   文本分块      │    │   向量化        │
│   Document      │───▶│   Text          │───▶│   Embedding     │
│   Input         │    │   Chunking      │    │   Service       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐             │
│   查询处理      │    │   相似度搜索    │             │
│   Query         │───▶│   Vector        │◀────────────┘
│   Processing    │    │   Search        │
└─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  Elasticsearch  │
                       │     Index       │
                       └─────────────────┘
```

## 📁 项目结构

```
test/test_elastic/
├── config/           # 配置管理
│   └── config.go
├── models/           # 数据模型
│   └── document.go
├── elasticsearch/    # ES 客户端封装
│   ├── client.go
│   ├── index.go
│   ├── document.go
│   └── chunk.go
├── chunker/          # 文本分块器
│   └── text_splitter.go
├── embedding/        # 嵌入服务
│   └── service.go
├── rag/              # RAG 核心服务
│   └── service.go
├── main.go           # 主程序示例
├── rag_test.go       # 测试文件
├── go.mod            # Go 模块文件
└── README.md         # 项目文档
```

## 🚀 快速开始

### 1. 环境准备

确保您已安装：
- Go 1.21+
- Elasticsearch 8.x
- Git

### 2. 启动 Elasticsearch

```bash
# 使用 Docker 启动 Elasticsearch
docker run -d \
  --name elasticsearch \
  -p 9200:9200 \
  -p 9300:9300 \
  -e "discovery.type=single-node" \
  -e "xpack.security.enabled=false" \
  docker.elastic.co/elasticsearch/elasticsearch:8.11.1
```

### 3. 设置环境变量

```bash
# Elasticsearch 配置
export ES_ADDRESSES="http://localhost:9200"
export ES_API_KEY="your_api_key_here"  # 可选，如果启用了安全功能

# RAG 配置
export RAG_DOCUMENT_INDEX="rag_documents"
export RAG_CHUNK_INDEX="rag_chunks"
export RAG_CHUNK_SIZE="512"
export RAG_CHUNK_OVERLAP="50"
export RAG_TOP_K="5"
export RAG_SCORE_THRESHOLD="0.7"
export RAG_EMBEDDING_DIM="768"
```

### 4. 安装依赖

```bash
cd test/test_elastic
go mod tidy
```

### 5. 运行示例

```bash
# 运行主程序
go run main.go

# 运行测试
go test -v
```

## 🔧 配置说明

### Elasticsearch 配置

```go
type ElasticsearchConfig struct {
    Addresses []string // ES 集群地址
    Username  string   // 用户名
    Password  string   // 密码
    APIKey    string   // API Key (推荐)
    CloudID   string   // ES Cloud ID
    Timeout   int      // 超时时间(秒)
}
```

### RAG 配置

```go
type RAGConfig struct {
    DocumentIndex  string  // 文档索引名
    ChunkIndex     string  // 文档块索引名
    ChunkSize      int     // 分块大小
    ChunkOverlap   int     // 分块重叠
    TopK           int     // 检索数量
    ScoreThreshold float64 // 相似度阈值
    EmbeddingDim   int     // 向量维度
    EmbeddingModel string  // 嵌入模型
}
```

## 📚 API 使用

### 添加文档

```go
doc, err := ragService.AddDocument(
    ctx,
    "文档标题",
    "文档内容...",
    "文档来源",
    models.SourceTypeText,
    map[string]interface{}{
        "author": "作者",
        "tags":   []string{"标签1", "标签2"},
    },
)
```

### 搜索文档

```go
searchReq := &models.SearchRequest{
    Query:          "搜索查询",
    TopK:           5,
    ScoreThreshold: 0.7,
    DocumentIDs:    []string{"doc1", "doc2"}, // 可选：限制搜索范围
}

response, err := ragService.Search(ctx, searchReq)
```

### 获取文档

```go
doc, err := ragService.GetDocument(ctx, "document_id")
```

### 删除文档

```go
err := ragService.DeleteDocument(ctx, "document_id")
```

### 列出文档

```go
docs, total, err := ragService.ListDocuments(ctx, limit, offset)
```

## 🧪 测试

### 运行所有测试

```bash
go test -v
```

### 运行特定测试

```bash
go test -v -run TestRAGService
go test -v -run TestRAGServiceBatch
```

### 基准测试

```bash
go test -bench=BenchmarkRAGSearch -benchmem
```

## 🔍 核心功能

### 1. 文档管理
- ✅ 文档添加、获取、删除
- ✅ 文档状态跟踪
- ✅ 元数据支持
- ✅ 批量操作

### 2. 文本处理
- ✅ 智能文本分块
- ✅ 段落保持
- ✅ 重叠处理
- ✅ 多语言支持

### 3. 向量搜索
- ✅ 语义相似度搜索
- ✅ 混合搜索（关键词+向量）
- ✅ 结果过滤
- ✅ 相似度阈值

### 4. 嵌入服务
- ✅ 模拟嵌入服务
- ✅ 可扩展架构
- ✅ 批量处理
- ✅ 多种模型支持

## 🎯 高级特性

### 1. 索引管理
- 自动创建索引
- 索引映射优化
- 向量字段配置
- 分片和副本设置

### 2. 搜索优化
- 余弦相似度计算
- 脚本评分
- 结果排序
- 分页支持

### 3. 错误处理
- 连接重试
- 超时处理
- 详细错误信息
- 健康检查

### 4. 性能优化
- 批量索引
- 连接池
- 缓存机制
- 异步处理

## 🔧 扩展开发

### 添加新的嵌入服务

```go
type CustomEmbeddingService struct {
    // 自定义字段
}

func (s *CustomEmbeddingService) GetEmbedding(ctx context.Context, text string) ([]float64, error) {
    // 实现嵌入逻辑
}

func (s *CustomEmbeddingService) GetEmbeddings(ctx context.Context, texts []string) ([][]float64, error) {
    // 实现批量嵌入逻辑
}

func (s *CustomEmbeddingService) GetDimension() int {
    // 返回向量维度
}
```

### 自定义文本分块器

```go
type CustomTextSplitter struct {
    // 自定义字段
}

func (ts *CustomTextSplitter) SplitText(text string) []string {
    // 实现自定义分块逻辑
}
```

## 🐛 故障排除

### 常见问题

1. **Elasticsearch 连接失败**
   - 检查 ES 是否运行
   - 验证地址和端口
   - 检查网络连接

2. **索引创建失败**
   - 检查 ES 权限
   - 验证索引名称
   - 查看 ES 日志

3. **搜索结果为空**
   - 检查文档是否已索引
   - 降低相似度阈值
   - 验证查询内容

4. **向量维度不匹配**
   - 检查嵌入服务配置
   - 验证索引映射
   - 重新创建索引

### 调试技巧

```bash
# 查看 ES 健康状态
curl -X GET "localhost:9200/_cluster/health"

# 查看索引信息
curl -X GET "localhost:9200/rag_documents/_mapping"

# 查看文档数量
curl -X GET "localhost:9200/rag_chunks/_count"
```

## 📈 性能调优

### Elasticsearch 优化

```json
{
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 0,
    "refresh_interval": "30s",
    "index.max_result_window": 10000
  }
}
```

### 应用层优化

- 使用批量操作
- 实现连接池
- 添加缓存层
- 异步处理

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
