{"api_key_local": {"addresses": ["http://localhost:9200"], "api_key": "your_api_key_here", "timeout": 30, "description": "使用 API Key 连接本地 Elasticsearch"}, "api_key_cloud": {"cloud_id": "your_cloud_id_here", "api_key": "your_api_key_here", "timeout": 30, "description": "使用 API Key 连接 Elasticsearch Cloud"}, "api_key_ssl": {"addresses": ["https://localhost:9200"], "api_key": "your_api_key_here", "timeout": 30, "description": "使用 API Key 连接 SSL Elasticsearch"}, "username_password": {"addresses": ["http://localhost:9200"], "username": "elastic", "password": "your_password_here", "timeout": 30, "description": "使用用户名密码连接（不推荐用于生产环境）"}, "api_key_examples": {"encoded_api_key": "VnVhQ2ZHY0JDZGJrUW0tZTVhT3g6dWkybHAyYXhUTm1zeWFrdzl0dk5udw==", "id_and_key": {"id": "VuaCfGcBCdbkQm-e5aOx", "api_key": "ui2lp2axTNmsyakw9tvNnw"}, "description": "API Key 可以是编码后的字符串，或者 id:key 格式"}}