#!/bin/bash

# RAG with Elasticsearch 8 运行脚本

set -e

echo "🚀 RAG with Elasticsearch 8 Setup Script"
echo "=========================================="

# 检查 Go 环境
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go 1.21+ first."
    echo "   Download from: https://golang.org/doc/install"
    exit 1
fi

GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
echo "✅ Go version: $GO_VERSION"

# 检查 Docker 环境
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "   Download from: https://docs.docker.com/get-docker/"
    exit 1
fi

echo "✅ Docker is available"

# 设置默认环境变量
export ES_ADDRESSES="${ES_ADDRESSES:-http://localhost:9200}"
export RAG_DOCUMENT_INDEX="${RAG_DOCUMENT_INDEX:-rag_documents}"
export RAG_CHUNK_INDEX="${RAG_CHUNK_INDEX:-rag_chunks}"
export RAG_CHUNK_SIZE="${RAG_CHUNK_SIZE:-512}"
export RAG_CHUNK_OVERLAP="${RAG_CHUNK_OVERLAP:-50}"
export RAG_TOP_K="${RAG_TOP_K:-5}"
export RAG_SCORE_THRESHOLD="${RAG_SCORE_THRESHOLD:-0.1}"
export RAG_EMBEDDING_DIM="${RAG_EMBEDDING_DIM:-768}"

echo ""
echo "📋 Configuration:"
echo "   ES_ADDRESSES: $ES_ADDRESSES"
echo "   RAG_DOCUMENT_INDEX: $RAG_DOCUMENT_INDEX"
echo "   RAG_CHUNK_INDEX: $RAG_CHUNK_INDEX"
echo "   RAG_CHUNK_SIZE: $RAG_CHUNK_SIZE"
echo "   RAG_EMBEDDING_DIM: $RAG_EMBEDDING_DIM"

# 检查 Elasticsearch 是否运行
echo ""
echo "🔍 Checking Elasticsearch..."
if curl -s "$ES_ADDRESSES" > /dev/null 2>&1; then
    echo "✅ Elasticsearch is running at $ES_ADDRESSES"
    ES_INFO=$(curl -s "$ES_ADDRESSES" | grep -o '"number" : "[^"]*"' | cut -d'"' -f4)
    echo "   Version: $ES_INFO"
else
    echo "❌ Elasticsearch is not running at $ES_ADDRESSES"
    echo ""
    echo "🐳 Starting Elasticsearch with Docker..."
    
    # 停止可能存在的容器
    docker stop elasticsearch-rag 2>/dev/null || true
    docker rm elasticsearch-rag 2>/dev/null || true
    
    # 启动新容器
    docker run -d \
        --name elasticsearch-rag \
        -p 9200:9200 \
        -p 9300:9300 \
        -e "discovery.type=single-node" \
        -e "xpack.security.enabled=false" \
        -e "ES_JAVA_OPTS=-Xms512m -Xmx512m" \
        docker.elastic.co/elasticsearch/elasticsearch:8.11.1
    
    echo "⏳ Waiting for Elasticsearch to start..."
    for i in {1..30}; do
        if curl -s "$ES_ADDRESSES" > /dev/null 2>&1; then
            echo "✅ Elasticsearch started successfully!"
            break
        fi
        echo "   Attempt $i/30..."
        sleep 2
    done
    
    if ! curl -s "$ES_ADDRESSES" > /dev/null 2>&1; then
        echo "❌ Failed to start Elasticsearch after 60 seconds"
        echo "   Please check Docker logs: docker logs elasticsearch-rag"
        exit 1
    fi
fi

# 安装 Go 依赖
echo ""
echo "📦 Installing Go dependencies..."
go mod tidy

# 选择运行模式
echo ""
echo "🎯 Choose what to run:"
echo "1) Run demo (main.go)"
echo "2) Run tests"
echo "3) Run benchmarks"
echo "4) Interactive mode"
echo "5) Exit"

read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        echo ""
        echo "🎬 Running RAG demo..."
        go run main.go
        ;;
    2)
        echo ""
        echo "🧪 Running tests..."
        go test -v
        ;;
    3)
        echo ""
        echo "⚡ Running benchmarks..."
        go test -bench=. -benchmem
        ;;
    4)
        echo ""
        echo "🔧 Interactive mode - you can now run commands manually:"
        echo "   go run main.go          # Run demo"
        echo "   go test -v              # Run tests"
        echo "   go test -bench=.        # Run benchmarks"
        echo ""
        echo "Environment variables are set. Elasticsearch is running."
        echo "Press Ctrl+C to exit when done."
        
        # 保持脚本运行
        trap 'echo ""; echo "👋 Goodbye!"; exit 0' INT
        while true; do
            sleep 1
        done
        ;;
    5)
        echo "👋 Goodbye!"
        exit 0
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "🎉 Done!"
echo ""
echo "💡 Useful commands:"
echo "   # Check Elasticsearch health"
echo "   curl $ES_ADDRESSES/_cluster/health"
echo ""
echo "   # View indices"
echo "   curl $ES_ADDRESSES/_cat/indices"
echo ""
echo "   # Stop Elasticsearch container"
echo "   docker stop elasticsearch-rag"
echo ""
echo "   # View container logs"
echo "   docker logs elasticsearch-rag"
