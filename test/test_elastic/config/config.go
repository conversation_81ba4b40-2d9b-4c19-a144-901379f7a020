package config

import (
	"os"
	"strconv"
	"strings"
)

// ElasticsearchConfig Elasticsearch 配置
type ElasticsearchConfig struct {
	Addresses []string `json:"addresses"`
	Username  string   `json:"username"`
	Password  string   `json:"password"`
	<PERSON>Key    string   `json:"api_key"`
	CloudID   string   `json:"cloud_id"`
	Timeout   int      `json:"timeout"`
}

// RAGConfig RAG 系统配置
type RAGConfig struct {
	Elasticsearch ElasticsearchConfig `json:"elasticsearch"`
	
	// 索引配置
	DocumentIndex string `json:"document_index"`
	ChunkIndex    string `json:"chunk_index"`
	
	// 分块配置
	ChunkSize    int `json:"chunk_size"`
	ChunkOverlap int `json:"chunk_overlap"`
	
	// 检索配置
	TopK         int     `json:"top_k"`
	ScoreThreshold float64 `json:"score_threshold"`
	
	// 向量配置
	EmbeddingDim int    `json:"embedding_dim"`
	EmbeddingModel string `json:"embedding_model"`
}

// LoadConfig 从环境变量加载配置
func LoadConfig() *RAGConfig {
	config := &RAGConfig{
		Elasticsearch: ElasticsearchConfig{
			Addresses: getEnvStringSlice("ES_ADDRESSES", []string{"http://localhost:9200"}),
			Username:  getEnv("ES_USERNAME", ""),
			Password:  getEnv("ES_PASSWORD", ""),
			APIKey:    getEnv("ES_API_KEY", ""),
			CloudID:   getEnv("ES_CLOUD_ID", ""),
			Timeout:   getEnvInt("ES_TIMEOUT", 30),
		},
		DocumentIndex:  getEnv("RAG_DOCUMENT_INDEX", "rag_documents"),
		ChunkIndex:     getEnv("RAG_CHUNK_INDEX", "rag_chunks"),
		ChunkSize:      getEnvInt("RAG_CHUNK_SIZE", 512),
		ChunkOverlap:   getEnvInt("RAG_CHUNK_OVERLAP", 50),
		TopK:           getEnvInt("RAG_TOP_K", 5),
		ScoreThreshold: getEnvFloat("RAG_SCORE_THRESHOLD", 0.7),
		EmbeddingDim:   getEnvInt("RAG_EMBEDDING_DIM", 768),
		EmbeddingModel: getEnv("RAG_EMBEDDING_MODEL", "sentence-transformers/all-MiniLM-L6-v2"),
	}
	
	return config
}

// 辅助函数
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvFloat(key string, defaultValue float64) float64 {
	if value := os.Getenv(key); value != "" {
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			return floatValue
		}
	}
	return defaultValue
}

func getEnvStringSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return defaultValue
}
