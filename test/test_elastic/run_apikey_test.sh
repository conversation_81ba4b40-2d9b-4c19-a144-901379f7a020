#!/bin/bash

# Elasticsearch API Key 测试脚本

set -e

echo "🚀 Elasticsearch API Key 连接测试"
echo "=================================="

# 检查环境变量
if [ -z "$ES_API_KEY" ]; then
    echo "❌ 错误: 请设置 ES_API_KEY 环境变量"
    echo ""
    echo "示例:"
    echo "export ES_API_KEY=\"VnVhQ2ZHY0JDZGJrUW0tZTVhT3g6dWkybHAyYXhUTm1zeWFrdzl0dk5udw==\""
    echo ""
    echo "如何获取 API Key:"
    echo "1. 使用 Kibana Dev Tools:"
    echo "   POST /_security/api_key"
    echo "   {"
    echo "     \"name\": \"my-api-key\","
    echo "     \"role_descriptors\": {"
    echo "       \"my_role\": {"
    echo "         \"cluster\": [\"monitor\"],"
    echo "         \"indices\": [{"
    echo "           \"names\": [\"test_*\"],"
    echo "           \"privileges\": [\"read\", \"write\", \"create_index\"]"
    echo "         }]"
    echo "       }"
    echo "     }"
    echo "   }"
    echo ""
    echo "2. 使用 curl:"
    echo "   curl -X POST \"localhost:9200/_security/api_key\" \\"
    echo "     -H \"Content-Type: application/json\" \\"
    echo "     -u elastic:your_password \\"
    echo "     -d '{\"name\": \"my-api-key\"}'"
    exit 1
fi

# 设置默认地址
if [ -z "$ES_ADDRESSES" ]; then
    export ES_ADDRESSES="http://localhost:9200"
fi

echo "📋 配置信息:"
echo "   Elasticsearch 地址: $ES_ADDRESSES"
echo "   API Key: ${ES_API_KEY:0:20}..."
echo ""

# 检查 Go 环境
if ! command -v go &> /dev/null; then
    echo "❌ 错误: 未找到 Go 环境"
    echo "请安装 Go: https://golang.org/doc/install"
    exit 1
fi

echo "🔧 安装依赖..."
go mod tidy
go get github.com/elastic/go-elasticsearch/v8

echo ""
echo "🧪 运行测试..."
echo ""

# 运行 API Key 连接测试
echo "1️⃣ 测试 API Key 连接..."
if go test -v ./test/ -run TestESAPIKeyConnection; then
    echo "✅ API Key 连接测试通过"
else
    echo "❌ API Key 连接测试失败"
fi

echo ""

# 运行示例程序
echo "2️⃣ 运行示例程序..."
if go run test/apikey_example.go; then
    echo "✅ 示例程序运行成功"
else
    echo "❌ 示例程序运行失败"
fi

echo ""

# 运行 API Key 管理测试（需要超级用户权限）
echo "3️⃣ 测试 API Key 管理功能（需要超级用户权限）..."
if [ ! -z "$ES_USERNAME" ] && [ ! -z "$ES_PASSWORD" ]; then
    echo "使用用户名: $ES_USERNAME"
    if go test -v ./test/ -run TestAPIKeyManagement; then
        echo "✅ API Key 管理测试通过"
    else
        echo "❌ API Key 管理测试失败"
    fi
else
    echo "⚠️  跳过 API Key 管理测试（需要设置 ES_USERNAME 和 ES_PASSWORD 环境变量）"
fi

echo ""
echo "🎉 测试完成!"
echo ""
echo "💡 提示:"
echo "   - 确保 Elasticsearch 正在运行"
echo "   - 确保 API Key 有足够的权限"
echo "   - 查看 test/elasticsearch_apikey_guide.md 获取更多信息"
echo ""
echo "🔗 有用的链接:"
echo "   - Elasticsearch API Key 文档: https://www.elastic.co/guide/en/elasticsearch/reference/current/security-api-create-api-key.html"
echo "   - Go Elasticsearch 客户端: https://github.com/elastic/go-elasticsearch"
