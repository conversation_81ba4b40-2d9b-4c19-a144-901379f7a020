package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/elastic/go-elasticsearch/v8"
)

// ESConfig Elasticsearch 配置
type ESConfig struct {
	Addresses []string `json:"addresses"`
	Username  string   `json:"username"`
	Password  string   `json:"password"`
	APIKey    string   `json:"api_key"`
	CloudID   string   `json:"cloud_id"`
	Timeout   int      `json:"timeout"`
}

func main() {
	// 从环境变量读取配置
	apiKey := os.Getenv("ES_API_KEY")
	addresses := os.Getenv("ES_ADDRESSES")
	
	if apiKey == "" {
		log.Fatal("请设置 ES_API_KEY 环境变量")
	}
	
	if addresses == "" {
		addresses = "http://localhost:9200"
	}

	// 创建配置
	config := ESConfig{
		Addresses: []string{addresses},
		APIKey:    apiKey,
		Timeout:   30,
	}

	// 创建客户端
	client, err := createESClient(&config)
	if err != nil {
		log.Fatalf("创建 Elasticsearch 客户端失败: %v", err)
	}

	ctx := context.Background()

	// 测试连接
	fmt.Println("测试 Elasticsearch 连接...")
	if err := testConnection(ctx, client); err != nil {
		log.Fatalf("连接测试失败: %v", err)
	}
	fmt.Println("✅ 连接成功!")

	// 获取集群信息
	fmt.Println("\n获取集群信息...")
	if err := getClusterInfo(ctx, client); err != nil {
		log.Printf("获取集群信息失败: %v", err)
	}

	// 创建测试索引
	indexName := "api_key_test_index"
	fmt.Printf("\n创建测试索引: %s\n", indexName)
	if err := createTestIndex(ctx, client, indexName); err != nil {
		log.Printf("创建索引失败: %v", err)
	} else {
		fmt.Println("✅ 索引创建成功!")
	}

	// 索引测试文档
	fmt.Println("\n索引测试文档...")
	if err := indexTestDocument(ctx, client, indexName); err != nil {
		log.Printf("索引文档失败: %v", err)
	} else {
		fmt.Println("✅ 文档索引成功!")
	}

	// 搜索文档
	fmt.Println("\n搜索文档...")
	if err := searchDocuments(ctx, client, indexName); err != nil {
		log.Printf("搜索失败: %v", err)
	}

	// 清理测试索引
	fmt.Printf("\n清理测试索引: %s\n", indexName)
	if err := deleteIndex(ctx, client, indexName); err != nil {
		log.Printf("删除索引失败: %v", err)
	} else {
		fmt.Println("✅ 索引删除成功!")
	}

	fmt.Println("\n🎉 API Key 测试完成!")
}

func createESClient(config *ESConfig) (*elasticsearch.Client, error) {
	cfg := elasticsearch.Config{
		Addresses: config.Addresses,
		APIKey:    config.APIKey,
		CloudID:   config.CloudID,
	}

	return elasticsearch.NewClient(cfg)
}

func testConnection(ctx context.Context, client *elasticsearch.Client) error {
	res, err := client.Ping(client.Ping.WithContext(ctx))
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("ping error: %s", res.Status())
	}

	return nil
}

func getClusterInfo(ctx context.Context, client *elasticsearch.Client) error {
	res, err := client.Info(client.Info.WithContext(ctx))
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("get info error: %s", res.Status())
	}

	var info map[string]interface{}
	if err := json.NewDecoder(res.Body).Decode(&info); err != nil {
		return err
	}

	fmt.Printf("集群名称: %v\n", info["cluster_name"])
	fmt.Printf("版本: %v\n", info["version"].(map[string]interface{})["number"])
	
	return nil
}

func createTestIndex(ctx context.Context, client *elasticsearch.Client, indexName string) error {
	mapping := `{
		"mappings": {
			"properties": {
				"title": {"type": "text"},
				"content": {"type": "text"},
				"timestamp": {"type": "date"},
				"tags": {"type": "keyword"}
			}
		}
	}`

	res, err := client.Indices.Create(
		indexName,
		client.Indices.Create.WithContext(ctx),
		client.Indices.Create.WithBody(fmt.Sprintf(`%s`, mapping)),
	)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("create index error: %s", res.Status())
	}

	return nil
}

func indexTestDocument(ctx context.Context, client *elasticsearch.Client, indexName string) error {
	doc := map[string]interface{}{
		"title":     "API Key 测试文档",
		"content":   "这是一个使用 API Key 连接 Elasticsearch 的测试文档",
		"timestamp": time.Now().Format(time.RFC3339),
		"tags":      []string{"test", "api-key", "elasticsearch"},
	}

	docJSON, err := json.Marshal(doc)
	if err != nil {
		return err
	}

	res, err := client.Index(
		indexName,
		fmt.Sprintf(`%s`, docJSON),
		client.Index.WithContext(ctx),
		client.Index.WithDocumentID("1"),
		client.Index.WithRefresh("true"),
	)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("index document error: %s", res.Status())
	}

	return nil
}

func searchDocuments(ctx context.Context, client *elasticsearch.Client, indexName string) error {
	query := `{
		"query": {
			"match": {
				"content": "API Key"
			}
		}
	}`

	res, err := client.Search(
		client.Search.WithContext(ctx),
		client.Search.WithIndex(indexName),
		client.Search.WithBody(fmt.Sprintf(`%s`, query)),
	)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("search error: %s", res.Status())
	}

	var result map[string]interface{}
	if err := json.NewDecoder(res.Body).Decode(&result); err != nil {
		return err
	}

	hits := result["hits"].(map[string]interface{})
	total := hits["total"].(map[string]interface{})
	
	fmt.Printf("找到 %v 个文档\n", total["value"])
	
	if hitsList, ok := hits["hits"].([]interface{}); ok && len(hitsList) > 0 {
		for i, hit := range hitsList {
			hitMap := hit.(map[string]interface{})
			source := hitMap["_source"].(map[string]interface{})
			fmt.Printf("文档 %d: %v\n", i+1, source["title"])
		}
	}

	return nil
}

func deleteIndex(ctx context.Context, client *elasticsearch.Client, indexName string) error {
	res, err := client.Indices.Delete(
		[]string{indexName},
		client.Indices.Delete.WithContext(ctx),
	)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() && res.StatusCode != 404 {
		return fmt.Errorf("delete index error: %s", res.Status())
	}

	return nil
}
