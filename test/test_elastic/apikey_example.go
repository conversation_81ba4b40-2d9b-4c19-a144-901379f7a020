package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/elastic/go-elasticsearch/v8"
	"github.com/elastic/go-elasticsearch/v8/esapi"
)

// ESConfig Elasticsearch 配置
type ESConfig struct {
	Addresses              []string `json:"addresses"`
	Username               string   `json:"username"`
	Password               string   `json:"password"`
	CertificateFingerPrint string   `json:"certificate_fingerprint"`
	APIKey                 string   `json:"api_key"`
	CloudID                string   `json:"cloud_id"`
	Timeout                int      `json:"timeout"`
}

func main() {
	// 从环境变量读取配置
	apiKey := "Z1dsMHNKY0JtSE5oNHI1TWo5STk6TE1BSzdfWUYzOXpfY3MxY1UyMHBsUQ=="
	addresses := "https://**************:9200"

	// 创建配置
	config := ESConfig{
		Addresses: []string{addresses},
		Username:  "elastic",
		Password:  "123456",
		APIKey:    api<PERSON><PERSON>,
		CertificateFingerPrint: "34:79:B1:C6:D8:98:C7:59:31:27:80:AA:85:28:5A:A1:0C:F0:49:FE:BE:7C:DA:92:F7:41:09:69:15:F4:96:6C"
		Timeout:   30,
	}

	// 创建客户端
	client, err := createESClient(&config)
	if err != nil {
		log.Fatalf("创建 Elasticsearch 客户端失败: %v", err)
	}

	ctx := context.Background()

	// 测试连接
	fmt.Println("测试 Elasticsearch 连接...")
	if err := testConnection(ctx, client); err != nil {
		log.Fatalf("连接测试失败: %v", err)
	}
	fmt.Println("✅ 连接成功!")

	// 获取集群信息
	fmt.Println("\n获取集群信息...")
	if err := getClusterInfo(ctx, client); err != nil {
		log.Printf("获取集群信息失败: %v", err)
	}

	// 创建测试索引
	indexName := "api_key_test_index"
	fmt.Printf("\n创建测试索引: %s\n", indexName)
	if err := createTestIndex(ctx, client, indexName); err != nil {
		log.Printf("创建索引失败: %v", err)
	} else {
		fmt.Println("✅ 索引创建成功!")
	}

	// 索引测试文档
	fmt.Println("\n索引测试文档...")
	if err := indexTestDocument(ctx, client, indexName); err != nil {
		log.Printf("索引文档失败: %v", err)
	} else {
		fmt.Println("✅ 文档索引成功!")
	}

	// 等待索引刷新
	fmt.Println("\n等待索引刷新...")
	time.Sleep(2 * time.Second)

	// 搜索文档
	fmt.Println("搜索文档...")
	if err := searchDocuments(ctx, client, indexName); err != nil {
		log.Printf("搜索失败: %v", err)
	}

	// 清理测试索引
	fmt.Printf("\n清理测试索引: %s\n", indexName)
	if err := deleteIndex(ctx, client, indexName); err != nil {
		log.Printf("删除索引失败: %v", err)
	} else {
		fmt.Println("✅ 索引删除成功!")
	}

	fmt.Println("\n🎉 API Key 测试完成!")
}

func createESClient(config *ESConfig) (*elasticsearch.Client, error) {
	cfg := elasticsearch.Config{
		Addresses: config.Addresses,
		Username:  config.Username,
		Password:  config.Password,
	}

	return elasticsearch.NewClient(cfg)
}

func testConnection(ctx context.Context, client *elasticsearch.Client) error {
	res, err := client.Ping(client.Ping.WithContext(ctx))
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("ping error: %s", res.Status())
	}

	return nil
}

func getClusterInfo(ctx context.Context, client *elasticsearch.Client) error {
	res, err := client.Info(client.Info.WithContext(ctx))
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("get info error: %s", res.Status())
	}

	var info map[string]interface{}
	if err := json.NewDecoder(res.Body).Decode(&info); err != nil {
		return err
	}

	fmt.Printf("集群名称: %v\n", info["cluster_name"])

	// 安全地访问版本信息
	if version, ok := info["version"].(map[string]interface{}); ok {
		if number, ok := version["number"].(string); ok {
			fmt.Printf("版本: %s\n", number)
		} else {
			fmt.Println("版本: 未知")
		}
	} else {
		fmt.Println("版本: 未知")
	}

	return nil
}

func createTestIndex(ctx context.Context, client *elasticsearch.Client, indexName string) error {
	mapping := `{
		"mappings": {
			"properties": {
				"title": {"type": "text"},
				"content": {"type": "text"},
				"timestamp": {"type": "date"},
				"tags": {"type": "keyword"}
			}
		}
	}`

	req := esapi.IndicesCreateRequest{
		Index: indexName,
		Body:  strings.NewReader(mapping),
	}

	res, err := req.Do(ctx, client)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("create index error: %s", res.Status())
	}

	return nil
}

func indexTestDocument(ctx context.Context, client *elasticsearch.Client, indexName string) error {
	doc := map[string]interface{}{
		"title":     "API Key 测试文档",
		"content":   "这是一个使用 API Key 连接 Elasticsearch 的测试文档",
		"timestamp": time.Now().Format(time.RFC3339),
		"tags":      []string{"test", "api-key", "elasticsearch"},
	}

	docJSON, err := json.Marshal(doc)
	if err != nil {
		return err
	}

	req := esapi.IndexRequest{
		Index:      indexName,
		DocumentID: "1",
		Body:       strings.NewReader(string(docJSON)),
		Refresh:    "true",
	}

	res, err := req.Do(ctx, client)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("index document error: %s", res.Status())
	}

	return nil
}

func searchDocuments(ctx context.Context, client *elasticsearch.Client, indexName string) error {
	query := `{
		"query": {
			"match": {
				"content": "API Key"
			}
		}
	}`

	req := esapi.SearchRequest{
		Index: []string{indexName},
		Body:  strings.NewReader(query),
	}

	res, err := req.Do(ctx, client)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("search error: %s", res.Status())
	}

	var result map[string]interface{}
	if err := json.NewDecoder(res.Body).Decode(&result); err != nil {
		return err
	}

	hits := result["hits"].(map[string]interface{})
	total := hits["total"].(map[string]interface{})

	fmt.Printf("找到 %v 个文档\n", total["value"])

	if hitsList, ok := hits["hits"].([]interface{}); ok && len(hitsList) > 0 {
		for i, hit := range hitsList {
			hitMap := hit.(map[string]interface{})
			source := hitMap["_source"].(map[string]interface{})
			fmt.Printf("文档 %d: %v\n", i+1, source["title"])
		}
	}

	return nil
}

func deleteIndex(ctx context.Context, client *elasticsearch.Client, indexName string) error {
	req := esapi.IndicesDeleteRequest{
		Index: []string{indexName},
	}

	res, err := req.Do(ctx, client)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() && res.StatusCode != 404 {
		return fmt.Errorf("delete index error: %s", res.Status())
	}

	return nil
}
