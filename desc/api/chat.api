syntax = "v1"

import "./common.api"

type (
	// 创建对话请求
	CreateChatReq {
		CharacterID int64  `json:"character_id"` // 角色ID
	}
	// 获取对话列表请求
	GetChatsReq {
		Page     	int64  `form:"page,optional"` // 页码
		Limit    	int64  `form:"limit,optional"` // 每页数量
	}
	// 对话列表响应
	ChatListResp {
		Chats   []Chat `json:"chats"` // 对话列表 
		Total   int64  `json:"total"` // 总数
		HasMore bool   `json:"has_more"` // 是否有更多
	}
	// 获取消息列表请求
	GetChatMessagesReq {
		ID       int64 `path:"id"` // 对话ID
		Page     int64  `form:"page,optional"` // 页码
		Limit    int64  `form:"limit,optional"` // 每页数量
		SceneId  int64  `form:"scene_id,optional"` // 场景ID
	}
	// 消息列表响应
	ChatMessageListResp {
		Messages []ChatMessage `json:"messages"` // 消息列表
		Total    int64         `json:"total"` // 总数
		HasMore  bool          `json:"has_more"` // 是否有更多
	}
	// 消息信息，对应 chat_messages 表
	ChatMessage {
		ID          int64  `json:"id,optional"` // 消息ID
		Role        string `json:"role"` // user/assistant/system
		Content     string `json:"content"` // 消息内容
		Type        string `json:"type"` // 消息类型: message, image, video, audio, file
		SceneId     int64  `json:"scene_id"` // 场景ID  
		NeedUnlock  int64  `json:"need_unlock"` // 是否需要解锁 0: 不需要, 1: 需要 
		CreatedAt   string `json:"created_at,optional"` // 创建时间
	}
	// 最近对话请求
	GetChatCharacterListReq {
		Page     int64    `form:"page,optional"` // 页码
		Limit    int64    `form:"limit,optional"` // 每页数量
	}
	// 最近对话角色列表响应
	ChatCharacterListResp {
		ChatCharacterList   []ChatCharacter		`json:"chat_character_list"` // 最近对话角色列表
		Total      			int64        		`json:"total"` 				// 总数
		HasMore    			bool         		`json:"has_more"` 			// 是否有更多
	}
	// 最近对话角色信息
	ChatCharacter {
		Character   Character `json:"character"`
		LastChat    Chat   `json:"chat"` // 最近对话
	}
	// 更新对话Persona请求
	UpdateChatPersonaReq {
		ID        int64 `path:"id"` // 对话ID
		PersonaID int64  `json:"persona_id"` // 新的个性ID
	}
	// 获取对话详情请求
	GetChatReq {
		ID int64 `path:"id"` // 对话ID
	}
	// 删除对话请求
	DeleteChatReq {
		ID int64 `path:"id"` // 对话ID
	}
	// 发送消息请求
	SendMessageReq {
		ID      		int64 `path:"id"` // 对话ID
		CharacterID 	int64  `json:"character_id"` // 角色ID
		PersonaID       int64  `json:"persona_id,optional"` // 个性ID
		Content string  `json:"content"` // 消息内容
		Stream  bool    `json:"stream,default=true"` // 是否使用流式响应
	}
	// 分享对话请求
	ShareChatReq {
		ID int64 `path:"id"` // 对话ID
	}
	// 分享响应
	ShareChatResp {
		ShareURL int64 `json:"share_url"` // 分享链接
	}
	
	// 获取用户在某个角色最近对话的请求
	GetChatsByCharacterReq {
		ID int64 `path:"id"` // 角色ID
	}
	// 用户在某个角色最近对话的响应
	CharacterChatsResp {
		Character  Character   `json:"character"` //角色
		Chats	   []Chat	   `json:"chats"`     //对话列表
		Total      int64       `json:"total"` 	  // 总数
		HasMore    bool        `json:"has_more"`  // 是否有更多
	}

	// 对话场景升级请求 (后端根据当前状态确定下一场景)
	SwitchChatSceneReq {
		ID 		int64 `path:"id"` // 对话ID
		SID 	int64 `path:"sid"` // 场景ID
	}

	ChatRelationResp {
		ID                   int64  	 `json:"id"` // 对话ID
		CurrentStageName     string      `json:"current_stage"` // 场景内，当前关系的名称
		CurToNextStage       string 	 `json:"cur_to_next_stage"` // 场景内，关系的当前阶段->下一阶段描述
		Relations            []Relation  `json:"relations"` // 关系列表
	}

	Relation {
		Name  		 	string `json:"name"` // 场景内，关系的当前阶段名称
		Description  	string `json:"description"` // 场景内，关系的当前阶段描述
		IntimacyNeed    int64  `json:"intimacy_need"` // 场景内，关系的当前阶段需要亲密度
		NextStageName   string `json:"next_stage_name"` // 场景内，下一阶段的名称
	}

	UnlockMessagesReq {
		ID     int64  	 `json:"id,optional"` // 对话ID
	}
)

@server (
	group: chat
	jwt:   Auth
)
service chat-api {
	@doc "获取用户的对话列表"
	@handler GetChats
	get /chats (GetChatsReq) returns (ChatListResp)

	@doc "创建用户和角色对话"
	@handler CreateChat
	post /chat (CreateChatReq) returns (Chat)

	@doc "获取对话详情"
	@handler GetChat
	get /chat/:id (GetChatReq) returns (Chat)
	
	@doc "获取对话关系"
	@handler GetChatRelation
	get /chat/:id/relation (GetChatReq) returns (ChatRelationResp)

	@doc "删除对话"
	@handler DeleteChat
	delete /chat/:id (DeleteChatReq)

	@doc "获取对话消息"
	@handler GetChatMessages
	get /chat/:id/messages (GetChatMessagesReq) returns (ChatMessageListResp)

	@doc "发送消息(流式SSE)"
	@handler SendMessage
	post /sse-chat/:id/messages (SendMessageReq)

	@doc "发送消息(流式SSE)"
	@handler SendMessageSync
	post /sync-chat/:id/messages (SendMessageReq)

	@doc "分享对话"
	@handler ShareChat
	post /chat/:id/share (ShareChatReq) returns (ShareChatResp)

	@doc "更新对话的Persona"
	@handler UpdateChatPersona
	put /chat/:id/persona (UpdateChatPersonaReq) returns (Chat)

	@doc "获取用户的所有对话中的角色列表,并且在角色后面附加和角色的最近一次对话"
	@handler GetChatWithCharacterList
	get /chat-character/list (GetChatCharacterListReq) returns (ChatCharacterListResp)

	@doc "获取用户和角色的最近对话列表"
	@handler GetCharacterWithChats
	get /character/:id/chats (GetChatsByCharacterReq) returns (CharacterChatsResp)

	@doc "对话场景切换"
	@handler SwitchChatScene
	put /chat/:id/scene/:sid/switch (SwitchChatSceneReq) returns (Chat)

	@doc "解锁消息内容, 会对是否订阅用户进行校验。如果传入chat id则解锁单个chat中的锁定内容, 如果不传入chat id，解锁所有对话中的锁定内容 "
	@handler unlockMessages
	put /unlock/messages (UnlockMessagesReq) returns (CommonResp)

}

