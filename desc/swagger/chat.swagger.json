{"swagger": "2.0", "info": {"title": "", "version": ""}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/character/{id}/chats": {"get": {"summary": "获取用户和角色的最近对话列表", "operationId": "GetCharacterWithChats", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/CharacterChatsResp"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "tags": ["chat"], "security": [{"apiKey": []}]}}, "/chat": {"post": {"summary": "创建用户和角色对话", "operationId": "CreateChat", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/Chat"}}}, "parameters": [{"name": "body", "description": " 创建对话请求", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateChatReq"}}], "tags": ["chat"], "consumes": ["application/json"], "security": [{"apiKey": []}]}}, "/chat-character/list": {"get": {"summary": "获取用户的所有对话中的角色列表,并且在角色后面附加和角色的最近一次对话", "operationId": "GetChatWithCharacterList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ChatCharacterListResp"}}}, "parameters": [{"name": "page", "description": " 页码", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "description": " 每页数量", "in": "query", "required": false, "type": "integer", "format": "int64"}], "tags": ["chat"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/chat/{id}": {"get": {"summary": "获取对话详情", "operationId": "GetChat", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/Chat"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "tags": ["chat"], "security": [{"apiKey": []}]}, "delete": {"summary": "删除对话", "operationId": "DeleteChat", "responses": {"200": {"description": "A successful response.", "schema": {}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "tags": ["chat"], "security": [{"apiKey": []}]}}, "/chat/{id}/messages": {"get": {"summary": "获取对话消息", "operationId": "GetChatMessages", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ChatMessageListResp"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "page", "description": " 页码", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "description": " 每页数量", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "scene_id", "description": " 场景ID", "in": "query", "required": false, "type": "integer", "format": "int64"}], "tags": ["chat"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/chat/{id}/persona": {"put": {"summary": "更新对话的Persona", "operationId": "UpdateChatPersona", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/Chat"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "description": " 更新对话Persona请求", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpdateChatPersonaReq"}}], "tags": ["chat"], "consumes": ["application/json"], "security": [{"apiKey": []}]}}, "/chat/{id}/relation": {"get": {"summary": "获取对话关系", "operationId": "GetChatRelation", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ChatRelationResp"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "tags": ["chat"], "security": [{"apiKey": []}]}}, "/chat/{id}/scene/{sid}/switch": {"put": {"summary": "对话场景切换", "operationId": "SwitchChatScene", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/Chat"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "sid", "in": "path", "required": true, "type": "string"}, {"name": "body", "description": " 对话场景升级请求 (后端根据当前状态确定下一场景)", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SwitchChatSceneReq"}}], "tags": ["chat"], "security": [{"apiKey": []}]}}, "/chat/{id}/share": {"post": {"summary": "分享对话", "operationId": "ShareChat", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ShareChatResp"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "description": " 分享对话请求", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ShareChatReq"}}], "tags": ["chat"], "security": [{"apiKey": []}]}}, "/chats": {"get": {"summary": "获取用户的对话列表", "operationId": "GetChats", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ChatListResp"}}}, "parameters": [{"name": "page", "description": " 页码", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "description": " 每页数量", "in": "query", "required": false, "type": "integer", "format": "int64"}], "tags": ["chat"], "consumes": ["multipart/form-data"], "security": [{"apiKey": []}]}}, "/sse-chat/{id}/messages": {"post": {"summary": "发送消息(流式SSE)", "operationId": "SendMessage", "responses": {"200": {"description": "A successful response.", "schema": {}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "description": " 发送消息请求", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SendMessageReq"}}], "tags": ["chat"], "consumes": ["application/json"], "security": [{"apiKey": []}]}}, "/sync-chat/{id}/messages": {"post": {"summary": "发送消息(同步消息)", "operationId": "SendMessageSync", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SendMessageSyncResp"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "description": " 发送消息请求", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SendMessageReq"}}], "tags": ["chat"], "consumes": ["application/json"], "security": [{"apiKey": []}]}}, "/unlock/messages": {"put": {"summary": "解锁消息内容, 会对是否订阅用户进行校验。如果传入chat id则解锁单个chat中的锁定内容, 如果不传入chat id，解锁所有对话中的锁定内容 ", "operationId": "unlockMessages", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/CommonResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UnlockMessagesReq"}}], "tags": ["chat"], "consumes": ["application/json"], "security": [{"apiKey": []}]}}}, "definitions": {"Character": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": " 角色ID"}, "name": {"type": "string", "description": " 角色名称"}, "tagline": {"type": "string", "description": " 标语"}, "bio": {"type": "string", "description": " 角色描述"}, "greeting": {"type": "string", "description": " 打招呼语"}, "avatar_image_url": {"type": "string", "description": " 头像图片URL"}, "preview_image_url": {"type": "string", "description": " 角色预览图片URL"}, "background_image_url": {"type": "string", "description": " 背景图片URL"}, "visibility": {"type": "string", "description": " 可见性：public/private"}, "tags": {"type": "array", "items": {"type": "string"}, "description": " 标签列表"}, "is_nsfw": {"type": "boolean", "format": "boolean", "description": " 是否包含成人内容"}, "interaction_count": {"type": "integer", "format": "int64", "description": " 互动次数"}, "like_count": {"type": "integer", "format": "int64", "description": " 点赞数"}, "user_id": {"type": "integer", "format": "int64", "description": " 创建者ID"}, "created_at": {"type": "string", "description": " 创建时间"}, "chatted": {"type": "boolean", "format": "boolean", "description": " 是否聊过"}, "liked": {"type": "boolean", "format": "boolean", "description": " 是否已经点赞"}}, "title": "Character", "required": ["id", "name", "tagline", "bio", "greeting", "avatar_image_url", "preview_image_url", "background_image_url", "visibility", "tags", "is_nsfw", "interaction_count", "like_count", "user_id", "created_at"]}, "CharacterChatsResp": {"type": "object", "properties": {"character": {"$ref": "#/definitions/Character", "description": "角色"}, "chats": {"type": "array", "items": {"$ref": "#/definitions/Chat"}, "description": "对话列表"}, "total": {"type": "integer", "format": "int64", "description": " 总数"}, "has_more": {"type": "boolean", "format": "boolean", "description": " 是否有更多"}}, "title": "CharacterChatsResp", "required": ["character", "chats", "total", "has_more"]}, "Chat": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": " 对话ID"}, "user_id": {"type": "integer", "format": "int64", "description": " 用户ID"}, "character_id": {"type": "integer", "format": "int64", "description": " 角色ID"}, "persona_id": {"type": "integer", "format": "int64", "description": " 个性ID"}, "last_message": {"type": "string", "description": " 最近一条消息"}, "interaction_count": {"type": "integer", "format": "int64", "description": " 交互次数"}, "share": {"type": "boolean", "format": "boolean", "description": " 是否分享"}, "title": {"type": "string", "description": " 对话标题"}, "intimacy_level": {"type": "integer", "format": "int64", "description": " 亲密度"}, "cur_to_next_stage": {"type": "string", "description": " 场景内，关系的当前阶段->下一阶段描述"}, "scene_id": {"type": "integer", "format": "int64", "description": " 场景ID"}, "last_message_timestamp": {"type": "string", "description": " 最后消息时间戳"}, "created_at": {"type": "string", "description": " 创建时间"}}, "title": "Cha<PERSON>", "required": ["id", "user_id", "character_id", "interaction_count", "share", "cur_to_next_stage", "scene_id", "last_message_timestamp", "created_at"]}, "ChatCharacter": {"type": "object", "properties": {"character": {"$ref": "#/definitions/Character"}, "chat": {"$ref": "#/definitions/Chat", "description": " 最近对话"}}, "title": "ChatCharacter", "required": ["character", "chat"]}, "ChatCharacterListResp": {"type": "object", "properties": {"chat_character_list": {"type": "array", "items": {"$ref": "#/definitions/ChatCharacter"}, "description": " 最近对话角色列表"}, "total": {"type": "integer", "format": "int64", "description": " 总数"}, "has_more": {"type": "boolean", "format": "boolean", "description": " 是否有更多"}}, "title": "ChatCharacterListResp", "required": ["chat_character_list", "total", "has_more"]}, "ChatListResp": {"type": "object", "properties": {"chats": {"type": "array", "items": {"$ref": "#/definitions/Chat"}, "description": " 对话列表"}, "total": {"type": "integer", "format": "int64", "description": " 总数"}, "has_more": {"type": "boolean", "format": "boolean", "description": " 是否有更多"}}, "title": "ChatListResp", "required": ["chats", "total", "has_more"]}, "ChatMessage": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": " 消息ID"}, "role": {"type": "string", "description": " user/assistant/system"}, "content": {"type": "string", "description": " 消息内容"}, "type": {"type": "string", "description": " 消息类型: message, image, video, audio, file"}, "scene_id": {"type": "integer", "format": "int64", "description": " 场景ID"}, "need_unlock": {"type": "integer", "format": "int64", "description": " 是否需要解锁 0: 不需要, 1: 需要"}, "created_at": {"type": "string", "description": " 创建时间"}}, "title": "ChatMessage", "required": ["role", "content", "type", "scene_id", "need_unlock"]}, "ChatMessageListResp": {"type": "object", "properties": {"messages": {"type": "array", "items": {"$ref": "#/definitions/ChatMessage"}, "description": " 消息列表"}, "total": {"type": "integer", "format": "int64", "description": " 总数"}, "has_more": {"type": "boolean", "format": "boolean", "description": " 是否有更多"}}, "title": "ChatMessageListResp", "required": ["messages", "total", "has_more"]}, "ChatRelationResp": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": " 对话ID"}, "current_stage": {"type": "string", "description": " 场景内，当前关系的名称"}, "cur_to_next_stage": {"type": "string", "description": " 场景内，关系的当前阶段->下一阶段描述"}, "relations": {"type": "array", "items": {"$ref": "#/definitions/Relation"}, "description": " 关系列表"}}, "title": "ChatRelationResp", "required": ["id", "current_stage", "cur_to_next_stage", "relations"]}, "CommonResp": {"type": "object", "properties": {"success": {"type": "boolean", "format": "boolean"}, "message": {"type": "string"}}, "title": "CommonResp", "required": ["success"]}, "CreateChatReq": {"type": "object", "properties": {"character_id": {"type": "integer", "format": "int64", "description": " 角色ID"}}, "title": "CreateChatReq", "required": ["character_id"]}, "DeleteChatReq": {"type": "object", "title": "DeleteChatReq"}, "EventInfo": {"type": "object", "properties": {"type": {"type": "string"}, "content": {"type": "string"}}, "title": "EventInfo", "required": ["type"]}, "GetChatCharacterListReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int64", "description": " 页码"}, "limit": {"type": "integer", "format": "int64", "description": " 每页数量"}}, "title": "GetChatCharacterListReq"}, "GetChatMessagesReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int64", "description": " 页码"}, "limit": {"type": "integer", "format": "int64", "description": " 每页数量"}, "scene_id": {"type": "integer", "format": "int64", "description": " 场景ID"}}, "title": "GetChatMessagesReq"}, "GetChatReq": {"type": "object", "title": "GetChatReq"}, "GetChatsByCharacterReq": {"type": "object", "title": "GetChatsByCharacterReq"}, "GetChatsReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int64", "description": " 页码"}, "limit": {"type": "integer", "format": "int64", "description": " 每页数量"}}, "title": "GetChatsReq"}, "Relation": {"type": "object", "properties": {"name": {"type": "string", "description": " 场景内，关系的当前阶段名称"}, "description": {"type": "string", "description": " 场景内，关系的当前阶段描述"}, "intimacy_need": {"type": "integer", "format": "int64", "description": " 场景内，关系的当前阶段需要亲密度"}, "next_stage_name": {"type": "string", "description": " 场景内，下一阶段的名称"}}, "title": "Relation", "required": ["name", "description", "intimacy_need", "next_stage_name"]}, "SceneFE": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": " 场景ID"}, "name": {"type": "string", "description": " 场景名称"}, "character_id": {"type": "integer", "format": "int64", "description": " 角色ID"}, "description": {"type": "string", "description": " 场景描述"}, "background_image_url": {"type": "string", "description": " 场景背景图片URL"}, "greeting": {"type": "string", "description": " 打招呼语"}, "stage": {"type": "integer", "format": "int64", "description": " 阶段"}, "next_scene_id": {"type": "integer", "format": "int64", "description": " 下一个场景ID"}, "intimacy_max": {"type": "integer", "format": "int64", "description": " 场景最大亲密度"}, "is_default": {"type": "integer", "format": "int64", "description": " 是否默认场景"}, "is_available": {"type": "boolean", "format": "boolean", "description": " 场景是否已经解锁"}}, "title": "SceneFE", "required": ["id", "name", "character_id", "description", "background_image_url", "greeting", "stage", "next_scene_id", "intimacy_max", "is_default", "is_available"]}, "SendMessageReq": {"type": "object", "properties": {"character_id": {"type": "integer", "format": "int64", "description": " 角色ID"}, "persona_id": {"type": "integer", "format": "int64", "description": " 个性ID"}, "content": {"type": "string", "description": " 消息内容"}, "stream": {"type": "boolean", "format": "boolean", "default": "true", "description": " 是否使用流式响应"}}, "title": "SendMessageReq", "required": ["character_id", "content", "stream"]}, "SendMessageSyncResp": {"type": "object", "properties": {"message": {"$ref": "#/definitions/ChatMessage", "description": " 消息"}, "events": {"type": "array", "items": {"$ref": "#/definitions/EventInfo"}}}, "title": "SendMessageSyncResp"}, "ShareChatReq": {"type": "object", "title": "ShareChatReq"}, "ShareChatResp": {"type": "object", "properties": {"share_url": {"type": "integer", "format": "int64", "description": " 分享链接"}}, "title": "ShareChatResp", "required": ["share_url"]}, "SwitchChatSceneReq": {"type": "object", "title": "SwitchChatSceneReq"}, "UnlockMessagesReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": " 对话ID"}}, "title": "UnlockMessagesReq"}, "UpdateChatPersonaReq": {"type": "object", "properties": {"persona_id": {"type": "integer", "format": "int64", "description": " 新的个性ID"}}, "title": "UpdateChatPersonaReq", "required": ["persona_id"]}}, "securityDefinitions": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter JWT Bearer token **_only_**", "name": "Authorization", "in": "header"}}}