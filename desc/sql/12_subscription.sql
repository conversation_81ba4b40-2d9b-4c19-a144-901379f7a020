-- 1. subscription_plans (订阅计划表)
CREATE TABLE IF NOT EXISTS `subscription_plans` (
  `id` VARCHAR(50) NOT NULL COMMENT '计划的唯一标识符 (例如: basic_monthly)',
  `name` VARCHAR(100) NOT NULL COMMENT '计划显示名称',
  `features` JSON NOT NULL COMMENT '功能列表 (例如: [\"feature A\", \"feature B\"])',
  `description` VARCHAR(255) NULL COMMENT '计划描述',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '计划是否可供选择',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订阅计划基础信息';

-- 2. plan_provider_details (计划的渠道定价详情 - 新增)
--    关联计划、支付渠道和具体价格
CREATE TABLE IF NOT EXISTS `plan_provider_details` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `plan_id` VARCHAR(50) NOT NULL COMMENT '关联 subscription_plans.id',
  `provider` VARCHAR(50) NOT NULL COMMENT '支付渠道名称 (e.g., stripe, bitcoin, paypal)',
  `provider_price_id` VARCHAR(100) NULL COMMENT '支付渠道的价格ID (e.g., Stripe Price ID, nullable)',
  `price` DECIMAL(18,8) NOT NULL COMMENT '价格 (支持更多小数位给加密货币)',
  `currency` VARCHAR(10) NOT NULL COMMENT '货币代码 (e.g., USD, CNY, BTC, ETH)',
  `interval` ENUM('day', 'week', 'month', 'season', 'year', 'one_time') NOT NULL COMMENT '计费周期 (one_time 用于非订阅购买)',
  `interval_count` INT NOT NULL DEFAULT 1 COMMENT '计费间隔数量 (例如 interval=month, count=3 表示每3个月)',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '该定价选项是否可用',
  `metadata` JSON NULL COMMENT '存储渠道特定的额外信息',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_plan_provider_price` (`plan_id`, `provider`, `currency`, `interval`, `interval_count`) COMMENT '确保同计划同渠道同货币同周期的定价唯一',
  INDEX `idx_plan_id` (`plan_id`),
  INDEX `idx_provider` (`provider`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='计划在不同支付渠道的具体定价';

-- 3. subscriptions (用户订阅表 - 修改)
CREATE TABLE IF NOT EXISTS `subscriptions` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '订阅的唯一ID',
  `user_id` BIGINT NOT NULL COMMENT '关联 users.id',
  `plan_id` VARCHAR(50) NOT NULL COMMENT '关联 subscription_plans.id',
  `status` VARCHAR(30) NOT NULL COMMENT '订阅状态 (e.g., pending_payment, active, past_due, canceled, trialing)',
  `payment_provider` VARCHAR(50) NOT NULL COMMENT '当前管理此订阅的支付渠道',
  `provider_subscription_ref` VARCHAR(255) NULL COMMENT '支付渠道的订阅ID或引用 (nullable)',
  `provider_customer_ref` VARCHAR(255) NULL COMMENT '支付渠道的客户ID或引用 (nullable)',
  `current_period_start` TIMESTAMP NULL COMMENT '当前计费周期开始时间',
  `current_period_end` TIMESTAMP NULL COMMENT '当前计费周期结束时间',
  `trial_ends_at` TIMESTAMP NULL COMMENT '试用期结束时间 (如果适用)',
  `cancel_at_period_end` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否在周期结束时取消',
  `canceled_at` TIMESTAMP NULL COMMENT '实际取消时间',
  `ended_at` TIMESTAMP NULL COMMENT '订阅完全终止时间',
  `metadata` JSON NULL COMMENT '存储与此订阅相关的额外信息',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`) COMMENT '一个用户只有一个有效订阅',
  INDEX `idx_provider_subscription_ref` (`provider_subscription_ref`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户订阅信息';


-- 4. payment_transactions (支付交易记录 - 新增)
--    记录所有支付尝试，无论成功与否，无论渠道
CREATE TABLE IF NOT EXISTS `payment_transactions` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '交易的唯一ID',
  `user_id` BIGINT  NOT NULL COMMENT '关联 users.id',
  `subscription_id` BIGINT NULL COMMENT '关联 subscriptions.id (nullable)',
  `item_id` VARCHAR(100) NOT NULL COMMENT '购买的项目ID (可能是 plan_id 等)',
  `item_type` VARCHAR(50) NOT NULL COMMENT '购买的项目类型 (e.g., subscription)',
  `provider` VARCHAR(50) NOT NULL COMMENT '支付渠道名称',
  `provider_transaction_id` VARCHAR(255) NULL COMMENT '支付渠道的交易ID (e.g., Stripe Charge ID, BTC Tx Hash)',
  `status` VARCHAR(30) NOT NULL COMMENT '交易状态 (e.g., pending, requires_action, processing, succeeded, failed, refunded)',
  `amount` DECIMAL(18,8) NOT NULL COMMENT '交易金额',
  `currency` VARCHAR(10) NOT NULL COMMENT '货币代码',
  `payment_method_details` JSON NULL COMMENT '支付方式详情 (e.g., {\\"type\\":\\"card\\", \\"last4\\":\\"4242\\"}, {\\"type\\":\\"bitcoin\\", \\"address\\":\\"...", \\"tx_hash\\":\\"..."})',
  `error_message` TEXT NULL COMMENT '失败时的错误信息',
  `metadata` JSON NULL COMMENT '支付渠道返回的额外元数据',
  `open_app_order_id` VARCHAR(100) NULL COMMENT '应用方订单ID (用于与支付网关交互)',
  `idempotency_key` VARCHAR(100) NULL COMMENT '客户端提供的幂等键',
  `idempotent_response_pay_url` VARCHAR(2048) NULL COMMENT '存储的幂等响应支付URL',
  `idempotent_response_platform_order_id`  VARCHAR(100) NULL COMMENT '存储的幂等响应平台订单ID',
  `idempotent_response_transaction_id`  VARCHAR(100) NULL COMMENT '存储的幂等支付交易ID',
  `idempotent_response_open_app_order_id`  VARCHAR(100) NULL COMMENT '存储的幂等应用方订单ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `processed_at` TIMESTAMP NULL COMMENT '交易处理完成时间 (成功或失败)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id_idempotency_key` (`user_id`, `idempotency_key`) COMMENT '确保用户维度幂等键唯一',
  INDEX `idx_open_app_order_id` (`open_app_order_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_subscription_id` (`subscription_id`),
  INDEX `idx_provider_transaction_id` (`provider_transaction_id`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付交易记录';

-- 5. plan_feature_settings (计划功能设置 - 新增)
--    记录每个计划的功能设置
CREATE TABLE IF NOT EXISTS `plan_feature_settings` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `plan_id` VARCHAR(50) NOT NULL COMMENT '关联 subscription_plans.id',
  `feature_key` VARCHAR(100) NOT NULL COMMENT '程序化功能键 (应用层负责其一致性)',
  `description` VARCHAR(255) NOT NULL COMMENT '功能的描述 (必须提供)',
  `value_type` ENUM('BOOLEAN', 'INTEGER', 'STRING', 'UNLIMITED') NOT NULL COMMENT '值的类型',
  `unit` VARCHAR(50) NULL COMMENT '值的单位 (如果适用)',
  `is_enabled` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '此计划是否实际启用/包含此功能',
  `limit_value_int` INT NULL COMMENT '整数限制值',
  `limit_value_string` VARCHAR(255) NULL COMMENT '字符串限制值',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_plan_feature` (`plan_id`, `feature_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='计划的功能具体配置和限制';


CREATE TABLE IF NOT EXISTS `feature_usage_counts` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT NOT NULL,
  `plan_id_at_usage` VARCHAR(50) NOT NULL COMMENT '发生用量时用户所处的计划ID',
  `feature_key` VARCHAR(100) NOT NULL,
  `entity_id` VARCHAR(100) NULL COMMENT '相关实体ID (例如 character_id, 如果是单角色限制)',
  `usage_period_start` DATE NOT NULL COMMENT '用量统计周期开始日期 (例如: YYYY-MM-DD for daily)',
  -- `usage_period_end` DATE NOT NULL, -- 如果是固定周期，结束日期可以推算
  `count` INT NOT NULL DEFAULT 0,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_feature_entity_period` (`user_id`, `feature_key`, `entity_id`, `usage_period_start`) COMMENT '确保同一用户同一功能同一实体同一周期的计数唯一',
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_feature_key` (`feature_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='功能用量计数';