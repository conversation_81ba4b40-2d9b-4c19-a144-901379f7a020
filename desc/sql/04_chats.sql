CREATE TABLE `chats` (
	`id` bigint NOT NULL AUTO_INCREMENT,
	`user_id` bigint NOT NULL COMMENT '用户ID',
	`character_id` bigint NOT NULL COMMENT '角色ID',
	`scene_id` bigint NOT NULL COMMENT '当前场景ID',
    `available_scene_ids` json NOT NULL COMMENT '可用的场景ID列表',
    `highest_scene_id` bigint NOT NULL COMMENT '最高已达到的场景ID',
    `highest_scene_relationship_name` varchar(50) COMMENT '最高已达到的场景的人物关系名称',
    `scene_switch_intimacy_record` json COMMENT '场景切换时的亲密度记录',
	`persona_id` bigint COMMENT '用户面具人格ID',
	`last_messages` json NOT NULL  COMMENT '最后20条消息，方便加载显示', 
	`interaction_count` int NOT NULL DEFAULT 1 COMMENT '互动次数',
	`chat_summary` json COMMENT '对话总结',
	`chat_user_personality` json COMMENT '对话中的用户画像',
	`system_prompt_template` text COMMENT '对话系统提示词模板, 不会被修改',
	`chat_system_prompt` text COMMENT '对话系统提示词',
	`template_data` json COMMENT '对话模板数据，方便对话中场景内的人物关系升级时替换chat_system_prompt中相关字段',
	`prompt_version` int NOT NULL DEFAULT 1 COMMENT '对话系统提示词版本，修改系统提示词和模板数据时，版本号+1',
	`intimacy_level` int NOT NULL DEFAULT 0 COMMENT '亲密度',
	`current_relationship` json COMMENT '当前场景人物关系描述，方便对话中场景内的人物关系升级时替换chat_system_prompt中关系描述和char设定',
	`cur_to_next_stage` text COMMENT '场景内，关系的当前阶段->下一阶段描述',
	`scene_relationship` json COMMENT '场景人物关系描述，冗余scene表的relationship字段，方便对话中场景内的人物关系升级使用',
	`img_list` json COMMENT '配图图片列表，冗余scenes表的image_list字段，方便对话中场景内配图使用',
	`img_send_list` json COMMENT '已发送的配图list，已发送过不再发送,示例：[{"name":"图片1", "url":"https://example.com/image1.jpg"}, {"name":"图片2", "url":"https://example.com/image2.jpg"}]',
	`share` boolean NOT NULL DEFAULT false COMMENT '是否分享',
	`title` varchar(255) COMMENT '对话标题',
	`last_message_timestamp` timestamp NOT NULL COMMENT '最后一条消息的时间',
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	PRIMARY KEY(`id`),
	KEY `idx_user_id` (`user_id`),
	KEY `idx_character_id` (`character_id`),
	KEY `idx_user_character_idx` (`user_id`, `character_id`),
    KEY `idx_user_character_created` (`user_id`, `character_id`, `created_at`),
	KEY `idx_intimacy_level` (`intimacy_level`),
	KEY `idx_interaction_count` (`interaction_count`)
);
