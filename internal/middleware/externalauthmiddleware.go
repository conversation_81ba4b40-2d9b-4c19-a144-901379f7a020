package middleware

import (
	"bytes" // For request body
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv" // For UserID conversion
	"strings"
	"time"

	"xai.backend/internal/config"        // Your config package
	"xai.backend/internal/types"         // Your types package for errors
	"xai.backend/internal/util/ctxutil"  // For Context Keys
	"xai.backend/internal/util/signutil" // Signature utility

	// "github.com/zeromicro/go-zero/core/ctxdata" // No longer needed for CtxKeyJwtUserId
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis" // Added for Redis
	xhttp "github.com/zeromicro/x/http"
)

// GetUserInfoByTokenRequest defines the structure for the request payload to the external auth service.
type GetUserInfoByTokenRequest struct {
	AppID string `json:"appid"`
	Nonce string `json:"nonce"`
	T     string `json:"t"` // Timestamp as string
	Sign  string `json:"sign"`
	Token string `json:"token"`
}

// ExternalUserCenterResponseInfo matches the "info" part of the external service's response.
type ExternalUserCenterResponseInfo struct {
	UserID   string `json:"user_id"`
	NickName string `json:"nick_name"`
	UserType int    `json:"user_type"`
	Diamond  string `json:"diamond"`
}

// ExternalUserCenterResponseData matches the "data" part of the external service's response.
type ExternalUserCenterResponseData struct {
	Info             ExternalUserCenterResponseInfo `json:"info"`
	Token            string                         `json:"token,omitempty"`            // Optional, might be a new token
	TokenTimeoutTime string                         `json:"tokenTimeoutTime,omitempty"` // Optional
}

// ExternalUserCenterBaseResponse is a generic structure for the base response from the external service.
type ExternalUserCenterBaseResponse struct {
	Ret struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Desc string `json:"desc,omitempty"` // desc might be optional
	} `json:"ret"`
	Data json.RawMessage `json:"data,omitempty"` // Use json.RawMessage for flexible data field parsing
}

// ExternalAuthMiddleware handles authentication by calling an external user center.
type ExternalAuthMiddleware struct {
	Config      config.Config
	HTTPClient  *http.Client
	RedisClient *redis.Redis // Corrected type to redis.Redis
}

// NewExternalAuthMiddleware creates a new ExternalAuthMiddleware.
func NewExternalAuthMiddleware(c config.Config, rds *redis.Redis) *ExternalAuthMiddleware { // Corrected type to redis.Redis
	return &ExternalAuthMiddleware{
		Config: c,
		HTTPClient: &http.Client{
			Timeout: time.Duration(c.ExternalUserCenter.TimeoutSeconds) * time.Second,
		},
		RedisClient: rds,
	}
}

// Handle is the middleware function that performs the authentication.
func (m *ExternalAuthMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		logger := logx.WithContext(r.Context())

		clientToken := ""
		authHeader := r.Header.Get("Authorization")
		if authHeader != "" {
			parts := strings.Split(authHeader, " ")
			if len(parts) == 2 && strings.EqualFold(parts[0], "Bearer") {
				clientToken = parts[1]
			}
		}

		if clientToken == "" {
			logger.Errorf("Missing Authorization header")
			xhttp.JsonBaseResponseCtx(r.Context(), w, types.ErrMsgMissingAuthCredential)
			return
		}

		// 1. Try to get UserID from Redis cache
		redisKeyUserID := fmt.Sprintf("%s%s", types.RedisAuthTokenKeyUserIDPrefix, clientToken)
		redisKeyNickName := fmt.Sprintf("%s%s", types.RedisAuthTokenKeyNickNamePrefix, clientToken)
		if m.RedisClient != nil {
			cachedUserIDStr, err := m.RedisClient.Get(redisKeyUserID)
			cachedNickNameStr, err2 := m.RedisClient.Get(redisKeyNickName)
			if err == nil && err2 == nil && cachedUserIDStr != "" && cachedNickNameStr != "" { // Cache hit
				userIDInt64, parseErr := strconv.ParseInt(cachedUserIDStr, 10, 64)
				if parseErr == nil {
					logger.Infof("User ID %d (ExternalID: %s) authenticated via Redis cache (token: %s)", userIDInt64, cachedUserIDStr, clientToken)
					newCtx := context.WithValue(r.Context(), ctxutil.UserIDKey, userIDInt64)
					newCtx = context.WithValue(newCtx, ctxutil.NickNameKey, cachedNickNameStr)
					next(w, r.WithContext(newCtx))
					return
				}
				logger.Errorf("Failed to parse cached UserID '%s' from Redis: %v. Token: %s", cachedUserIDStr, parseErr, clientToken)
				// Fall through to external API if parsing fails
			} else if err != redis.Nil {
				logger.Infof("Error fetching UserID from Redis for token %s: %v. Proceeding to external auth.", clientToken, err)
				// Fall through to external API on other Redis errors too, for resilience
			} else {
				logger.Infof("Token %s not found in Redis cache. Proceeding to external auth.", clientToken)
			}
		} else {
			logger.Infof("Warning: RedisClient is nil in ExternalAuthMiddleware. Skipping cache check.") // Changed Warn to Infof with prefix
		}

		// 2. If not in cache or Redis error, call external user center
		targetURL := m.Config.ExternalUserCenter.GetUserInfoByTokenUrl
		appID := m.Config.ExternalUserCenter.AppId
		appKey := m.Config.ExternalUserCenter.AppKey // This is the AppSecret for HMAC

		if targetURL == "" || appID == "" || appKey == "" {
			logger.Errorf("targetURL: %s, appID: %s, appKey: %s", targetURL, appID, appKey)
			xhttp.JsonBaseResponseCtx(r.Context(), w, types.ErrMsgAuthServiceConfigMissing)
			return
		}

		timestampStr := signutil.GenerateTimestamp()
		nonce := signutil.GenerateNonce(30)

		paramsForSign := map[string]string{
			"appid": appID,
			"nonce": nonce,
			"t":     timestampStr,
			"token": clientToken,
		}

		// signature, err := signutil.GenerateSign(paramsForSign, appKey)
		signature := signutil.AppSign(appID, appKey, paramsForSign)

		requestPayload := GetUserInfoByTokenRequest{
			AppID: appID,
			Nonce: nonce,
			T:     timestampStr,
			Sign:  signature,
			Token: clientToken,
		}
		payloadBytes, err := json.Marshal(requestPayload)
		if err != nil {
			logger.Errorf("Failed to build request body: %v", err)
			xhttp.JsonBaseResponseCtx(r.Context(), w, types.ErrMsgAuthBuildRequestBody)
			return
		}

		req, err := http.NewRequestWithContext(r.Context(), "POST", targetURL, bytes.NewBuffer(payloadBytes))
		if err != nil {
			logger.Errorf("Failed to create request: %v", err)
			xhttp.JsonBaseResponseCtx(r.Context(), w, types.ErrMsgAuthCreateRequestFailed)
			return
		}
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("token", clientToken)

		resp, err := m.HTTPClient.Do(req)
		if err != nil {
			logger.Errorf("Failed to send request: %v", err)
			xhttp.JsonBaseResponseCtx(r.Context(), w, types.ErrMsgAuthServiceComFailed)
			return
		}
		defer resp.Body.Close()

		respBodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			logger.Errorf("Failed to read response: %v", err)
			xhttp.JsonBaseResponseCtx(r.Context(), w, types.ErrMsgAuthReadResponseFailed)
			return
		}

		if resp.StatusCode != http.StatusOK {
			logger.Errorf("External auth service returned non-200 status (HTTP %d)", resp.StatusCode)
			xhttp.JsonBaseResponseCtx(r.Context(), w, types.ErrMsgAuthBadGateway)
			return
		}

		var baseResponse ExternalUserCenterBaseResponse
		if err := json.Unmarshal(respBodyBytes, &baseResponse); err != nil {
			logger.Errorf("Failed to parse base response: %v", err)
			xhttp.JsonBaseResponseCtx(r.Context(), w, types.ErrMsgAuthParseBaseRespFailed)
			return
		}

		if baseResponse.Ret.Code != 0 {
			logger.Errorf("External auth service returned non-0 code: %d, msg: %s", baseResponse.Ret.Code, baseResponse.Ret.Msg)
			xhttp.JsonBaseResponseCtx(r.Context(), w, types.ErrMsgAuthExternalFailed)
			return
		}

		if len(baseResponse.Data) == 0 || string(baseResponse.Data) == "null" {
			logger.Errorf("Content missing in response")
			xhttp.JsonBaseResponseCtx(r.Context(), w, types.ErrMsgAuthDataMissing)
			return
		}

		var responseData ExternalUserCenterResponseData
		if err := json.Unmarshal(baseResponse.Data, &responseData); err != nil {
			logger.Errorf("Failed to parse user data: %v", err)
			xhttp.JsonBaseResponseCtx(r.Context(), w, types.ErrMsgAuthParseUserDataFailed)
			return
		}

		externalUserIDStr := responseData.Info.UserID
		externalNickNameStr := responseData.Info.NickName
		if externalUserIDStr == "" {
			logger.Errorf("User ID missing in response")
			xhttp.JsonBaseResponseCtx(r.Context(), w, types.ErrMsgAuthUserIDMissing)
			return
		}

		userIDInt64, err := strconv.ParseInt(externalUserIDStr, 10, 64)
		if err != nil {
			logger.Errorf("User ID format invalid: %v", err)
			xhttp.JsonBaseResponseCtx(r.Context(), w, types.ErrMsgAuthUserIDFormatInvalid)
			return
		}

		// 3. Store UserID in Redis if fetched from external API
		if m.RedisClient != nil {
			ttl := types.DefaultAuthTokenTTL
			if responseData.TokenTimeoutTime != "" {
				// Assume TokenTimeoutTime is a string representing TTL in seconds, e.g., "86400"
				ttlInSeconds, parseErr := strconv.ParseInt(responseData.TokenTimeoutTime, 10, 64)
				if parseErr == nil && ttlInSeconds > 0 {
					ttl = int(ttlInSeconds)
					logger.Infof("Using TTL from TokenTimeoutTime (parsed as seconds) for token %s: %v (raw value: '%s')", clientToken, ttl, responseData.TokenTimeoutTime)
				} else if parseErr != nil {
					logger.Infof("Warning: Failed to parse TokenTimeoutTime '%s' as seconds for token %s: %v. Using default TTL: %v", responseData.TokenTimeoutTime, clientToken, parseErr, types.DefaultAuthTokenTTL)
				} else { // ttlInSeconds <= 0
					logger.Infof("Warning: Parsed TTL in seconds from TokenTimeoutTime '%s' is not positive (%d). Using default TTL: %v", responseData.TokenTimeoutTime, ttlInSeconds, types.DefaultAuthTokenTTL)
				}
			} else {
				logger.Infof("TokenTimeoutTime not provided for token %s. Using default TTL: %v", clientToken, types.DefaultAuthTokenTTL)
			}

			err = m.RedisClient.Setex(redisKeyUserID, externalUserIDStr, ttl)
			if err != nil {
				logger.Errorf("Failed to store UserID in Redis for token %s: %v", clientToken, err)
			} else {
				logger.Infof("Stored UserID %s in Redis for token %s with TTL %v", externalUserIDStr, clientToken, ttl)
			}
			err = m.RedisClient.Setex(redisKeyNickName, externalNickNameStr, ttl)
			if err != nil {
				logger.Errorf("Failed to store UserName in Redis for token %s: %v", clientToken, err)
			} else {
				logger.Infof("Stored UserName %s in Redis for token %s with TTL %v", externalNickNameStr, clientToken, ttl)
			}
		}

		newCtx := context.WithValue(r.Context(), ctxutil.UserIDKey, userIDInt64)
		newCtx = context.WithValue(newCtx, ctxutil.NickNameKey, externalNickNameStr)
		logger.Infof("User %d (Nick: %s, ExternalID: %s) authenticated successfully via external user center (token: %s)",
			userIDInt64, responseData.Info.NickName, externalUserIDStr, clientToken)

		next(w, r.WithContext(newCtx))
	}
}
