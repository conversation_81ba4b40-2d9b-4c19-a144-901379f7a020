package worker

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/huandu/go-sqlbuilder"
	"github.com/jzero-io/jzero-contrib/condition"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	_04_chats "xai.backend/internal/model/04_chats"
	_12_subscription "xai.backend/internal/model/12_subscription"
	_14_llm_config "xai.backend/internal/model/14_llm_config"
	"xai.backend/internal/types"
	"xai.backend/internal/util"
	"xai.backend/internal/util/llmutil"
)

// SummaryLLMResult LLMResult 定义匹配 *outer* LLM 响应的结构
type SummaryLLMResult struct {
	Choices []struct {
		Message struct {
			Content string `json:"content"` // Changed to string to match actual response
		} `json:"message"`
	} `json:"choices"`
	// Add other top-level fields like id, object, model, usage if needed
}

// LLMContentPayload 定义匹配 *inside* 内容字符串的结构
type LLMContentPayload struct {
	Summary     string `json:"summary"`
	UserProfile string `json:"user_profile"`
}

// StartSummaryGeneratorWorker 启动一个处理对话总结生成任务的 worker
func StartSummaryGeneratorWorker(
	extChatsModel _04_chats.ExtChatsModel,
	llmConfigCharacterModel _14_llm_config.LlmCharacterModelModel,
	llmConfigModel _14_llm_config.LlmConfigModel,
	subscriptionsModel _12_subscription.SubscriptionsModel,
	queue <-chan types.GenerateSummaryTask,
	redisConn *redis.Redis,
) {
	logx.Info("Starting Summary Generator Worker...")
	for task := range queue {
		processSummaryGeneration(extChatsModel, llmConfigCharacterModel, llmConfigModel, subscriptionsModel, task, redisConn)
	}
	logx.Info("Stopping Summary Generator Worker.")
}

// processSummaryGeneration 处理单个对话总结生成任务
func processSummaryGeneration(
	extChatsModel _04_chats.ExtChatsModel,
	llmConfigCharacterModel _14_llm_config.LlmCharacterModelModel,
	llmConfigModel _14_llm_config.LlmConfigModel,
	subscriptionsModel _12_subscription.SubscriptionsModel,
	task types.GenerateSummaryTask,
	redisConn *redis.Redis,
) {
	const maxRetries = types.ChatSystemPromptMaxRetries // 最大重试次数
	var retryCount int                                  // 内部重试计数器

	var processWithRetry func(
		extChatsModel _04_chats.ExtChatsModel,
		llmConfigCharacterModel _14_llm_config.LlmCharacterModelModel,
		llmConfigModel _14_llm_config.LlmConfigModel,
		task types.GenerateSummaryTask,
	)
	processWithRetry = func(
		extChatsModel _04_chats.ExtChatsModel,
		llmConfigCharacterModel _14_llm_config.LlmCharacterModelModel,
		llmConfigModel _14_llm_config.LlmConfigModel,
		task types.GenerateSummaryTask,
	) {
		ctx := context.Background()
		logx.WithContext(ctx).Infof("Processing summary generation task for chatID: %d", task.ChatID)

		summary, userProfile := generateSummaryWithLLM(ctx, task.CharacterID, task.ChatID, task.LastMessagesList, llmConfigCharacterModel, llmConfigModel)
		if summary == "" && userProfile == "" {
			logx.WithContext(ctx).Infof("SummaryWorker: LLM returned empty summary and profile for chat %d.", task.ChatID)
			return
		}

		chatInfo, err := extChatsModel.FindOne(ctx, nil, task.ChatID)
		if err != nil {
			logx.WithContext(ctx).Errorf("SummaryWorker: Failed to find chat %d: %v", task.ChatID, err)
			return
		}

		// 检查用户是否是VIP用户, 设定总结数量
		var summaryCount int
		userActivePlanID, err := GetUserActivePlanID(ctx, task.UserID, subscriptionsModel)
		if err != nil {
			logx.WithContext(ctx).Errorf("SummaryWorker: Failed to get user active plan ID for chat %d: %v", task.ChatID, err)
			return
		}
		if userActivePlanID == types.PlanIDFree {
			logx.WithContext(ctx).Infof("SummaryWorker: ChatID %d: 用户不是VIP用户，不生成总结。", task.ChatID)
			summaryCount = types.ChatSummariesCountFree
		} else {
			logx.WithContext(ctx).Infof("SummaryWorker: ChatID %d: 用户是VIP用户，生成总结。", task.ChatID)
			summaryCount = types.ChatSummariesVIPCount
		}

		// --- TemplateManager 设置 ---
		var tm *util.TemplateManager
		var tmErr error
		var newSystemPrompt string
		var updatedTemplateDataJsonString string

		baseSystemPrompt := chatInfo.SystemPromptTemplate.String

		tm, tmErr = util.NewTemplateManager(baseSystemPrompt)
		if tmErr != nil {
			logx.WithContext(ctx).Errorf("SummaryWorker: ChatID %d: 创建TemplateManager失败: %v. 将使用旧的提示词和模板数据。", task.ChatID, tmErr)
		} else {
			if chatInfo.TemplateData.Valid && chatInfo.TemplateData.String != "" {
				if loadErr := tm.LoadFromJson(chatInfo.TemplateData.String); loadErr != nil {
					logx.WithContext(ctx).Infof("SummaryWorker: ChatID %d: 加载现有TemplateData失败: %v. TemplateManager将使用其默认/初始数据。", task.ChatID, loadErr)
				}
			}
		}
		// --- 结束 TemplateManager 设置 ---

		templateUpdates := make(map[string]interface{}) // 准备用于 TemplateManager 的更新
		needsDBUpdate := false                          // 标记是否发生了实际的数据变化用于DB提交

		// --- 更新 ChatSummary 并准备 TemplateManager 更新 for userCharacterChatSummary ---
		var finalChatSummaryStringForDB = chatInfo.ChatSummary // 保留原始值，如果没有任何更新
		if summary != "" {
			var chatSummaryList []types.SummaryWithScene
			if chatInfo.ChatSummary.Valid && chatInfo.ChatSummary.String != "" {
				err := json.Unmarshal([]byte(chatInfo.ChatSummary.String), &chatSummaryList)
				if err != nil {
					logx.WithContext(ctx).Errorf("SummaryWorker: Failed to unmarshal existing chat summary for chat %d: %v", task.ChatID, err)
					chatSummaryList = []types.SummaryWithScene{}
				}
			}
			summary := types.SummaryWithScene{SceneID: chatInfo.SceneId, Summary: summary}

			var summaryWithinCurrentSceneList []types.SummaryWithScene
			var summaryOutsideCurrentSceneList []types.SummaryWithScene
			var summaryList []string
			// 先分两堆
			for _, s := range chatSummaryList {
				if s.SceneID == chatInfo.SceneId {
					summaryWithinCurrentSceneList = append(summaryWithinCurrentSceneList, s)
					summaryList = append(summaryList, s.Summary)
				} else {
					summaryOutsideCurrentSceneList = append(summaryOutsideCurrentSceneList, s)
				}
			}
			//先追加，如果带场景的总结已经超过40条则去掉最前面的一个
			summaryWithinCurrentSceneList = append(summaryWithinCurrentSceneList, summary)
			summaryList = append(summaryList, summary.Summary)
			if len(summaryWithinCurrentSceneList) >= summaryCount {
				summaryWithinCurrentSceneList = summaryWithinCurrentSceneList[len(summaryWithinCurrentSceneList)-summaryCount:]
				summaryList = summaryList[len(summaryList)-summaryCount:]
			}
			// 合并 summaryWithinCurrentSceneList 和 summaryOutsideCurrentSceneList
			chatSummaryList = append(summaryWithinCurrentSceneList, summaryOutsideCurrentSceneList...)
			// 准备用于DB的更新
			chatSummaryBytes, marshalErr := json.Marshal(chatSummaryList)
			if marshalErr == nil {
				finalChatSummaryStringForDB = sql.NullString{String: string(chatSummaryBytes), Valid: true}
				needsDBUpdate = true
			} else {
				logx.WithContext(ctx).Errorf("SummaryWorker: Failed to marshal chat summary list for chat %d: %v", task.ChatID, marshalErr)
			}
			// 准备用于 TemplateManager 的更新
			summaryListBytes, marshalErr := json.Marshal(summaryList)
			if marshalErr == nil {
				templateUpdates["user_character_chat_summary"] = string(summaryListBytes)
			} else {
				logx.WithContext(ctx).Errorf("SummaryWorker: Failed to marshal chat summary list for chat %d: %v", task.ChatID, marshalErr)
			}
		}

		// --- 更新 ChatUserPersonality 并准备 TemplateManager 更新 for relationshipChatSummary ---
		var finalUserProfileStringForDB = chatInfo.ChatUserPersonality // 保留原始值，如果没有任何更新
		if userProfile != "" {
			var userProfileList []types.PersonalityWithScene
			if chatInfo.ChatUserPersonality.Valid && chatInfo.ChatUserPersonality.String != "" {
				err := json.Unmarshal([]byte(chatInfo.ChatUserPersonality.String), &userProfileList)
				if err != nil {
					logx.WithContext(ctx).Errorf("SummaryWorker: Failed to unmarshal user profile list for chat %d: %v", task.ChatID, err)
					userProfileList = []types.PersonalityWithScene{}
				}
			}
			userProfile := types.PersonalityWithScene{SceneID: chatInfo.SceneId, Personality: userProfile}
			var personalityWithinCurrentSceneList []types.PersonalityWithScene
			var personalityOutsideCurrentSceneList []types.PersonalityWithScene
			var personalityList []string
			// 先分两堆
			for _, s := range userProfileList {
				if s.SceneID == chatInfo.SceneId {
					personalityWithinCurrentSceneList = append(personalityWithinCurrentSceneList, s)
					personalityList = append(personalityList, s.Personality)
				} else {
					personalityOutsideCurrentSceneList = append(personalityOutsideCurrentSceneList, s)
				}
			}
			//先追加，如果带场景的总结已经超过40条则去掉最前面的一个
			personalityWithinCurrentSceneList = append(personalityWithinCurrentSceneList, userProfile)
			personalityList = append(personalityList, userProfile.Personality)
			if len(personalityWithinCurrentSceneList) >= summaryCount {
				personalityWithinCurrentSceneList = personalityWithinCurrentSceneList[len(personalityWithinCurrentSceneList)-summaryCount:]
				personalityList = personalityList[len(personalityList)-summaryCount:]
			}
			// 合并 personalityWithinCurrentSceneList 和 personalityOutsideCurrentSceneList
			userProfileList = append(personalityWithinCurrentSceneList, personalityOutsideCurrentSceneList...)
			// 准备用于DB的更新
			userProfileBytes, marshalErr := json.Marshal(userProfileList)
			if marshalErr == nil {
				finalUserProfileStringForDB = sql.NullString{String: string(userProfileBytes), Valid: true}
				needsDBUpdate = true
			} else {
				logx.WithContext(ctx).Errorf("SummaryWorker: Failed to marshal user personality list for chat %d: %v", task.ChatID, marshalErr)
			}
			// 准备用于 TemplateManager 的更新
			personalityListBytes, marshalErr := json.Marshal(personalityList)
			if marshalErr == nil {
				templateUpdates["relationship_chat_summary"] = string(personalityListBytes)
			} else {
				logx.WithContext(ctx).Errorf("SummaryWorker: Failed to marshal user personality list for chat %d: %v", task.ChatID, marshalErr)
			}
		}

		// --- 如果 TemplateManager 可用且有更新，则生成新的系统提示词和模板数据 ---
		if tm != nil && tmErr == nil && len(templateUpdates) > 0 {
			var updateErr error
			newSystemPrompt, updateErr = tm.UpdateTemplate(templateUpdates)
			if updateErr != nil {
				logx.WithContext(ctx).Errorf("SummaryWorker: ChatID %d: 使用TemplateManager更新提示词失败: %v. 将使用旧的提示词。", task.ChatID, updateErr)
				newSystemPrompt = chatInfo.ChatSystemPrompt.String           // 回退
				updatedTemplateDataJsonString = chatInfo.TemplateData.String // 回退
			} else {
				updatedTemplateDataSnapshot := tm.GetCurrentData()
				updatedTemplateDataJsonBytes, marshalErr := json.Marshal(updatedTemplateDataSnapshot)
				if marshalErr != nil {
					logx.WithContext(ctx).Errorf("SummaryWorker: ChatID %d: Failed to marshal updated TemplateData snapshot: %v. 将使用旧的模板数据。", task.ChatID, marshalErr)
					updatedTemplateDataJsonString = chatInfo.TemplateData.String // Fallback
				} else {
					updatedTemplateDataJsonString = string(updatedTemplateDataJsonBytes)
					logx.WithContext(ctx).Infof("SummaryWorker: ChatID %d: TemplateManager 成功生成新的提示词和模板数据。", task.ChatID)
					needsDBUpdate = true // 提示词或模板数据肯定已更改
				}
			}
		} else {
			// 如果 TemplateManager 不可用或有错误，或没有模板更新，则保持现有的提示词和数据
			newSystemPrompt = chatInfo.ChatSystemPrompt.String
			updatedTemplateDataJsonString = chatInfo.TemplateData.String
		}
		// --- 结束生成新的系统提示词和模板数据 ---

		if needsDBUpdate { // 只有当有实际变化时才更新DB
			dbUpdateData := map[string]any{
				"chat_system_prompt":    newSystemPrompt,
				"template_data":         updatedTemplateDataJsonString,
				"chat_summary":          finalChatSummaryStringForDB, // 使用可能更新的总结用于DB
				"chat_user_personality": finalUserProfileStringForDB, // 使用可能更新的画像用于DB
				"prompt_version":        sqlbuilder.Raw("prompt_version + 1"),
			}

			rowsAffected, err := extChatsModel.UpdateFieldsByConditionWithRowsAffected(ctx, nil, dbUpdateData,
				condition.Condition{Field: "id", Operator: condition.Equal, Value: task.ChatID},
				condition.Condition{Field: "prompt_version", Operator: condition.Equal, Value: chatInfo.PromptVersion})

			if err != nil {
				logx.WithContext(ctx).Errorf("SummaryWorker: Failed to update chat info %d: %v. RowsAffected: %d", task.ChatID, err, rowsAffected)
				if rowsAffected == 0 {
					if retryCount >= maxRetries {
						logx.WithContext(ctx).Errorf("乐观锁重试次数超过最大限制(%d次), ChatID: %d", maxRetries, task.ChatID)
						return
					}
					retryCount++
					logx.WithContext(ctx).Infof("乐观锁失败或DB错误未更新行，第%d次重试: ChatID %d", retryCount, task.ChatID)
					processWithRetry(extChatsModel, llmConfigCharacterModel, llmConfigModel, task)
					return
				}
				// 如果错误但受影响的行数大于0，则是一个不同的DB错误。不重试。
				return
			}

			if rowsAffected == 0 { // 没有DB错误，但受影响的行数为0
				if retryCount >= maxRetries {
					logx.WithContext(ctx).Errorf("乐观锁重试次数超过最大限制(%d次) (0行受影响), ChatID: %d", maxRetries, task.ChatID)
					return
				}
				retryCount++
				logx.WithContext(ctx).Infof("乐观锁失败 (0行受影响)，第%d次重试: ChatID %d", retryCount, task.ChatID)
				processWithRetry(extChatsModel, llmConfigCharacterModel, llmConfigModel, task)
				return
			}
			logx.WithContext(ctx).Infof("SummaryWorker: Successfully updated chat %d with new summary/profile and prompt.", task.ChatID)
			// 删除Chat的缓存
			v, err := extChatsModel.DelCache(ctx, task.ChatID)
			if err != nil {
				logx.WithContext(ctx).Errorf("SummaryWorker: Failed to delete cache for chat %d: %v", task.ChatID, err)
			} else {
				logx.WithContext(ctx).Infof("SummaryWorker: Deleted cache for chat %d, affected rows: %d", task.ChatID, v)
			}
		} else {
			logx.WithContext(ctx).Infof("SummaryWorker: No actual data changes to commit for chat %d after summary generation.", task.ChatID)
		}
	}

	// 开始第一次更新尝试
	processWithRetry(extChatsModel, llmConfigCharacterModel, llmConfigModel, task)
}

// generateSummaryWithLLM 使用LLM生成对话总结和用户画像
// 返回 (总结, 用户画像)
func generateSummaryWithLLM(ctx context.Context, characterId int64, chatId int64, lastMessagesList []types.LatestChatMessageInternal, llmConfigCharacterModel _14_llm_config.LlmCharacterModelModel, llmConfigModel _14_llm_config.LlmConfigModel) (string, string) {
	// 使用工具类获取 LLM 配置
	llmConfig, err := llmutil.GetLLMConfig(ctx, characterId, llmConfigCharacterModel, llmConfigModel)
	if err != nil {
		// GetLLMConfig 内部已打印错误日志，此处可不再重复打印或打印更上层信息
		logx.WithContext(ctx).Errorf("generateSummaryWithLLM: Failed to get LLM config for character %d: %v", characterId, err)
		return "", ""
	}

	apiUrl := llmConfig.ApiEndpoint
	apiKey := llmConfig.ApiKey
	modelName := llmConfig.ModelName

	logx.Infof("LLM API call details - URL: %s, Model: %s", apiUrl, modelName)
	// GetLLMConfig 内部已校验 ApiEndpoint 和 ModelName 是否为空

	systemInstructions := `你是一个专业的对话总结和用户画像生成专家。你会对对话内容进行总结，并生成用户画像。
## 回复设定
## 你的总结格式
你的总结 以json格式返回。 包含两个键：'summary' 和 'user_profile'。
其中个字段含义：
* summary: 对话总结，包含对话的场景，对话的双方，对话的内容，对话的情感，对话的意图，对话的结论等。
* user_profile: 用户画像，包含用户的基本信息，性格特点，兴趣爱好，生活习惯等。

### 示例
{summary: '老王和小白聊了些关于电影的话题，他们坐下一起看电影，亲密感提升，并进行亲吻和拥抱，最后在床上发生了关系', user_profile: '老王：年龄位置，很喜欢小白，热情开朗，主要特点： (1) 自信 (2) 体贴 (3)好色。 小白：年龄未知，性格温柔、娇羞但也主动。她对‘老王’表现出欢迎与回应的态度， 主要特点包括：(1) 依赖感强（2）外向型'}

## 你的总结规则
1. 必须严格按照JSON格式返回。不可以返回其他格式。
2. 除了summary和user_profile，不可以返回其他字段内容。
3. 对话总结内容必须包含对话的场景，对话的双方，对话的内容，对话的情感，对话的意图，对话的结论等。
4. 用户画像内容必须包含用户的基本信息，性格特点，兴趣爱好，生活习惯等。
5. 回复的总字数在120字以内。`
	userInstructions := "请分析以下对话内容，生成对话总结和用户画像。对话历史如下：\n"
	var dialogueHistoryBuilder strings.Builder
	dialogueHistoryBuilder.WriteString(userInstructions)
	for _, msg := range lastMessagesList {
		dialogueHistoryBuilder.WriteString(fmt.Sprintf("%s: %s\n", msg.Role, msg.Content))
	}
	dialogueHistoryString := dialogueHistoryBuilder.String()

	llmMessages := make([]types.LLMMessage, 0, 2)
	llmMessages = append(llmMessages, types.LLMMessage{Role: "system", Content: systemInstructions})
	llmMessages = append(llmMessages, types.LLMMessage{Role: "user", Content: dialogueHistoryString}) // 将整个对话历史作为一条用户消息

	payload := types.LLMRequestPayload{
		Model:    modelName,
		Messages: llmMessages,
		Stream:   false,
		Provider: map[string]interface{}{
			"quantizations": []string{"fp8"},
			"sort":          "latency",
			"only":          []string{"gmicloud", "nebius"},
		},
		Temperature:      0.7,
		TopP:             0.95,
		MaxTokens:        300,
		FrequencyPenalty: 1.15,
	}

	requestPayloadBytes, err := json.Marshal(payload)
	if err != nil {
		logx.WithContext(ctx).Errorf("generateSummaryWithLLM: Failed to marshal LLM request payload for chat %d: %v", chatId, err)
		return "", ""
	}

	logx.WithContext(ctx).Infof("LLM Request Payload: %s", string(requestPayloadBytes))

	// 使用工具类执行 HTTP 请求
	llmResp, err := llmutil.ExecuteLLMHttpRequest(ctx, apiUrl, apiKey, requestPayloadBytes)
	if err != nil {
		// ExecuteLLMHttpRequest 内部已打印错误日志
		logx.WithContext(ctx).Errorf("generateSummaryWithLLM: Error calling LLM for chat %d: %v", chatId, err)
		return "", ""
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logx.WithContext(ctx).Errorf("Failed to close response body: %v", err)
		}
	}(llmResp.Body) // <<< 确保响应体被关闭

	if llmResp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(llmResp.Body)
		logx.Errorf("LLM API request failed with status %d: %s", llmResp.StatusCode, string(bodyBytes))
		return "", ""
	}

	bodyBytes, err := io.ReadAll(llmResp.Body)
	if err != nil {
		logx.Errorf("Failed to read LLM API response body: %v", err)
		return "", ""
	}
	logx.WithContext(ctx).Infof("LLM Raw Response: %s", string(bodyBytes))

	// Step 1: Unmarshal the outer response structure
	var llmOuterResult SummaryLLMResult
	err = json.Unmarshal(bodyBytes, &llmOuterResult)
	if err != nil {
		logx.Errorf("Failed to unmarshal outer LLM response JSON: %v. Response body: %s", err, string(bodyBytes))
		return "", ""
	}

	// Check if choices exist
	if len(llmOuterResult.Choices) == 0 {
		logx.Errorf("LLM response contained no choices. Response body: %s", string(bodyBytes))
		return "", ""
	}

	// Step 2: Extract the content string and clean it
	contentString := llmOuterResult.Choices[0].Message.Content
	logx.WithContext(ctx).Infof("Extracted content string: %s", contentString)

	// Remove potential Markdown code block fences and surrounding whitespace
	jsonString := strings.TrimSpace(contentString)
	jsonString = strings.TrimPrefix(jsonString, "```json")
	jsonString = strings.TrimPrefix(jsonString, "```")
	jsonString = strings.TrimSuffix(jsonString, "```")
	jsonString = strings.TrimSpace(jsonString)
	jsonString = strings.ReplaceAll(jsonString, "\n", "")
	jsonString = strings.ReplaceAll(jsonString, "\t", "")

	logx.WithContext(ctx).Infof("Cleaned JSON string for inner parsing: %s", jsonString)

	// Step 3: Unmarshal the inner JSON string
	var llmInnerResult LLMContentPayload
	err = json.Unmarshal([]byte(jsonString), &llmInnerResult)
	if err != nil {
		logx.Errorf("Failed to unmarshal inner LLM content JSON: %v. Cleaned string: %s", err, jsonString)
		return "", ""
	}

	logx.Infof("LLM generated summary: '%s', user_profile: '%s'", llmInnerResult.Summary, llmInnerResult.UserProfile)
	return llmInnerResult.Summary, llmInnerResult.UserProfile
}

func GetUserActivePlanID(ctx context.Context, userID int64, subscriptionsModel _12_subscription.SubscriptionsModel) (string, error) {

	subscription, err := subscriptionsModel.FindOneByUserIdWithCache(ctx, nil, userID)

	if err != nil {
		// 这里的错误可能是 DB 错误，也可能是 sqlc.ErrNotFound
		if errors.Is(err, sqlc.ErrNotFound) { // 明确检查 sqlc.ErrNotFound
			logx.Infof("用户 %d 没有订阅记录，默认为 '%s' 计划", userID, types.PlanIDFree)
			return types.PlanIDFree, nil
		}
		logx.Errorf("获取用户 %d 的订阅信息失败: %v", userID, err)
		return "", fmt.Errorf("获取用户订阅信息失败: %w", err) // 保持错误包装
	}

	// 检查订阅状态和有效期
	isActive := subscription.Status == types.SubscriptionStatusActive || subscription.Status == types.SubscriptionStatusTrialing
	isExpired := subscription.CurrentPeriodEnd.Valid && subscription.CurrentPeriodEnd.Time.Before(time.Now())
	// isCancelledAndEnded := subscription.CancelAtPeriodEnd && isExpired // 更复杂的取消逻辑

	if !isActive || isExpired {
		logx.Infof("用户 %d 订阅 (ID: %d, Plan: %s, Status: %s, End: %v) 非有效状态或已过期，视为 '%s' 计划",
			userID, subscription.Id, subscription.PlanId, subscription.Status, subscription.CurrentPeriodEnd, types.PlanIDFree)
		return types.PlanIDFree, nil
	}

	logx.Infof("用户 %d 当前有效计划为: %s (订阅ID: %d)", userID, subscription.PlanId, subscription.Id)
	return subscription.PlanId, nil
}
