package handler

import (
	"net/http"

	"github.com/zeromicro/go-zero/core/logx"

	"github.com/zeromicro/go-zero/rest"
	character "xai.backend/internal/handler/character"
	chat "xai.backend/internal/handler/chat"
	like "xai.backend/internal/handler/like"
	public "xai.backend/internal/handler/public"
	referral "xai.backend/internal/handler/referral"
	subscription "xai.backend/internal/handler/subscription"
	upload "xai.backend/internal/handler/upload"
	usercenter "xai.backend/internal/handler/usercenter"
	"xai.backend/internal/middleware"
	"xai.backend/internal/svc"
	"xai.backend/internal/types"
)

// RegisterHandlers 注册所有API路由
func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	logx.Info("开始注册路由...")

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/webhook/:provider",
				Handler: subscription.HandleWebhook(serverCtx),
			},
		},
	)

	// 外部认证
	externalAuthJwtOption := middleware.NewExternalAuthMiddleware(serverCtx.Config, serverCtx.RedisConn).Handle

	// --- Character 路由 (需要认证) ---
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{externalAuthJwtOption},
			[]rest.Route{
				{
					// 获取角色的场景列表
					Method:  http.MethodGet,
					Path:    "/character/:id/scenes",
					Handler: character.GetScenes(serverCtx),
				},
				{
					// 获取角色列表-过滤
					Method:  http.MethodGet,
					Path:    "/characters/filter",
					Handler: character.GetCharactersByFilter(serverCtx),
				},
				{
					// 获取角色列表-瀑布流
					Method:  http.MethodGet,
					Path:    "/characters",
					Handler: character.GetCharacters(serverCtx),
				},
				{
					// 获取角色详情
					Method:  http.MethodGet,
					Path:    "/character/:id",
					Handler: character.GetCharacter(serverCtx),
				},
			}...,
		),
	)

	// --- Chat 路由 (需要认证) ---
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{externalAuthJwtOption},
			[]rest.Route{
				{
					// 获取对话列表
					Method:  http.MethodGet,
					Path:    "/chats",
					Handler: chat.GetChats(serverCtx),
				},
				{
					// 创建对话
					Method:  http.MethodPost,
					Path:    "/chat",
					Handler: chat.CreateChat(serverCtx),
				},
				{
					// 获取对话详情
					Method:  http.MethodGet,
					Path:    "/chat/:id",
					Handler: chat.GetChat(serverCtx),
				},
				{
					// 删除对话
					Method:  http.MethodDelete,
					Path:    "/chat/:id",
					Handler: chat.DeleteChat(serverCtx),
				},
				{
					// 获取消息列表
					Method:  http.MethodGet,
					Path:    "/chat/:id/messages",
					Handler: chat.GetChatMessages(serverCtx),
				},
				{
					// 发送消息（流式SSE）
					Method:  http.MethodPost,
					Path:    "/sse-chat/:id/messages",
					Handler: chat.SendMessage(serverCtx),
				},
				{
					//发送消息(同步消息)
					Method:  http.MethodPost,
					Path:    "/sync-chat/:id/messages",
					Handler: chat.SendMessageSync(serverCtx),
				},
				{
					// 更新对话的Persona
					Method:  http.MethodPut,
					Path:    "/chat/:id/persona",
					Handler: chat.UpdateChatPersona(serverCtx),
				},
				{
					// 获取对话关系
					Method:  http.MethodGet,
					Path:    "/chat/:id/relation",
					Handler: chat.GetChatRelation(serverCtx),
				},
				// {
				// 	// 升级对话到下一个场景
				// 	Method:  http.MethodPut,
				// 	Path:    "/chat/:id/scene/upgrade",
				// 	Handler: chat.UpgradeChatScene(serverCtx),
				// },
				{
					// 分享对话
					Method:  http.MethodPost,
					Path:    "/chat/:id/share",
					Handler: chat.ShareChat(serverCtx),
				},
				{
					// 获取用户的所有对话中的角色列表,并且在角色后面附加和角色的最近最近一次对话
					Method:  http.MethodGet,
					Path:    "/chat-character/list",
					Handler: chat.GetChatWithCharacterList(serverCtx),
				},
				{
					// 获取用户在某个角色最近对话的请求
					Method:  http.MethodGet,
					Path:    "/character/:id/chats",
					Handler: chat.GetCharacterWithChats(serverCtx),
				},
				{
					// 解锁消息内容, 会对是否订阅用户进行校验。如果传入chat id则解锁单个chat中的锁定内容, 如果不传入chat id，解锁所有对话中的锁定内容
					Method:  http.MethodPut,
					Path:    "/unlock/messages",
					Handler: chat.UnlockMessages(serverCtx),
				},
			}...,
		),
	)

	// --- 升级对话到下一个场景 (需要认证) ---
	toggleMiddleware := middleware.NewFeatureToggleMiddleware(serverCtx, types.FeatureSceneUpgradeAllow).Handle

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{externalAuthJwtOption, toggleMiddleware},
			[]rest.Route{
				{
					// 对话场景切换
					Method:  http.MethodPut,
					Path:    "/chat/:id/scene/:sid/switch",
					Handler: chat.SwitchChatScene(serverCtx),
				},
			}...,
		),
	)

	// --- Like 路由 (需要认证) ---
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{externalAuthJwtOption},
			[]rest.Route{
				{
					// 点赞角色
					Method:  http.MethodPost,
					Path:    "/characters/:id/like",
					Handler: like.LikeCharacter(serverCtx),
				},
				{
					// 取消点赞角色
					Method:  http.MethodPost,
					Path:    "/characters/:id/unlike",
					Handler: like.UnLikeCharacter(serverCtx),
				},
				{
					// 获取角色点赞状态
					Method:  http.MethodGet,
					Path:    "/characters/:id/like",
					Handler: like.GetLikeStatus(serverCtx),
				},
			}...,
		),
	)

	// --- Subscription 路由 (需要认证) ---
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{externalAuthJwtOption},
			[]rest.Route{
				{

					Method:  http.MethodGet,
					Path:    "/subscription",
					Handler: subscription.GetUserSubscription(serverCtx),
				},
				{

					Method:  http.MethodPost,
					Path:    "/subscription/initiate",
					Handler: subscription.InitiateSubscriptionPayment(serverCtx),
				},
				{

					Method:  http.MethodGet,
					Path:    "/subscription/plans",
					Handler: subscription.GetSubscriptionPlans(serverCtx),
				},
			}...,
		),
	)

	server.AddRoutes(
		[]rest.Route{
			{

				Method:  http.MethodGet,
				Path:    "/users/:userId",
				Handler: public.GetUserPublicInfo(serverCtx),
			},
			{

				Method:  http.MethodGet,
				Path:    "/users/:userId/characters",
				Handler: public.GetUserPublicCharacters(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1"),
	)

	// --- Upload 路由 (需要认证) ---
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{externalAuthJwtOption},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/image",
					Handler: upload.UploadImage(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/upload"),
	)

	// --- usercenter 需要认证的部分 (/user/) ---
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{externalAuthJwtOption},
			[]rest.Route{
				{

					Method:  http.MethodPost,
					Path:    "/user/persona",
					Handler: usercenter.CreatePersona(serverCtx),
				},
				{

					Method:  http.MethodGet,
					Path:    "/user/persona/:id",
					Handler: usercenter.GetPersona(serverCtx),
				},
				{

					Method:  http.MethodPut,
					Path:    "/user/persona/:id",
					Handler: usercenter.UpdatePersona(serverCtx),
				},
				{

					Method:  http.MethodDelete,
					Path:    "/user/persona/:id",
					Handler: usercenter.DeletePersona(serverCtx),
				},
				{

					Method:  http.MethodGet,
					Path:    "/user/personas",
					Handler: usercenter.GetPersonas(serverCtx),
				},
				{
					// 获取当前用户信息
					Method:  http.MethodGet,
					Path:    "/user/profile",
					Handler: usercenter.GetUserInfo(serverCtx),
				},
			}...,
		),
	)

	// --- user 推荐积分 需要认证的部分 (/points/) ---
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{externalAuthJwtOption},
			[]rest.Route{
				{

					Method:  http.MethodGet,
					Path:    "/points/actions",
					Handler: referral.GetPointActions(serverCtx),
				},
				{

					Method:  http.MethodGet,
					Path:    "/points/balance",
					Handler: referral.GetUserPointsAndInvitations(serverCtx),
				},
				{

					Method:  http.MethodPost,
					Path:    "/points/execute",
					Handler: referral.ExecutePointAction(serverCtx),
				},
				{

					Method:  http.MethodGet,
					Path:    "/points/ledger",
					Handler: referral.GetUserPointsLedger(serverCtx),
				},
				{

					Method:  http.MethodGet,
					Path:    "/referral",
					Handler: referral.GetUserReferralInfo(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/v1/user"),
	)

}
