package chat

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	xhttp "github.com/zeromicro/x/http"

	"xai.backend/internal/logic/chat"
	"xai.backend/internal/svc"
	"xai.backend/internal/types"
)

// SendMessageSync 发送消息(同步消息)
func SendMessageSync(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SendMessageReq
		if err := httpx.Parse(r, &req); err != nil {
			xhttp.JsonBaseResponseCtx(r.Context(), w, err)
			return
		}

		l := chat.NewSendMessageSync(r.Context(), svcCtx, r)
		resp, err := l.SendMessageSync(&req)
		if err != nil {
			xhttp.JsonBaseResponseCtx(r.Context(), w, err)
		} else {
			xhttp.JsonBaseResponseCtx(r.Context(), w, resp)
		}
	}
}
