package chat

import (
	"fmt"
	"net/http"
	"unicode/utf8"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	xhttp "github.com/zeromicro/x/http"

	"xai.backend/internal/logic/chat"
	"xai.backend/internal/svc"
	"xai.backend/internal/types"
)

// SendMessage 发送消息（流式SSE）
func SendMessage(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		logx.WithContext(r.Context()).Infof("Received request: %v", r)

		var req types.SendMessageReq
		// 先使用 httpx.Parse 解析请求 (包括 Body)
		if err := httpx.Parse(r, &req); err != nil {
			logx.WithContext(r.Context()).Errorf("Failed to parse request: %v", err)
			xhttp.JsonBaseResponseCtx(r.Context(), w, err)
			return
		}
		// --- 如果内容长度超过80个字则返回错误 ---
		if utf8.RuneCountInString(req.Content) > types.ChatMessageLengthLengthLimit {
			logx.WithContext(r.Context()).Errorf("消息内容超过限制 %d个字: %s", types.ChatMessageLengthLengthLimit, req.Content)
			xhttp.JsonBaseResponseCtx(r.Context(), w, types.ErrChatMessageContentLengthOverLimit)
			return
		}

		// --- 设置 SSE 响应头 ---
		w.Header().Set("Content-Type", "text/event-stream")
		w.Header().Set("Cache-Control", "no-cache")
		w.Header().Set("Connection", "keep-alive")
		// 可能需要设置 CORS 头
		// w.Header().Set("Access-Control-Allow-Origin", "*")

		// 获取 Flusher 接口，用于强制将缓冲数据发送给客户端
		flusher, ok := w.(http.Flusher)
		if !ok {
			logx.WithContext(r.Context()).Error("Streaming unsupported: ResponseWriter does not implement http.Flusher")
			http.Error(w, "不支持流式响应", http.StatusInternalServerError)
			return
		}

		// --- 调用 Logic 进行流式处理 ---
		l := chat.NewSendMessage(r.Context(), svcCtx, r, w)
		chunkChan := make(chan types.StreamChunk, 80) // 创建用于接收数据块的 channel

		// 在新的 goroutine 中运行 Logic 的流处理函数
		// 这样 Handler 可以继续监听 channel 并写入响应，不会阻塞
		go l.SendMessageStream(&req, chunkChan)

		// --- 监听 channel 并发送 SSE 事件 ---
		ctx := r.Context() // 获取请求上下文，用于检测客户端断开
		for {
			select {
			case <-ctx.Done(): // 客户端断开连接
				logx.WithContext(ctx).Info("Client disconnected")
				// 不需要显式关闭连接，goroutine 中的 Logic 会因为 context 取消而停止（如果它正确使用了 context）
				return // 结束 Handler

			case chunk, ok := <-chunkChan: // 从 Logic 接收数据块
				if !ok {
					// Channel 已关闭，表示 Logic 处理完成 (无论成功或失败)
					logx.WithContext(ctx).Infof("Stream channel closed for chatId: %s", req.ID)
					// 可以选择发送一个特殊的结束事件，或者直接返回
					// fmt.Fprintf(w, "event: close\ndata: Stream ended\n\n")
					// flusher.Flush()
					return // 结束 Handler
				}

				if chunk.Err != nil {
					// Logic 传递了错误
					logx.WithContext(ctx).Errorf("Error received from logic stream: %v", chunk.Err)
					// 向客户端发送错误事件 (可选)
					// 注意：这会暴露内部错误信息，生产环境应谨慎
					_, err := fmt.Fprintf(w, "event: error\ndata: {\"error\": \"%s\"}\n\n", chunk.Err.Error())
					if err != nil {
						return
					}
					//记录发送错误的chunk
					logx.Infof("send error chunk: %s", chunk.Err.Error())
					flusher.Flush()
					// 通常发生错误后也应结束流
					return // 结束 Handler
				}

				if chunk.Content != "" {
					// 发送正常的数据块
					// 格式化为 SSE 的 data 字段
					// 注意：SSE 标准要求数据中不能包含换行符，如果 content 可能包含，需要替换或编码
					// data := strings.ReplaceAll(chunk.Content, "\n", "\\n")
					_, err := fmt.Fprintf(w, "event: %s\ndata: %s\n\n", chunk.Event, chunk.Content)
					if err != nil {
						return
					}
					// 标准 SSE 格式
					//logx.Infof("send chunk: %s", chunk.Content)
					flusher.Flush() // 强制发送数据
				}

				if chunk.IsFinal {
					// Logic 发送了结束标记（虽然 channel 关闭也能表示结束）
					logx.WithContext(ctx).Infof("Received final chunk signal for chatId: %s", req.ID)
					// 可以选择发送一个特殊的结束事件
					// fmt.Fprintf(w, "event: close\ndata: Stream ended successfully\n\n")
					// flusher.Flush()
					// 正常情况下，channel 关闭后会退出循环，这里 return 可能不是必须的
					// 但如果需要在此处做清理或发送确认，可以保留
					return
				}
			}
		}
	}
}
