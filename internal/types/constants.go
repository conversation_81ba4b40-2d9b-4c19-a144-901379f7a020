package types

import "time"

// 常量
const (
	StreamChunkEventMessage              = "message"
	StreamChunkEventPromptInfo           = "prompt_info"
	StreamChunkEventIntimacyChanged      = "intimacy_changed"
	StreamChunkEventRelationChanged      = "relation_changed"
	StreamChunkEventIntimacyImage        = "intimacy_image"
	StreamChunkEventSceneUpgrade         = "scene_upgrade"
	StreamChunkEventMessagesReachedLimit = "messages_reached_limit"
	StreamChunkEventImageReachedLimit    = "image_reached_limit"
)

const (
	IntimacyChangedNoPrompt = "亲密度变化，不需要提示"
	IntimacyDownPrompt      = "亲密度下降了, 请注意调整你的行为哦"
	IntimacyUpPrompt1       = "可以尝试做一些亲密的动作, 加油哦"
	IntimacyMaxPrompt       = "我们为您准备了新场景, 是否切换?"
	MessagesLimitPrompt     = "您已经达到了每日消息限制, 请升级到VIP用户"
	ImageLimitPrompt        = "您已经达到了每日图片限制, 请升级到VIP用户"
)

const (
	ChatMessageTypeMessage = "message"
	ChatMessageTypeImage   = "image"
	ChatMessageTypeVideo   = "video"
	ChatMessageTypeAudio   = "audio"
	ChatMessageTypeFile    = "file"
)

const (
	ChatMessageNeedUnlock = 1
	ChatMessageNoUnlock   = 0
)

const (
	LockImagePath = "/characters/lock/lock.jpg"
)

// 支付提供商
const (
	PaymentProviderStripe    = "stripe"
	PaymentProviderWechatpay = "wechatpay"
	PaymentProviderAlipay    = "alipay"
	PaymentProviderBitcoin   = "bitcoin"
	PaymentProviderZhongxin  = "zhongxin"
)

// 物品类型 1:代币，2:物品，3：订阅
const (
	ItemTypeToken        = 1
	ItemTypeItem         = 2
	ItemTypeSubscription = 3
)

// 支付方式 1:众信，2:微信，3:支付宝
const (
	PaymentTypeZhongxin = 1
	PaymentTypeWechat   = 2
	PaymentTypeAlipay   = 3
)

const (
	PaymentChannelDefault = "8011"
)

// 支付状态
const (
	PaymentStatusInit    = 0 // 未发起支付
	PaymentStatusSuccess = 1 // 支付成功
	PaymentStatusFailed  = 2 // 支付失败
	PaymentStatusPending = 3 // 支付中
)

// 交付状态
const (
	DeliveryStatusInit     = 0 // 未交付
	DeliveryStatusSent     = 1 // 已交付
	DeliveryStatusReceived = 2 // 已收货
	DeliveryStatusFailed   = 3 // 交付失败
)

// plan
const (
	PlanIDFree            = "free"
	PlanIDPremiumMonthly  = "premium_monthly"
	PlanIDPremiumSeasonly = "premium_seasonly"
	PlanIDPremiumYearly   = "premium_yearly"
	PlanIDUltimateMonthly = "ultimate_monthly"
	PlanIDUltimateYearly  = "ultimate_yearly"
)

// Subscription Status (示例，根据你的 `subscriptions` 表调整)
const (
	SubscriptionStatusActive   = "active"
	SubscriptionStatusExpired  = "past_due"
	SubscriptionStatusTrialing = "trialing"
	SubscriptionStatusFree     = "free" // 代表免费计划的 plan_id
	// ... 其他状态 ...
)

const (
	SubscriptionTypeNew          = "new"
	SubscriptionTypeRenewNormal  = "renewal"
	SubscriptionTypeRenewExpired = "expired_renewal"
)

// Feature Keys - 用于 plan_feature_settings 表中的 feature_key
const (
	// --- Chat Message Features ---
	FeatureChatMessageLimitPerDayPerCharacter = "chat_message_limit_per_char_per_day" // 单角色每日消息限制
	// --- Image Generation Features ---
	FeatureImageGenerationLimitPerDay = "image_generation_limit_per_day" // 账户级别每日配图限制
	// --- Scene Features ---
	FeatureSceneUpgradeAllow = "scene_upgrade_allow" // 是否允许场景升级
	// --- Memory Features ---
	FeatureChatMemoryTokens = "chat_memory_tokens" // 聊天记忆Token数
)

// Value Types - 用于 plan_feature_settings 表中的 value_type (确保与数据库ENUM一致)
const (
	ValueTypeBoolean   = "BOOLEAN"
	ValueTypeInteger   = "INTEGER"
	ValueTypeString    = "STRING"
	ValueTypeUnlimited = "UNLIMITED"
)

// Units - 用于 plan_feature_settings 表中的 unit (确保与数据库ENUM一致或匹配)
const (
	UnitMessagesPerCharacterPerDay = "messages_per_char_per_day"
	UnitImagesPerDay               = "images_per_day"
	UnitTokens                     = "tokens"
)

// Referral Event Types
const (
	EventTypeUserReferral        = "USER_REFERRAL"
	EventTypeChannelRegistration = "CHANNEL_REGISTRATION"
)

// Referral Recipient Types
const (
	RecipientTypeReferrer          = "REFERRER"
	RecipientTypeInvitee           = "INVITEE"
	RecipientTypeChannelRegistered = "CHANNEL_REGISTERED"
)

// 积分来源类型
const (
	SourceTypeUserReferralReferrer     = "USER_REFERRAL_REFERRER"
	SourceTypeUserReferralInvitee      = "USER_REFERRAL_INVITEE"
	SourceTypeChannelRegistration      = "CHANNEL_REGISTRATION_REWARD"
	SourceTypeManualAdjustmentAdd      = "MANUAL_ADJUSTMENT_ADD"
	SourceTypeOtherReward              = "OTHER_REWARD"
	SourceTypeRedemption               = "REDEMPTION"
	SourceTypeExpirationAdjustment     = "EXPIRATION_ADJUSTMENT"
	SourceTypePenalty                  = "PENALTY"
	SourceTypeManualAdjustmentSubtract = "MANUAL_ADJUSTMENT_SUBTRACT"
	SourceTypeOtherDeduction           = "OTHER_DEDUCTION"
)

const (
	LastMessagesCount = 40
	DefaultPageSize   = 1
	DefaultLimitSize  = 10
)

const (
	ChatMessageRoleUser      = "user"
	ChatMessageRoleSystem    = "system"
	ChatMessageRoleAssistant = "assistant"
)

const (
	LLMRequestTimeout = 60 * time.Second
)

const (
	ChatSummariesVIPCount  = 32
	ChatSummariesCountFree = 16
)

const (
	ChatSystemPromptMaxRetries = 3
	// ChatMessageLengthLengthLimit 80个汉字
	ChatMessageLengthLengthLimit = 80
)

// ------------------------------------------------------------
// redis 缓存 相关常量
// ------------------------------------------------------------
const (
	FeatureUsageUpdateQueueSize   = 1000
	RedisUsageCountPrefix         = "xai:usage_count:"
	RedisUsageCountDailyExpirySec = 25 * 60 * 60 // 25 hours for daily expiry

	PlanFeatureSettingCachePrefix = "xai:plan_feat_set:" // 定义常量
	PlanFeatureSettingCacheExpiry = 90 * 24 * 60 * 60    // 定义常量
)

const (
	RedisKeyMessagesPreview         = "xai:chatprmsgs:%d"     // chatID; 存储 JSON 消息字符串列表
	RedisKeyInteractionCountPreview = "xai:chatprcount:%d"    // chatID; 存储交互计数
	RedisKeyIntimacyLevelPreview    = "xai:chatprintimacy:%d" // chatID; 存储亲密度
	RedisPreviewCacheExpiry         = 24 * time.Hour          // 预览键的示例过期时间
)

const (
	RedisAuthTokenKeyUserIDPrefix   = "xai:auth_token_uid:"
	RedisAuthTokenKeyNickNamePrefix = "xai:auth_token_nick_name:"
	DefaultAuthTokenTTL             = 24 * 60 * 60 // Default TTL of 24 hours
)

// ------------------------------------------------------------

const (
	// 每十轮消息刷新一次数据库的最近消息、交互次数
	DefaultFlushToDBInteractionThreshold = 10

	// 每20轮消息生成一次总结任务
	DefaultGenerateSummaryInterval = 20
)
