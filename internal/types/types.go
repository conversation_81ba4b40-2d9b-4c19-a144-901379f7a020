// Code generated by jzero. DO NOT EDIT.
package types

import (
	"time"
)

var (
	_ = time.Now()
)

type Character struct {
	ID                 int64    `json:"id"`                   // 角色ID
	Name               string   `json:"name"`                 // 角色名称
	Tagline            string   `json:"tagline"`              // 标语
	Bio                string   `json:"bio"`                  // 角色描述
	Greeting           string   `json:"greeting"`             // 打招呼语
	AvatarImageURL     string   `json:"avatar_image_url"`     // 头像图片URL
	PreviewImageURL    string   `json:"preview_image_url"`    // 角色预览图片URL
	BackgroundImageURL string   `json:"background_image_url"` // 背景图片URL
	Visibility         string   `json:"visibility"`           // 可见性：public/private
	Tags               []string `json:"tags"`                 // 标签列表
	IsNSFW             bool     `json:"is_nsfw"`              // 是否包含成人内容
	InteractionCount   int64    `json:"interaction_count"`    // 互动次数
	LikeCount          int64    `json:"like_count"`           // 点赞数
	Creator            int64    `json:"user_id"`              // 创建者ID
	CreatedAt          string   `json:"created_at"`           // 创建时间
	Chatted            bool     `json:"chatted,optional"`     // 是否聊过
	Liked              bool     `json:"liked,optional"`       // 是否已经点赞
}

type CharacterFilter struct {
	Search   string   `form:"search,optional"`    // 搜索关键词
	Tags     []string `form:"tags,optional"`      // 标签过滤，可以指定多个标签
	ShowNSFW bool     `form:"show_nsfw,optional"` // 是否显示成人内容
	SortBy   string   `form:"sort_by,optional"`   // 排序字段
	Page     int64    `form:"page,optional"`      // 页码
	Limit    int64    `form:"limit,optional"`     // 每页数量
}

type CharacterListResp struct {
	Characters []Character `json:"characters"` // 角色列表
	Total      int64       `json:"total"`      // 总数
	HasMore    bool        `json:"has_more"`   // 是否有更多
}

type Chat struct {
	ID                   int64  `json:"id"`                      // 对话ID
	UserID               int64  `json:"user_id"`                 // 用户ID
	CharacterID          int64  `json:"character_id"`            // 角色ID
	PersonaID            int64  `json:"persona_id,optional"`     // 个性ID
	LastMessage          string `json:"last_message,optional"`   // 最近一条消息
	InteractionCount     int64  `json:"interaction_count"`       // 交互次数
	Share                bool   `json:"share"`                   // 是否分享
	Title                string `json:"title,optional"`          // 对话标题
	IntimacyLevel        int64  `json:"intimacy_level,optional"` // 亲密度
	CurToNextStage       string `json:"cur_to_next_stage"`       // 场景内，关系的当前阶段->下一阶段描述
	SceneId              int64  `json:"scene_id"`                // 场景ID
	LastMessageTimestamp string `json:"last_message_timestamp"`  // 最后消息时间戳
	CreatedAt            string `json:"created_at"`              // 创建时间
}

type CommonResp struct {
	Success bool   `json:"success"`
	Message string `json:"message,optional"`
}

type GetCharacterReq struct {
	ID int64 `path:"id"` // 角色ID
}

type GetCharactersReq struct {
	Page  int64 `form:"page,optional"`  // 页码
	Limit int64 `form:"limit,optional"` // 每页数量
}

type GetScenesReq struct {
	ID     int64 `path:"id"`               // 角色ID
	ChatID int64 `form:"chat_id,optional"` // 当前对话的id，可选, 如果有则会标记哪些场景是可用的
}

type SceneFE struct {
	ID                 int64  `json:"id"`                   // 场景ID
	Name               string `json:"name"`                 // 场景名称
	CharacterID        int64  `json:"character_id"`         // 角色ID
	Description        string `json:"description"`          // 场景描述
	BackgroundImageURL string `json:"background_image_url"` // 场景背景图片URL
	Greeting           string `json:"greeting"`             // 打招呼语
	Stage              int64  `json:"stage"`                // 阶段
	NextSceneID        int64  `json:"next_scene_id"`        // 下一个场景ID
	IntimacyMax        int64  `json:"intimacy_max"`         // 场景最大亲密度
	IsDefault          int64  `json:"is_default"`           // 是否默认场景
	IsAvailable        bool   `json:"is_available"`         // 场景是否已经解锁
}

type SceneListRep struct {
	Scenes []SceneFE `json:"scenes"` // 角色列表
	Total  int64     `json:"total"`  // 总数
}

type CharacterChatsResp struct {
	Character Character `json:"character"` //角色
	Chats     []Chat    `json:"chats"`     //对话列表
	Total     int64     `json:"total"`     // 总数
	HasMore   bool      `json:"has_more"`  // 是否有更多
}

type ChatCharacter struct {
	Character Character `json:"character"`
	LastChat  Chat      `json:"chat"` // 最近对话
}

type ChatCharacterListResp struct {
	ChatCharacterList []ChatCharacter `json:"chat_character_list"` // 最近对话角色列表
	Total             int64           `json:"total"`               // 总数
	HasMore           bool            `json:"has_more"`            // 是否有更多
}

type ChatListResp struct {
	Chats   []Chat `json:"chats"`    // 对话列表
	Total   int64  `json:"total"`    // 总数
	HasMore bool   `json:"has_more"` // 是否有更多
}

type ChatMessage struct {
	ID         int64  `json:"id,optional"`         // 消息ID
	Role       string `json:"role"`                // user/assistant/system
	Content    string `json:"content"`             // 消息内容
	Type       string `json:"type"`                // 消息类型: message, image, video, audio, file
	SceneId    int64  `json:"scene_id"`            // 场景ID
	NeedUnlock int64  `json:"need_unlock"`         // 是否需要解锁 0: 不需要, 1: 需要
	CreatedAt  string `json:"created_at,optional"` // 创建时间
}

type ChatMessageListResp struct {
	Messages []ChatMessage `json:"messages"` // 消息列表
	Total    int64         `json:"total"`    // 总数
	HasMore  bool          `json:"has_more"` // 是否有更多
}

type ChatRelationResp struct {
	ID               int64      `json:"id"`                // 对话ID
	CurrentStageName string     `json:"current_stage"`     // 场景内，当前关系的名称
	CurToNextStage   string     `json:"cur_to_next_stage"` // 场景内，关系的当前阶段->下一阶段描述
	Relations        []Relation `json:"relations"`         // 关系列表
}

type CreateChatReq struct {
	CharacterID int64 `json:"character_id"` // 角色ID
}

type DeleteChatReq struct {
	ID int64 `path:"id"` // 对话ID
}

type EventInfo struct {
	Type    string      `json:"type"`
	Data    interface{} `json:"data,optional"`
	Message string      `json:"message,optional"`
}

type GetChatCharacterListReq struct {
	Page  int64 `form:"page,optional"`  // 页码
	Limit int64 `form:"limit,optional"` // 每页数量
}

type GetChatMessagesReq struct {
	ID      int64 `path:"id"`                // 对话ID
	Page    int64 `form:"page,optional"`     // 页码
	Limit   int64 `form:"limit,optional"`    // 每页数量
	SceneId int64 `form:"scene_id,optional"` // 场景ID
}

type GetChatReq struct {
	ID int64 `path:"id"` // 对话ID
}

type GetChatsByCharacterReq struct {
	ID int64 `path:"id"` // 角色ID
}

type GetChatsReq struct {
	Page  int64 `form:"page,optional"`  // 页码
	Limit int64 `form:"limit,optional"` // 每页数量
}

type Relation struct {
	Name          string `json:"name"`            // 场景内，关系的当前阶段名称
	Description   string `json:"description"`     // 场景内，关系的当前阶段描述
	IntimacyNeed  int64  `json:"intimacy_need"`   // 场景内，关系的当前阶段需要亲密度
	NextStageName string `json:"next_stage_name"` // 场景内，下一阶段的名称
}

type SendMessageReq struct {
	ID          int64  `path:"id"`                  // 对话ID
	CharacterID int64  `json:"character_id"`        // 角色ID
	PersonaID   int64  `json:"persona_id,optional"` // 个性ID
	Content     string `json:"content"`             // 消息内容
	Stream      bool   `json:"stream,default=true"` // 是否使用流式响应
}

type SendMessageResp struct {
	Message ChatMessage `json:"message,optional"` // 消息
	Events  []EventInfo `json:"events,optional"`
}

type ShareChatReq struct {
	ID int64 `path:"id"` // 对话ID
}

type ShareChatResp struct {
	ShareURL int64 `json:"share_url"` // 分享链接
}

type SwitchChatSceneReq struct {
	ID  int64 `path:"id"`  // 对话ID
	SID int64 `path:"sid"` // 场景ID
}

type UnlockMessagesReq struct {
	ID int64 `json:"id,optional"` // 对话ID
}

type UpdateChatPersonaReq struct {
	ID        int64 `path:"id"`         // 对话ID
	PersonaID int64 `json:"persona_id"` // 新的个性ID
}

type GetLikeStatusReq struct {
	ID int64 `path:"id"` // 角色ID
}

type LikeCharacterReq struct {
	ID int64 `path:"id"` // 角色ID
}

type LikeResp struct {
	CharacterID int64 `json:"character_id"` // 角色ID
	UserID      int64 `json:"user_id"`      // 用户ID
	IsLiked     bool  `json:"is_liked"`     // 是否已点赞
	LikeCount   int64 `json:"like_count"`   // 当前角色点赞数
}

type LikeStatus struct {
	IsLiked bool `json:"is_liked"` // 是否已点赞
}

type UnLikeCharacterReq struct {
	ID int64 `path:"id"` // 角色ID
}

type CharacterBasicInfo struct {
	Id               int64  `json:"id"`                // 角色ID
	Name             string `json:"name"`              // 角色名称
	AvatarImageUrl   string `json:"avatar_image_url"`  // 角色头像
	Tagline          string `json:"tagline"`           // 角色标语
	LikeCount        int64  `json:"like_count"`        // 点赞数
	InteractionCount int64  `json:"interaction_count"` // 互动数
}

type GetUserCharactersReq struct {
	UserId int64 `path:"userId"`                    // 用户ID
	Page   int   `json:"page,optional,default=1"`   // 页码
	Limit  int   `json:"limit,optional,default=10"` // 每页数量
}

type GetUserPublicInfoReq struct {
	UserId int64 `path:"userId"` // 用户ID
}

type UserCharacterListResp struct {
	Characters []CharacterBasicInfo `json:"characters"` // 角色列表
	Total      int64                `json:"total"`      // 总数
	HasMore    bool                 `json:"has_more"`   // 是否有更多页
}

type UserProfileBasic struct {
	Id       int64  `json:"id"`              // 用户ID
	Name     string `json:"name"`            // 用户名
	Nickname string `json:"nickname"`        // 昵称
	Avatar   string `json:"avatar,optional"` // 头像URL
	Bio      string `json:"bio,optional"`    // 个人简介
	Gender   string `json:"gender,optional"` // 性别
}

type ExecutePointActionReq struct {
	ActionCode string `json:"action_code"` // point_actions.action_code
}

type GetPointActionsResp struct {
	Actions []PointActionInfo `json:"actions"`
}

type LedgerItem struct {
	Id           int64  `json:"id"`
	PointsChange int    `json:"points_change"` // Positive or negative
	SourceType   string `json:"source_type"`   // e.g., USER_REFERRAL_INVITEE, REDEMPTION
	Description  string `json:"description,omitempty"`
	CreatedAt    string `json:"created_at"`           // ISO 8601 format, e.g., "2023-10-27T11:30:00Z"
	ExpiresAt    string `json:"expires_at,omitempty"` // ISO 8601 format or null/empty
}

type PointActionInfo struct {
	ActionCode           string `json:"action_code"`
	ActionType           string `json:"action_type"` // CONSUMPTION, PURCHASE_CREDITS, PURCHASE_SUBSCRIPTION
	Name                 string `json:"name"`
	Description          string `json:"description,optional"`
	PointCost            int    `json:"point_cost"`
	GrantedCreditsAmount *int   `json:"granted_credits_amount,optional"`
	GrantedSubPlanID     string `json:"granted_subscription_plan_id,optional"`
	GrantedSubDays       *int   `json:"granted_subscription_days,optional"`
}

type UserPointsBalanceReq struct {
}

type UserPointsBalanceResp struct {
	AvailablePoints  int64 `json:"available_points"`
	ValidInvitations int64 `json:"valid_invitations"`
}

type UserPointsLedgerReq struct {
	Page  int `form:"page,optional,default=1"`
	Limit int `form:"limit,optional,default=20"`
}

type UserPointsLedgerResp struct {
	Items   []LedgerItem `json:"items"`
	Total   int64        `json:"total"`
	Page    int          `json:"page"`
	Limit   int          `json:"limit"`
	HasMore bool         `json:"has_more"`
}

type UserReferralInfoReq struct {
}

type UserReferralInfoResp struct {
	ReferralCode string `json:"referral_code"`
	ShareLink    string `json:"share_link,omitempty"` // Optional pre-generated links
}

type InitiatePaymentReq struct {
	ProviderPriceID int64  `json:"provider_price_id"` // 用户选择的 plan_provider_details.id
	IdempotencyKey  string `json:"idempotency_key"`   // 客户端生成的唯一幂等键 (例如 UUID)
}

type InitiatePaymentResp struct {
	PayURL          string `json:"pay_url"`           // 支付链接
	PlatformOrderId string `json:"platform_order_id"` // 平台订单ID
	TransactionID   string `json:"transaction_id"`    // 支付订单ID
	OpenAppOrderId  string `json:"open_app_order_id"` //   业务方订单ID
}

type ProviderPriceInfo struct {
	ID              int64   `json:"id"`                         // plan_provider_details.id
	Provider        string  `json:"provider"`                   // 支付渠道 (stripe, bitcoin, etc.)
	ProviderPriceID string  `json:"provider_price_id,optional"` // 渠道价格ID (Stripe Price ID)
	Price           float64 `json:"price"`                      // 价格
	Currency        string  `json:"currency"`                   // 货币 (USD, BTC)
	Interval        string  `json:"interval"`                   // 周期 (month, year, one_time)
	IntervalCount   int     `json:"interval_count"`             // 周期数量
}

type SubscriptionPlan struct {
	ID                      string              `json:"id"`                        // 计划ID (e.g., basic_monthly)
	Name                    string              `json:"name"`                      // 显示名称
	Features                []string            `json:"features"`                  // 功能名称列表
	Limits                  []string            `json:"limits"`                    // 限制列表
	FeaturesJson            string              `json:"features_json"`             // 功能列表的json
	Description             string              `json:"description"`               // 描述
	AvailablePaymentOptions []ProviderPriceInfo `json:"available_payment_options"` // 可用的支付选项和价格
}

type UpdateSubscriptionReq struct {
	NewProviderPriceID int64 `json:"new_provider_price_id"` // 用户想要升级到的目标 plan_provider_details.id
}

type UserSubscription struct {
	ID                 string `json:"id"`                 // subscriptions.id
	PlanID             string `json:"plan_id"`            // 当前计划 ID
	PlanName           string `json:"plan_name,optional"` // (冗余) 当前计划名称
	Status             string `json:"status"`             // active, past_due, canceled, etc.
	PaymentProvider    string `json:"payment_provider"`   // 当前支付渠道
	CurrentPeriodStart string `json:"current_period_start,optional"`
	CurrentPeriodEnd   string `json:"current_period_end,optional"`
	TrialEndsAt        string `json:"trial_ends_at,optional"` // 试用结束时间
	CancelAtPeriodEnd  bool   `json:"cancel_at_period_end"`
	CanceledAt         string `json:"canceled_at,optional"`
	EndedAt            string `json:"ended_at,optional"`
}

type WebHookReq struct {
	Provider        string `path:"provider"`          // 支付提供商名字
	AppID           string `json:"appid"`             // APP ID
	Nonce           string `json:"nonce"`             // 随机数
	Timestamp       string `json:"t"`                 // 时间
	Sign            string `json:"sign"`              //签名
	OpenAppOrderID  string `json:"open_app_order_id"` //业务订单ID
	PlatformOrderID string `json:"platform_order_id"` //平台订单ID
	PaymentStatus   string `json:"payment_status"`    //支付状态
	FailReason      string `json:"fail_reason"`       //失败原因
	PaymentAmount   string `json:"payment_amount"`    //支付金额
	TransactionID   string `json:"transaction_id"`    //支付订单ID
	FinishTime      string `json:"finish_time"`       //完成时间
}

type WebhookResp struct {
	DeliveryOrderId string `json:"delivery_order_id"` // 交付订单ID
	DeliveryStatus  int64  `json:"delivery_status"`   // 交付状态  0: 未发货 1: 已发货 2: 已收货 3: 失败
	DeliveryTime    string `json:"delivery_time"`     // 完成时间
	FailReason      string `json:"fail_reason"`       // 失败原因
}

type UploadImageReq struct {
}

type UploadImageResp struct {
	Url string `json:"url"` // 上传成功后的图片URL
}

type CaptchaResp struct {
	CaptchaId    string `json:"captcha_id"`    // 验证码ID
	CaptchaImage string `json:"captcha_image"` // Base64编码的验证码图片
	ExpireAt     int64  `json:"expire_at"`     // 过期时间戳（秒）
}

type ChangePasswordReq struct {
	OldPassword string `json:"old_password"`
	NewPassword string `json:"new_password"`
}

type CreatePersonaReq struct {
	Name string `json:"name"`         // Persona名称
	Bio  string `json:"bio,optional"` // 描述
}

type DeletePersonaReq struct {
	ID string `path:"id"` // Persona ID
}

type EmailLoginReq struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type GetPersonaReq struct {
	ID string `path:"id"` // Persona ID
}

type GetPersonasReq struct {
	Page  int64 `form:"page,optional"`  // 页码
	Limit int64 `form:"limit,optional"` // 每页数量
}

type LoginResp struct {
	AccessToken  string   `json:"access_token"`
	RefreshToken string   `json:"refresh_token"`
	User         UserInfo `json:"user"`
}

type MobileLoginReq struct {
	Mobile     string `json:"mobile"`
	VerifyCode string `json:"verify_code,optional"`
	Password   string `json:"password,optional"`
}

type NameLoginReq struct {
	Name     string `json:"name"`
	Password string `json:"password"`
}

type PersonaInfo struct {
	Id        int64  `json:"id"`           // Persona ID
	UserId    int64  `json:"user_id"`      // 用户ID
	Name      string `json:"name"`         // Persona名称
	Bio       string `json:"bio,optional"` // 描述
	CreatedAt string `json:"created_at"`   // 创建时间
}

type PersonaListResp struct {
	Personas []PersonaInfo `json:"personas"` // Persona列表
	Total    int64         `json:"total"`    // 总数
}

type RefreshTokenReq struct {
	RefreshToken string `json:"refresh_token"`
}

type RefreshTokenResp struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresAt    int64  `json:"expires_at"`
}

type RegisterReq struct {
	Name         string `json:"name"`                  // 用户名
	Email        string `json:"email,optional"`        // 邮箱
	Mobile       string `json:"mobile,optional"`       // 手机号
	Password     string `json:"password"`              // 密码
	Nickname     string `json:"nickname,optional"`     // 昵称
	VerifyCode   string `json:"verify_code,optional"`  // 验证码
	CaptchaId    string `json:"captcha_id,optional"`   // 图片验证码ID
	CaptchaCode  string `json:"captcha_code,optional"` // 图片验证码输入
	ReferrerCode string `form:"u,optional"`            // 将 URL query param 'u' 绑定到 ReferrerCode 字段
	ChannelCode  string `form:"c,optional"`            // 将 URL query param 'c' 绑定到 ChannelCode 字段
	SourceLink   string `json:"source_link,optional"`  // 完整的来源URL
}

type ResetPasswordReq struct {
	ResetType   string `json:"reset_type"`
	Identifier  string `json:"identifier"`
	VerifyCode  string `json:"verify_code,optional"`
	CaptchaId   string `json:"captcha_id,optional"`
	CaptchaCode string `json:"captcha_code,optional"`
}

type SendVerifyCodeReq struct {
	CodeType    string `json:"code_type"`
	ReceiveType string `json:"receive_type"`
	Receiver    string `json:"receiver"`
	CaptchaId   string `json:"captcha_id,optional"`
	CaptchaCode string `json:"captcha_code,optional"`
}

type SetNewPasswordReq struct {
	Token      string `json:"token,optional"`
	ResetType  string `json:"reset_type,optional"`
	Identifier string `json:"identifier,optional"`
	VerifyCode string `json:"verify_code,optional"`
	Password   string `json:"password"`
}

type UpdatePersonaReq struct {
	ID   string `path:"id"`           // Persona ID
	Name string `json:"name"`         // Persona名称
	Bio  string `json:"bio,optional"` // 描述
}

type UpdateProfileReq struct {
	Name     string `json:"name,optional"`     // 用户名
	Email    string `json:"email,optional"`    // 邮箱
	Mobile   string `json:"mobile,optional"`   // 手机号
	Avatar   string `json:"avatar,optional"`   // 头像URL
	Bio      string `json:"bio,optional"`      // 个人简介
	Gender   string `json:"gender,optional"`   // 性别
	Nickname string `json:"nickname,optional"` // 昵称
}

type UserInfo struct {
	Id                 int64  `json:"id"`                           // 用户ID
	Name               string `json:"name"`                         // 用户名
	Email              string `json:"email,optional"`               // 邮箱
	EmailVerified      int    `json:"email_verified,optional"`      // 邮箱是否已验证
	Mobile             string `json:"mobile,optional"`              // 手机号
	Nickname           string `json:"nickname,optional"`            // 昵称
	Avatar             string `json:"avatar,optional"`              // 头像URL
	Bio                string `json:"bio,optional"`                 // 个人简介
	Gender             string `json:"gender,optional"`              // 性别
	Birthday           string `json:"birthday,optional"`            // 生日
	Status             string `json:"status"`                       // 用户状态
	SubscriptionStatus string `json:"subscription_status,optional"` // 订阅状态
	CreatedAt          string `json:"created_at"`                   // 创建时间
}

type VerifyCaptchaReq struct {
	CaptchaId   string `json:"captcha_id"`   // 验证码ID
	CaptchaCode string `json:"captcha_code"` // 用户输入的验证码
}

type VerifyEmailReq struct {
	Code string `json:"code"`
}

type VerifyResetTokenReq struct {
	Token string `json:"token"`
}
