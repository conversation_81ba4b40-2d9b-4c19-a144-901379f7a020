package types

import (
	"github.com/zeromicro/x/errors"
)

// --- 自定义业务错误码和消息 ---
// --- httpx 错误 ---
var (

	// 内部/通用错误 (Logic 层可以使用 errors.New 或 fmt.Errorf)
	// 但也可以定义一些常用的，例如
	ErrInternalServer   = errors.New(500, "服务器内部错误，请稍后重试")
	ErrBadRequest       = errors.New(400, "请求参数错误")
	ErrUnauthorized     = errors.New(401, "未授权或登录已过期")
	ErrNotFound         = errors.New(404, "请求的资源不存在")
	ErrMethodNotAllowed = errors.New(405, "请求方法不允许")
)

// BaseErrorResponse defines a common structure for error responses.
// We can use this directly or have httpx format it.
// For now, we focus on providing structured errors to httpx.Error

// Error Messages -- common error , start 2000000----
var (
	ErrMsgDBQueryFailed           = errors.New(2000001, "数据库查询失败")
	ErrMsgDBUpdateFailed          = errors.New(2000002, "数据库更新失败")
	ErrMsgDBDeleteFailed          = errors.New(2000003, "数据库删除失败")
	ErrMsgDBCreateFailed          = errors.New(2000004, "数据库创建失败")
	ErrMsgDBSelectFailed          = errors.New(2000005, "数据库选择失败")
	ErrMsgDBExecFailed            = errors.New(2000006, "数据库执行失败")
	ErrMsgDBScanFailed            = errors.New(2000007, "数据库扫描失败")
	ErrMsgDBCloseFailed           = errors.New(2000008, "数据库关闭失败")
	ErrMsgDBGetLastInsertIDFailed = errors.New(2000009, "获取最后插入ID失败")
	ErrMsgCacheMiss               = errors.New(2000010, "缓存未命中")
	ErrMsgCacheSetFailed          = errors.New(2000011, "缓存设置失败")
	ErrMsgCacheGetFailed          = errors.New(2000012, "缓存获取失败")
	ErrMsgCacheDeleteFailed       = errors.New(2000013, "缓存删除失败")
	ErrMsgCacheExpired            = errors.New(2000014, "缓存已过期")
	ErrMsgCacheInvalid            = errors.New(2000015, "缓存无效")
	ErrMsgCacheUpdateFailed       = errors.New(2000016, "缓存更新失败")
	ErrMsgJsonMarshalFailed       = errors.New(2000017, "JSON 序列化失败")
	ErrMsgJsonUnmarshalFailed     = errors.New(2000018, "JSON 反序列化失败")
	ErrMsgInvalidRequest          = errors.New(2000019, "请求无效，缺少幂等参数")
)

// Error Messages -- middleware auth , start 2001000----
var (
	ErrMsgMissingAuthCredential    = errors.New(2001001, "缺少认证凭证")
	ErrMsgMalformedAuthCredential  = errors.New(2001002, "认证凭证格式错误")
	ErrMsgAuthServiceConfigMissing = errors.New(2001003, "认证服务重要配置缺失")
	ErrMsgAuthSignatureFailed      = errors.New(2001004, "生成认证签名失败")
	ErrMsgAuthBuildRequestBody     = errors.New(2001005, "构建认证请求体失败")
	ErrMsgAuthCreateRequestFailed  = errors.New(2001006, "创建外部认证请求失败")
	ErrMsgAuthServiceComFailed     = errors.New(2001007, "外部认证服务通讯失败")
	ErrMsgAuthReadResponseFailed   = errors.New(2001008, "读取外部认证响应数据失败")
	ErrMsgAuthBadGateway           = errors.New(2001009, "外部认证服务返回非200状态")
	ErrMsgAuthParseBaseRespFailed  = errors.New(2001010, "解析外部认证基础响应失败")
	ErrMsgAuthExternalFailed       = errors.New(2001011, "外部认证失败")
	ErrMsgAuthDataMissing          = errors.New(2001012, "外部认证响应数据缺失")
	ErrMsgAuthParseUserDataFailed  = errors.New(2001013, "解析外部认证用户数据失败")
	ErrMsgAuthUserIDMissing        = errors.New(2001014, "外部认证关键用户信息(ID)缺失")
	ErrMsgAuthUserIDFormatInvalid  = errors.New(2001015, "外部认证用户ID格式无法处理")
)

// Error Messages -- Character error  start 2002000----
var (
	ErrCharacterNotFound        = errors.New(2002001, "角色不存在")
	ErrCharacterConvertFailed   = errors.New(2002002, "角色转换失败")
	ErrSceneQueryFailed         = errors.New(2002003, "场景查询失败")
	ErrDefaultSceneNotFound     = errors.New(2002004, "角色默认场景不存在")
	ErrCharacterListFailed      = errors.New(2002005, "角色列表查询失败")
	ErrCharacterQueryFailed     = errors.New(2002006, "角色查询失败")
	ErrCharacterTagsParseFailed = errors.New(2002007, "角色标签解析失败")
	ErrInvalidCharacterID       = errors.New(2002008, "无效的角色ID")
)

// Error Messages -- chat error , start 2003000----
var (
	ErrSystemPromptNotFound                 = errors.New(2003001, "系统提示词不存在")
	ErrTemplateManagerInitFailed            = errors.New(2003002, "模板服务初始化失败")
	ErrChatSystemPromptTemplateEmpty        = errors.New(2003003, "对话系统提示词模板不存在")
	ErrChatSystemPromptRenderFailed         = errors.New(2003004, "生成对话配置失败")
	ErrChatCreateFailed                     = errors.New(2003005, "创建对话失败")
	ErrChatMessageRenderFailed              = errors.New(2003006, "处理消息数据失败")
	ErrChatRelationRenderFailed             = errors.New(2003007, "处理关系数据失败")
	ErrChatNotFound                         = errors.New(2003008, "对话不存在")
	ErrChatDeleteFailed                     = errors.New(2003009, "删除对话失败")
	ErrChatPermissionCheckFailed            = errors.New(2003010, "检查对话权限失败")
	ErrChatMessageListFailed                = errors.New(2003011, "获取消息列表失败")
	ErrChatPreviewCacheFailed               = errors.New(2003012, "获取对话预览缓存失败")
	ErrChatListFailed                       = errors.New(2003013, "获取对话列表失败")
	ErrChatCharacterListFailed              = errors.New(2003014, "获取角色对话列表失败")
	ErrPersonaNotFound                      = errors.New(2003015, "面具不存在")
	ErrChatPersonaUpdateFailed              = errors.New(2003016, "更新对话人格失败")
	ErrChatPeronaUpdateMaxRetriesFailed     = errors.New(2003017, "更新对话人格失败，重试次数超过最大限制")
	ErrChatUpgradeFailed                    = errors.New(2003018, "升级对话失败")
	ErrChatUpgradeMaxRetriesFailed          = errors.New(2003019, "升级对话失败，重试次数超过最大限制")
	ErrChatQueryFailed                      = errors.New(2003020, "获取对话失败")
	ErrChatNextSceneQueryFailed             = errors.New(2003021, "获取下一个场景失败")
	ErrChatSceneQueryFailed                 = errors.New(2003022, "获取当前场景失败")
	ErrChatNewSceneRelationshipDataEmpty    = errors.New(2003023, "新场景关系数据为空")
	ErrChatNewSceneRelationshipParseFailed  = errors.New(2003024, "新场景关系数据解析失败")
	ErrChatTemplateDataLoadFailed           = errors.New(2003025, "加载当前对话配置失败")
	ErrChatSystemPromptTemplateUpdateFailed = errors.New(2003026, "生成新的对话配置失败")
	ErrChatUpgradeSceneFailed               = errors.New(2003027, "升级对话场景失败")
	ErrChatMessageContentLengthOverLimit    = errors.New(2003028, "消息内容超出长度限制")
	ErrMsgSceneCantSwitch                   = errors.New(2003029, "场景不在可切换范围内")
	ErrorInvalidMessageContent              = errors.New(2003030, "无效的消息内容")
	ErrorUserOverLimit                      = errors.New(2003031, "已经达到当前套餐的限制，请升级套餐哦")
	ErrorSystemConfigError                  = errors.New(2003032, "抱歉，服务器暂时繁忙，请稍后再试")
	ErrChatLLMMessageBuildFailed            = errors.New(2003033, "构建LLM请求体失败")
	ErrorChatSendMessageFailed              = errors.New(2003034, "发送消息失败，请稍后再试")
	ErrorLLMServiceReturnErrorStatus        = errors.New(2003035, "LLM服务返回错误状态")
)

// Error Messages -- chat send message -----
const (
	ErrInvalidChatIDFormat           = "无效的对话ID格式"
	ErrGetSendMessageUserIDFailed    = "获取发送消息用户ID失败"
	ErrGetUserActivePlanIDFailed     = "获取用户活跃套餐失败"
	ErrSystemConfigError             = "抱歉，服务器暂时繁忙，请稍后再试"
	ErrChatSendMessageFailed         = "发送消息失败，请稍后再试"
	ErrLLMServiceReturnErrorStatus   = "发送消息失败，请稍后再试"
	ErrUserOverLimit                 = "已经达到当前套餐的限制，请升级套餐哦"
	ErrInvalidMessageContent         = "无效的消息内容"
	ErrMessageContentLengthOverLimit = "消息内容超出长度限制"
	ErrChatNoPermission              = "检查对话权限失败"
)

// Error Messages -- like character , start 2004000----
var (
	ErrLikeStatusNotFound             = errors.New(2004001, "点赞记录不存在")
	ErrLikeRecordFailed               = errors.New(2004002, "记录点赞行为失败")
	ErrCharacterLikeCountQueryFailed  = errors.New(2004003, "获取角色点赞数失败")
	ErrCharacterLikeCountUpdateFailed = errors.New(2004004, "更新角色点赞数失败")
	ErrLikeDeleteFailed               = errors.New(2004005, "删除点赞行为失败")
	ErrCharacterLikeListQueryFailed   = errors.New(2004006, "获取角色点赞列表失败")
)

// Error Messages -- user center , start 2005000----
var (
	ErrGetUserIDFailed   = errors.New(2005001, "获取用户ID失败")
	ErrUserQueryFailed   = errors.New(2005002, "获取用户信息失败")
	ErrUserNotFound      = errors.New(2005003, "用户不存在") // 使用 NewCodeError 创建带 Code 的错误
	ErrUserAlreadyExists = errors.New(2005004, "用户已存在")
	ErrInvalidPassword   = errors.New(2005005, "用户名或密码错误") // 返回给前端统一用这个
	ErrUserNoEmail       = errors.New(2005006, "用户未绑定邮箱")
	ErrEmailVerified     = errors.New(2005007, "邮箱已被验证")

	// Token/验证码相关错误
	ErrTokenNotFound      = errors.New(2005008, "令牌/验证码无效或不存在")
	ErrTokenExpired       = errors.New(2005009, "令牌/验证码已过期")
	ErrInvalidIdentifier  = errors.New(2005010, "无效的标识符") // (这个可能更多是内部错误)
	ErrVerifyCode         = errors.New(2005011, "验证码错误")
	ErrGetCodeTooFrequent = errors.New(2005012, "获取验证码过于频繁") // 示例：添加频率限制错误

	// 面具相关错误
	ErrInvalidPersonaID       = errors.New(2005013, "无效的 面具 ID")
	ErrPersonaListQueryFailed = errors.New(2005014, "获取 面具 列表失败")
	ErrPersonaCreateFailed    = errors.New(2005015, "创建 面具 失败")
	ErrPersonaQueryFailed     = errors.New(2005016, "获取 面具 失败")
	ErrPersonaUpdateFailed    = errors.New(2005017, "更新 面具 失败")
	ErrPersonaDeleteFailed    = errors.New(2005018, "删除 面具 失败")

	// 积分相关错误
	ErrGetUserPointsFailed       = errors.New(2005019, "获取用户积分失败")
	ErrGetUserPointsLedgerFailed = errors.New(2005020, "获取用户积分流水记录失败")

	// 用户推荐码相关错误
	ErrUpdateUserReferralCodeFailed = errors.New(2005021, "更新用户推荐码失败")

	ErrorGetUserActivePlanIDFailed = errors.New(2005022, "获取用户活跃套餐失败")
)

// Error Messages -- subscription error , start 2006000----
var (
	ErrMsgNotSubscribed                    = errors.New(2006001, "用户未订阅")
	ErrSubscriptionPlanNotFound            = errors.New(2006002, "未找到有效的订阅来取消")
	ErrSubscriptionProviderPriceNotFound   = errors.New(2006003, "选择的支付选项无效")
	ErrSubscriptionProviderPriceInactive   = errors.New(2006004, "选择的支付选项已下架")
	ErrMsgPaymentProviderUnavailable       = errors.New(2006005, "支付服务当前不可用")
	ErrSubscriptionPaymentAlreadyProcessed = errors.New(2006006, "此支付请求已完成")
	ErrSubscriptionPaymentProcessing       = errors.New(2006007, "此支付请求正在处理中或状态异常，请稍后")
	ErrSubscriptionPaymentFailed           = errors.New(2006008, "创建支付订单失败")
	ErrSubscriptionPaymentGatewayFailed    = errors.New(2006009, "发起支付网关调用失败")
	ErrSubscriptionPaymentGatewayTimeout   = errors.New(2006010, "支付网关调用超时")
	ErrSubscriptionPaymentUpdateFailed     = errors.New(2006011, "更新支付订单状态失败")
	ErrSubscriptionPaymentInternalError    = errors.New(2006012, "内部服务器错误：未能获取支付结果")
	ErrSubscriptionPaymentInvalid          = errors.New(2006013, "无效的支付请求")
	ErrSubscriptionProviderNotFound        = errors.New(2006014, "无效的支付提供商")
	ErrWebhookProcessingFailed             = errors.New(2006015, "处理 webhook 失败")
	ErrUsageLimitExceeded                  = errors.New(2006016, "您已达到当前套餐的限制")
	ErrPermissionDenied                    = errors.New(2006017, "用户未订阅VIP，权限不足")
)
