package types // 或者放在一个更合适的位置，比如 internal/model/paytypes

import "encoding/json"

// --- Stripe Webhook Event ---
// 注意: data.object 是可变结构，这里使用 json.RawMessage 延迟解析
// 你需要在处理逻辑中根据 event.Type 来 Unmarshal data.Object 到具体的 Stripe 对象类型
// (e.g., stripe.CheckoutSession, stripe.Invoice, stripe.Subscription)
type StripeWebhookEvent struct {
	ID              string          `json:"id"`                // Event ID (用于幂等性检查)
	APIVersion      string          `json:"api_version"`       // API version used for the event
	Created         int64           `json:"created"`           // Timestamp of event creation
	Livemode        bool            `json:"livemode"`          // True if the event occurred in live mode
	Type            string          `json:"type"`              // Event type (e.g., "checkout.session.completed", "invoice.payment_succeeded")
	Data            StripeEventData `json:"data"`              // Event data payload
	Request         *StripeRequest  `json:"request,omitempty"` // Information about the request that triggered the event
	PendingWebhooks int64           `json:"pending_webhooks"`  // Number of pending webhooks for this event
}

type StripeEventData struct {
	Object             json.RawMessage        `json:"object"`                        // The actual Stripe object related to the event (Charge, Invoice, Subscription, etc.)
	PreviousAttributes map[string]interface{} `json:"previous_attributes,omitempty"` // Previous attributes of the object, for some event types
}

type StripeRequest struct {
	ID             string `json:"id"`              // Request ID
	IdempotencyKey string `json:"idempotency_key"` // Idempotency key used for the request
}

// --- Bitcoin Confirmation Callback (Generic Example) ---
// 假设有一个服务在确认支付后调用此回调
type BitcoinConfirmationCallback struct {
	InternalTransactionID string                 `json:"internal_transaction_id"` // 我们系统中的 payment_transactions.id
	BitcoinTransactionID  string                 `json:"bitcoin_transaction_id"`  // BTC 交易哈希 (txid)
	ReceivingAddress      string                 `json:"receiving_address"`       // 收款地址
	AmountReceivedBTC     string                 `json:"amount_received_btc"`     // 实际收到的 BTC 金额 (使用 string 避免精度问题)
	Confirmations         int                    `json:"confirmations"`           // 确认数
	Status                string                 `json:"status"`                  // 状态 (e.g., "confirmed", "unconfirmed", "failed")
	CallbackSignature     string                 `json:"callback_signature"`      // 用于验证回调来源的签名 (如果你的服务生成的话)
	Timestamp             int64                  `json:"timestamp"`               // 回调时间戳
	Metadata              map[string]interface{} `json:"metadata,omitempty"`      // 其他元数据
}

// --- WeChat Pay Notification (XML) ---
// 注意：这是 V2/V3 常见字段的混合示例，你需要根据实际使用的 API 版本调整。
// 需要使用 encoding/xml 进行解析。金额单位通常是分。
type WeChatPayNotification struct {
	XMLName       string `xml:"xml"` // Root element
	AppID         string `xml:"appid"`
	MchID         string `xml:"mch_id"`
	NonceStr      string `xml:"nonce_str"`
	Sign          string `xml:"sign"`
	SignType      string `xml:"sign_type,omitempty"` // V3 可能不同
	ResultCode    string `xml:"result_code"`         // "SUCCESS" or "FAIL"
	ErrCode       string `xml:"err_code,omitempty"`
	ErrCodeDes    string `xml:"err_code_des,omitempty"`
	OpenID        string `xml:"openid,omitempty"`       // 用户标识 (JSAPI/小程序支付)
	IsSubscribe   string `xml:"is_subscribe,omitempty"` // 用户是否关注公众号
	TradeType     string `xml:"trade_type"`             // JSAPI, NATIVE, APP, MWEB etc.
	BankType      string `xml:"bank_type"`              // 付款银行
	TotalFee      int64  `xml:"total_fee"`              // 订单总金额，单位为分
	CashFee       int64  `xml:"cash_fee"`               // 现金支付金额
	TransactionID string `xml:"transaction_id"`         // 微信支付订单号
	OutTradeNo    string `xml:"out_trade_no"`           // 商户订单号 (我们系统的 payment_transactions.id)
	TimeEnd       string `xml:"time_end"`               // 支付完成时间 (格式 YYYYMMDDHHMMSS)
	Attach        string `xml:"attach,omitempty"`       // 商家数据包，原样返回
	// ... 可能还有其他字段，如优惠券信息等
}

// --- Alipay Notification (Form Content) ---
// 注意：这是常见字段的示例。你需要根据具体接口和场景调整。
// 需要从 POST 的 Form Content 中解析。金额单位通常是元。
type AlipayNotification struct {
	NotifyTime     string `form:"notify_time"`                // 通知时间 (YYYY-MM-DD HH:mm:ss)
	NotifyType     string `form:"notify_type"`                // 通知类型 (e.g., trade_status_sync)
	NotifyID       string `form:"notify_id"`                  // 通知校验ID
	AppID          string `form:"app_id"`                     // 开发者的 app_id
	Charset        string `form:"charset"`                    // 编码格式 (e.g., utf-8)
	Version        string `form:"version"`                    // 接口版本
	SignType       string `form:"sign_type"`                  // 签名类型 (RSA2)
	Sign           string `form:"sign"`                       // 签名
	TradeNo        string `form:"trade_no"`                   // 支付宝交易号
	OutTradeNo     string `form:"out_trade_no"`               // 商户订单号 (我们系统的 payment_transactions.id)
	OutBizNo       string `form:"out_biz_no,omitempty"`       // 商户业务号 (可选)
	BuyerID        string `form:"buyer_id,omitempty"`         // 买家支付宝用户号
	BuyerLogonID   string `form:"buyer_logon_id,omitempty"`   // 买家支付宝登录号
	SellerID       string `form:"seller_id,omitempty"`        // 卖家支付宝用户号
	SellerEmail    string `form:"seller_email,omitempty"`     // 卖家支付宝账号
	TradeStatus    string `form:"trade_status"`               // 交易状态 (e.g., TRADE_SUCCESS, TRADE_FINISHED, TRADE_CLOSED)
	TotalAmount    string `form:"total_amount"`               // 订单金额 (元)
	ReceiptAmount  string `form:"receipt_amount,omitempty"`   // 实收金额 (元)
	InvoiceAmount  string `form:"invoice_amount,omitempty"`   // 开票金额 (元)
	BuyerPayAmount string `form:"buyer_pay_amount,omitempty"` // 付款金额 (元)
	PointAmount    string `form:"point_amount,omitempty"`     // 集分宝金额 (元)
	RefundFee      string `form:"refund_fee,omitempty"`       // 总退款金额 (元)
	Subject        string `form:"subject,omitempty"`          // 订单标题
	Body           string `form:"body,omitempty"`             // 商品描述
	GmtCreate      string `form:"gmt_create,omitempty"`       // 交易创建时间
	GmtPayment     string `form:"gmt_payment,omitempty"`      // 交易付款时间
	GmtRefund      string `form:"gmt_refund,omitempty"`       // 交易退款时间
	GmtClose       string `form:"gmt_close,omitempty"`        // 交易结束时间
	FundBillList   string `form:"fund_bill_list,omitempty"`   // 支付金额信息 (JSON 字符串)
	PassbackParams string `form:"passback_params,omitempty"`  // 公共回传参数
	// ... 可能还有其他字段
}

// ProviderInitiationResult 封装了发起支付后提供商返回的信息
type ProviderInitiationResult struct {
	PayURL          string `json:"pay_url,omitempty"` //  URL
	PlatformOrderId string `json:"platform_order_id"` // 平台订单号
	OpenAppOrderId  string `json:"open_app_order_id"` // 开放平台订单号
	TransactionID   string `json:"transaction_id"`    // 支付订单ID
}
