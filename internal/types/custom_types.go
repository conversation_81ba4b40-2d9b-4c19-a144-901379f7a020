package types

import (
	"database/sql"

	"github.com/golang-jwt/jwt/v4"
)

// 令牌自定义声明
type CustomClaims struct {
	UserId int64  `json:"userId"`
	Name   string `json:"name"`
	jwt.RegisteredClaims
}

// 定义传递给 Handler 的数据块结构, Event 为事件类型, 默认值是"stream_data"
type StreamChunk struct {
	Event   string `default:"stream_data"`
	Content string
	IsFinal bool  // 标记是否是最后一块
	Err     error // 用于传递处理过程中的错误
}

type SceneRelationship struct {
	SN                     int    `json:"sn"`
	StageName              string `json:"stage_name"`
	StageDesc              string `json:"stage_desc"`
	NextStageName          string `json:"next_stage_name"`
	RelationDesc           string `json:"relation_desc"`
	CharCSetting           string `json:"char_csetting"`
	AllowForbiddenBehavior string `json:"allow_forbidden_behavior"`
	IntimacySubMin         int64  `json:"intimacy_sub_min"`
	IntimacySubMax         int64  `json:"intimacy_sub_max"`
}

type SceneImage struct {
	Name           string `json:"name"`
	URL            string `json:"url"`
	IntimacySubMin int64  `json:"intimacy_sub_min"`
	IntimacySubMax int64  `json:"intimacy_sub_max"`
}

type SceneImageSendList struct {
	Name string `json:"name"`
	URL  string `json:"url"`
}

type UserPrompt struct {
	IntimacyLevel int    `json:"intimacy_level,omitempty"`
	UserPrompt    string `json:"user_prompt"`
}

type IntimacyImageContent struct {
	Content    string `json:"content"`
	NeedUnlock int64  `json:"need_unlock"`
}

type ChatCharacterQueryResult struct {
	// Chat fields (matched with chat_* aliases)
	ChatID                   int64          `db:"chat_id"`
	ChatUserID               int64          `db:"chat_user_id"`
	ChatCharacterID          int64          `db:"chat_character_id"`
	ChatSceneID              int64          `db:"chat_scene_id"`
	ChatIntimacyLevel        int64          `db:"chat_intimacy_level"`
	ChatCurToNextStage       sql.NullString `db:"chat_cur_to_next_stage"`
	ChatTitle                sql.NullString `db:"chat_title"`
	ChatLastMessages         sql.NullString `db:"chat_last_messages"`
	ChatInteractionCount     int64          `db:"chat_interaction_count"`
	ChatLastMessageTimestamp sql.NullTime   `db:"chat_last_message_timestamp"`
	ChatCreatedAt            sql.NullTime   `db:"chat_created_at"`
	ChatPersonaID            sql.NullInt64  `db:"chat_persona_id"`
	ChatShare                sql.NullString `db:"chat_share"`

	// Character fields (matched with character_* aliases)
	CharacterID                 int64          `db:"character_id"`
	CharacterName               sql.NullString `db:"character_name"`
	CharacterAvatarImageURL     sql.NullString `db:"character_avatar_image_url"`
	CharacterTagline            sql.NullString `db:"character_tagline"`
	CharacterBio                sql.NullString `db:"character_bio"`
	CharacterGreeting           sql.NullString `db:"character_greeting"`
	CharacterPreviewImageURL    sql.NullString `db:"character_preview_image_url"`
	CharacterBackgroundImageURL sql.NullString `db:"character_background_image_url"`
	CharacterVisibility         sql.NullString `db:"character_visibility"`
	CharacterTags               sql.NullString `db:"character_tags"`
	CharacterScenes             sql.NullString `db:"character_scenes"`
	CharacterIsNSFW             sql.NullBool   `db:"character_is_nsfw"`
	CharacterInteractionCount   sql.NullInt64  `db:"character_interaction_count"`
	CharacterLikeCount          sql.NullInt64  `db:"character_like_count"`
	CharacterCreator            sql.NullString `db:"character_creator"`
	CharacterCreatedAt          sql.NullTime   `db:"character_created_at"`
}

type ChatCharactersQueryResult struct {
	ID          int64 `db:"id"`
	UserID      int64 `db:"user_id"`
	CharacterID int64 `db:"character_id"`
	SceneID     int64 `db:"scene_id"`
	CreatedAt   int64 `db:"created_at"`
}

// LatestChatMessageInternal 用于服务内部传递或缓存最新消息的结构体
type LatestChatMessageInternal struct {
	Role       string `json:"role"`
	Content    string `json:"content"`
	Type       string `json:"type"`
	SceneID    int64  `json:"scene_id,optional"`
	NeedUnlock int64  `json:"need_unlock,optional"`
}

// PlanFeatureSettingInternal 用于服务内部传递或缓存计划功能配置的结构体
// 它整合了 plan_feature_settings 表的信息
type PlanFeatureSettingInternal struct {
	PlanID           string         `json:"plan_id"`
	FeatureKey       string         `json:"feature_key"`
	Description      string         `json:"description"`
	ValueType        string         `json:"value_type"` // e.g., BOOLEAN, INTEGER, UNLIMITED
	Unit             sql.NullString `json:"unit"`       // e.g., messages_per_day, tokens
	IsEnabled        bool           `json:"is_enabled"`
	LimitValueInt    sql.NullInt64  `json:"limit_value_int"`
	LimitValueString sql.NullString `json:"limit_value_string"`
}

// LLMRequestPayload LLMMessage 用于构建发送到 LLM API 的消息结构
type LLMRequestPayload struct {
	Model            string                 `json:"model"`
	Messages         []LLMMessage           `json:"messages"`
	Stream           bool                   `json:"stream"`
	Provider         map[string]interface{} `json:"provider"`
	Temperature      float64                `json:"temperature"`
	TopP             float64                `json:"top_p"`
	MaxTokens        int                    `json:"max_tokens"`
	FrequencyPenalty float64                `json:"frequency_penalty"`
	PresencePenalty  float64                `json:"presence_penalty"`
}

type LLMMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type ChatCompletionResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

type SummaryWithScene struct {
	SceneID int64  `json:"scene_id"`
	Summary string `json:"summary"`
}

type PersonalityWithScene struct {
	SceneID     int64  `json:"scene_id"`
	Personality string `json:"personality"`
}

type SceneSwitchIntimacyRecord struct {
	SceneID  int64 `json:"scene_id"` // 场景ID
	Intimacy int64 `json:"intimacy"` // 切换时的亲密度
}

type AssistantReply struct {
	ReplyText   string `json:"reply_text"`
	PictureName string `json:"picture_name"`
	Score       int64  `json:"score"`
	Reason      string `json:"reason"`
}

// ------- LLM response format end
