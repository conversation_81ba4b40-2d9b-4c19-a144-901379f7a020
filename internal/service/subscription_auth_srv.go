// internal/service/subscription_auth_service.go
package service

import (
	"context"
	"encoding/json" // 用于 Redis 存取结构体
	"fmt"
	"strings"
	"time"

	// 你的数据库模型 (sqlc 生成)
	"xai.backend/internal/svc"
	"xai.backend/internal/types" // 你的常量和自定义类型

	"github.com/jzero-io/jzero-contrib/condition"
	"github.com/zeromicro/go-zero/core/logx"        // 导入 redis 以使用 redis.ErrNil
	"github.com/zeromicro/go-zero/core/stores/sqlc" // 用于 sqlc.ErrNotFound
)

type SubscriptionAuthService struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logger logx.Logger
}

func NewSubscriptionAuthService(ctx context.Context, svcCtx *svc.ServiceContext) *SubscriptionAuthService {
	return &SubscriptionAuthService{
		ctx:    ctx,
		svcCtx: svcCtx,
		logger: logx.WithContext(ctx),
	}
}

// GetUserActivePlanID 获取用户当前有效的订阅计划ID
// subscriptions 表有 user_id 的唯一索引。
// 如果没有有效付费订阅，则返回 types.PlanIDFree
func (s *SubscriptionAuthService) GetUserActivePlanID(userID int64) (string, error) {

	subscription, err := s.svcCtx.Model.SubscriptionsModel.FindOneByUserIdWithCache(s.ctx, nil, userID)

	if err != nil {
		// 这里的错误可能是 DB 错误，也可能是 sqlc.ErrNotFound
		if err == sqlc.ErrNotFound { // 明确检查 sqlc.ErrNotFound
			s.logger.Infof("用户 %d 没有订阅记录，默认为 '%s' 计划", userID, types.PlanIDFree)
			return types.PlanIDFree, nil
		}
		s.logger.Errorf("获取用户 %d 的订阅信息失败: %v", userID, err)
		return "", fmt.Errorf("获取用户订阅信息失败: %w", err) // 保持错误包装
	}

	// 检查订阅状态和有效期
	isActive := (subscription.Status == types.SubscriptionStatusActive || subscription.Status == types.SubscriptionStatusTrialing)
	isExpired := subscription.CurrentPeriodEnd.Valid && subscription.CurrentPeriodEnd.Time.Before(time.Now())
	// isCancelledAndEnded := subscription.CancelAtPeriodEnd && isExpired // 更复杂的取消逻辑

	if !isActive || isExpired {
		s.logger.Infof("用户 %d 订阅 (ID: %s, Plan: %s, Status: %s, End: %v) 非有效状态或已过期，视为 '%s' 计划",
			userID, subscription.Id, subscription.PlanId, subscription.Status, subscription.CurrentPeriodEnd, types.PlanIDFree)
		return types.PlanIDFree, nil
	}

	s.logger.Infof("用户 %d 当前有效计划为: %s (订阅ID: %s)", userID, subscription.PlanId, subscription.Id)
	return subscription.PlanId, nil
}

// GetPlanFeatureSetting 获取特定计划的特定功能配置 (已包含Redis缓存逻辑)
func (s *SubscriptionAuthService) GetPlanFeatureSetting(planID string, featureKey string) (*types.PlanFeatureSettingInternal, error) {
	cacheKey := fmt.Sprintf("%s%s:%s", types.PlanFeatureSettingCachePrefix, planID, featureKey)
	var setting types.PlanFeatureSettingInternal

	// 使用 *redis.Redis 的 Get 方法
	jsonVal, err := s.svcCtx.RedisConn.Get(cacheKey)
	if err == nil { // 获取成功
		if jsonVal == "" {
			s.logger.Debugf("缓存未命中: 计划 %s 功能 %s, 结果为空", planID, featureKey)
			// 缓存未命中，继续查库
		} else {
			if errJson := json.Unmarshal([]byte(jsonVal), &setting); errJson == nil {
				s.logger.Debugf("缓存命中: 计划 %s 功能 %s", planID, featureKey)
				return &setting, nil
			} else {
				s.logger.Errorf("JSON反序列化缓存数据失败 (key: %s) (value: %s): %v (视为缓存未命中)", cacheKey, jsonVal, errJson)
				// 反序列化失败，视为缓存未命中，继续查库并覆盖缓存
			}
		}
	} else {
		s.logger.Errorf("从Redis获取计划功能 %s:%s 失败: %v (非ErrNil)", planID, featureKey, err)
		// 降级查库
	}

	s.logger.Debugf("缓存未命中或获取/解析失败，从DB加载: 计划 %s 功能 %s", planID, featureKey)
	// 确保 ServiceContext 中有 Model.PlanFeatureSettingsModel
	dbSetting, dbErr := s.svcCtx.Model.PlanFeatureSettingsModel.FindOneByCondition(s.ctx, nil,
		condition.Condition{Field: "plan_id", Value: planID, Operator: condition.Equal},
		condition.Condition{Field: "feature_key", Value: featureKey, Operator: condition.Equal},
	)
	if dbErr != nil {
		if dbErr == sqlc.ErrNotFound { // 使用 sqlc.ErrNotFound
			s.logger.Infof("计划 %s 功能 %s 在DB中未找到配置", planID, featureKey)
			// 可以考虑缓存一个空标记的字符串，例如 "NOT_FOUND"，并用 Setex 设置短时过期
			// 以防止缓存穿透。取出时判断是否为该标记。
			// s.svcCtx.RedisConn.Setex(cacheKey, "NOT_FOUND", int(time.Minute*5/time.Second)) // 示例
			return nil, nil
		}
		s.logger.Errorf("从DB获取计划 %s 功能 %s 的配置失败: %v", planID, featureKey, dbErr)
		return nil, fmt.Errorf("获取计划功能配置失败: %w", dbErr)
	}

	internalSetting := &types.PlanFeatureSettingInternal{
		PlanID:           dbSetting.PlanId,
		FeatureKey:       dbSetting.FeatureKey,
		Description:      dbSetting.Description,
		ValueType:        dbSetting.ValueType,
		Unit:             dbSetting.Unit,
		IsEnabled:        dbSetting.IsEnabled,
		LimitValueInt:    dbSetting.LimitValueInt,
		LimitValueString: dbSetting.LimitValueString,
	}

	// 使用 *redis.Redis 的 Setex 方法
	jsonData, jsonErr := json.Marshal(internalSetting)
	if jsonErr != nil {
		s.logger.Errorf("JSON序列化计划功能 %s:%s 失败: %v", planID, featureKey, jsonErr)
		// 即使序列化失败，之前的DB查询结果仍然返回，只是不缓存
		return internalSetting, nil
	}

	// 设置缓存
	s.logger.Infof("设置Redis缓存 (key: %s): %s", cacheKey, string(jsonData))
	setErr := s.svcCtx.RedisConn.Setex(cacheKey, string(jsonData), types.PlanFeatureSettingCacheExpiry)
	if setErr != nil {
		s.logger.Errorf("设置Redis缓存失败 (key: %s): %v", cacheKey, setErr)
		// 缓存设置失败不应阻塞主流程，记录日志即可
	}

	s.logger.Debugf("从数据库加载计划 %s 功能 %s 的配置并已缓存", planID, featureKey)
	return internalSetting, nil
}

// CanUseFeature 检查用户是否可以使用某个功能 (用于开关型或基础权限)
func (s *SubscriptionAuthService) CanUseFeature(userID int64, featureKey string) (bool, string, error) { // bool:是否可用, string:描述/原因, error
	planID, err := s.GetUserActivePlanID(userID)
	if err != nil {
		return false, "无法确定用户计划", err
	}

	setting, err := s.GetPlanFeatureSetting(planID, featureKey)
	if err != nil { // DB or Redis error during fetch
		s.logger.Errorf("获取计划 %s 功能 %s 配置失败: %v", planID, featureKey, err)
		return false, "无法获取功能配置", err
	}

	// Setting IS found for featureKey.
	featureDescription := setting.Description
	if featureDescription == "" {
		featureDescription = featureKey // Fallback to featureKey if description is empty
	}

	if !setting.IsEnabled {
		s.logger.Infof("用户 %d (计划 %s) 功能 %s (描述: %s) 配置存在但已禁用", userID, planID, featureKey, featureDescription)
		// If a feature is explicitly configured but IsEnabled=false, we respect that and DO NOT try _unlimited fallback.
		return false, fmt.Sprintf("您的套餐目前不包含 '%s' 功能。", featureDescription), nil
	}

	s.logger.Infof("用户 %d (计划 %s) 功能 %s (描述: %s) 已启用", userID, planID, featureKey, setting.Description)
	return true, setting.Description, nil
}

func generateRedisUsageKey(userID int64, featureKey string, entityID_optional string, periodStart string) string {
	//如果是账户级别限制 (例如配图数)，则不包含 entityID
	if entityID_optional == "" || featureKey == types.FeatureImageGenerationLimitPerDay {
		return fmt.Sprintf("%s%d:%s:%s", types.RedisUsageCountPrefix, userID, featureKey, periodStart)
	}
	return fmt.Sprintf("%s%d:%s:%s:%s", types.RedisUsageCountPrefix, userID, featureKey, entityID_optional, periodStart)
}

// ProcessUsageLogic 执行核心的用量检查和增加逻辑，假设 planID 和 setting 已被获取
// setting 参数可以为 nil，方法内部会处理这种情况（例如，功能未配置）
func (s *SubscriptionAuthService) ProcessUsageLogic(
	userID int64,
	planID string, // 用户当前的计划ID
	setting *types.PlanFeatureSettingInternal, // 针对 specificFeatureKey 的配置，可能为 nil
	specificFeatureKey string, // 必须提供，用于日志、描述和可能的 Redis key
	entityID_optional ...string,
) (allowed bool, message string, err error) {
	currentEntityIDStr := ""
	if len(entityID_optional) > 0 && entityID_optional[0] != "" {
		currentEntityIDStr = entityID_optional[0]
	}

	featureDescription := specificFeatureKey // 默认描述为 feature key 本身
	if setting != nil && setting.Description != "" {
		featureDescription = setting.Description
	}

	// 1. 检查此 featureKey 的 setting 是否为 UNLIMITED (基于传入的 setting)
	if setting != nil && setting.IsEnabled && setting.ValueType == types.ValueTypeUnlimited {
		s.logger.Infof("用户 %d (计划 %s) 功能 %s 直接配置为 UNLIMITED (通过 ProcessUsageLogic)", userID, planID, specificFeatureKey)
		s.publishUsageUpdateEvent(userID, planID, specificFeatureKey, currentEntityIDStr, time.Now(), 0, true, 0)
		return true, "", nil
	}

	// 2. 如果功能未配置 (setting == nil) 或未启用
	if setting == nil || !setting.IsEnabled {
		s.logger.Infof("用户 %d (计划 %s) 功能 %s 未配置或未启用 (通过 ProcessUsageLogic)", userID, planID, specificFeatureKey)
		// 尝试回退到 _unlimited 检查 (仅当主 featureKey 的 setting 为 nil 或 IsEnabled=false 时)
		// 注意：这里的回退逻辑是 ProcessUsageLogic 特有的，因为 CheckAndIncrementUsage 的原始版本没有传入 setting
		// 如果 CheckAndIncrementUsage 的调用者期望它做 _unlimited 回退，那么该逻辑应保留在 CheckAndIncrementUsage 中，
		// 或者 ProcessUsageLogic 需要知道原始 featureKey 以便自行回退。
		// 为简单起见，当前 ProcessUsageLogic 假设传入的 setting 就是最终决策依据，不再进行 _unlimited 后缀key的查找。
		// 如果需要回退，建议由调用 ProcessUsageLogic 之前的地方（例如调整后的 CheckAndIncrementUsage）处理。
		return false, fmt.Sprintf("您的套餐 '%s' 功能或已达上限。", featureDescription), nil
	}

	// 3. 如果不是数值限制类型 (INTEGER) - 前面已经处理了 UNLIMITED
	if setting.ValueType != types.ValueTypeInteger {
		s.logger.Infof("用户 %d (计划 %s) 功能 %s (类型 %s) 请求检查数值用量，但类型不符 (通过 ProcessUsageLogic)", userID, planID, specificFeatureKey, setting.ValueType)
		return false, "功能配置类型错误", fmt.Errorf("功能 '%s' 类型 %s 无法检查数值用量", featureDescription, setting.ValueType)
	}

	// 4. 获取数值限制
	if !setting.LimitValueInt.Valid {
		return false, "功能限制值未正确配置", fmt.Errorf("功能 '%s' 的 LimitValueInt 为 NULL (通过 ProcessUsageLogic)", featureDescription)
	}
	limit := setting.LimitValueInt.Int64
	if limit <= 0 {
		s.logger.Infof("用户 %d (计划 %s) 功能 %s 限制值为 %d，视为不允许 (通过 ProcessUsageLogic)", userID, planID, specificFeatureKey, limit)
		return false, fmt.Sprintf("'%s' 功能当前不可用或已达上限。", featureDescription), nil
	}

	// 5. Redis 计数与检查
	usagePeriodStartStr := time.Now().Truncate(24 * time.Hour).Format("2006-01-02")
	redisKey := generateRedisUsageKey(userID, specificFeatureKey, currentEntityIDStr, usagePeriodStartStr)

	newCount, redisErr := s.svcCtx.RedisConn.IncrCtx(s.ctx, redisKey)
	if redisErr != nil {
		s.logger.Errorf("Redis INCR 失败 (key: %s): %v. 允许操作并记录错误，依赖DB最终一致性.", redisKey, redisErr)
		s.publishUsageUpdateEvent(userID, planID, specificFeatureKey, currentEntityIDStr, time.Now(), -1, false, limit)
		return true, "服务暂时繁忙，请稍后再试（用量记录可能延迟）。", nil
	}
	if newCount == 1 {
		//expire 事件设置为+8时区的23:59分59秒, 计算出过期的秒数
		expireTime := time.Now().Add(24 * time.Hour).Truncate(24 * time.Hour).Add(8 * time.Hour).Add(-time.Second)

		expireErr := s.svcCtx.RedisConn.ExpireCtx(s.ctx, redisKey, types.RedisUsageCountDailyExpirySec)
		if expireErr != nil {
			s.logger.Errorf("Redis EXPIRE 失败 (key: %s): %v", redisKey, expireErr)
		}
	}
	if newCount > limit {
		s.logger.Infof("用户 %d (计划 %s) 功能 %s Redis 计数 %d 超出限制 %d (key: %s)", userID, planID, specificFeatureKey, newCount, limit, redisKey)
		_, decrErr := s.svcCtx.RedisConn.DecrCtx(s.ctx, redisKey)
		if decrErr != nil {
			s.logger.Errorf("Redis DECR 回滚失败 (key: %s): %v", redisKey, decrErr)
		}
		unitStr := ""
		if setting.Unit.Valid {
			unitStr = " " + setting.Unit.String
		}
		return false, fmt.Sprintf("您今日已达到'%s'的上限 (%d/%d%s)。", featureDescription, newCount, limit, unitStr), nil
	}

	// 6. 未超限，发送异步消息更新数据库
	s.publishUsageUpdateEvent(userID, planID, specificFeatureKey, currentEntityIDStr, time.Now(), newCount, false, limit)
	s.logger.Infof("用户 %d (计划 %s) 功能 %s Redis 计数 %d (限制 %d). Key: %s (通过 ProcessUsageLogic)", userID, planID, specificFeatureKey, newCount, limit, redisKey)
	return true, "", nil
}

// CheckAndIncrementUsage 检查并增加用量 (使用Redis异步更新)
// userID: 用户ID
// featureKey: 要检查的功能键 (通常是带 _limit_ 的键)
// entityID_optional: 可选的实体ID (如 character_id)，用于特定实体的用量限制
// 返回: (是否允许操作, 用户提示信息, 错误)
func (s *SubscriptionAuthService) CheckAndIncrementUsage(userID int64, featureKey string, entityID_optional ...string) (bool, string, error) {
	planID, err := s.GetUserActivePlanID(userID)
	if err != nil {
		return false, "无法确定您的订阅计划。", err
	}

	// 尝试获取主 featureKey 的配置
	setting, err := s.GetPlanFeatureSetting(planID, featureKey)
	if err != nil {
		// GetPlanFeatureSetting 内部错误，例如数据库连接问题
		s.logger.Errorf("CheckAndIncrementUsage: 获取功能 %s (计划 %s) 配置时出错: %v", featureKey, planID, err)
		return false, "无法获取功能配置。", err
	}

	// 如果主 featureKey 未配置 (setting == nil) 或已禁用 (!setting.IsEnabled)，
	// 并且该 featureKey 不是 _unlimited 后缀，则尝试查找对应的 _unlimited 配置。
	if (setting == nil || !setting.IsEnabled) && !strings.HasSuffix(featureKey, "_unlimited") {
		baseFeatureKey := strings.Replace(featureKey, "_limit_per_day_per_character", "", 1)
		baseFeatureKey = strings.Replace(baseFeatureKey, "_limit_per_day", "", 1)
		unlimitedFeatureKey := baseFeatureKey + "_unlimited"

		s.logger.Debugf("主功能键 %s 未配置/启用，尝试检查回退无限制键 %s (计划 %s)", featureKey, unlimitedFeatureKey, planID)
		unlimitedSetting, uErr := s.GetPlanFeatureSetting(planID, unlimitedFeatureKey)
		if uErr != nil {
			s.logger.Errorf("CheckAndIncrementUsage: 获取回退无限制功能 %s (计划 %s) 配置时出错: %v", unlimitedFeatureKey, planID, uErr)
			// 即使回退查找失败，也继续使用原始的 setting (可能为 nil) 调用 ProcessUsageLogic
		} else if unlimitedSetting != nil && unlimitedSetting.IsEnabled && unlimitedSetting.ValueType == types.ValueTypeUnlimited {
			s.logger.Infof("用户 %d (计划 %s) 通过回退键 %s 获得 %s 的无限制权限", userID, planID, unlimitedFeatureKey, featureKey)
			// 如果回退的 unlimitedSetting 有效，则使用它。ProcessUsageLogic 会直接处理 ValueTypeUnlimited
			setting = unlimitedSetting
		}
	}

	return s.ProcessUsageLogic(userID, planID, setting, featureKey, entityID_optional...)
}

func (s *SubscriptionAuthService) CheckUsageLimit(userId int64, characterID int64, userActivePlanID string, featureKey string,
	chunkChan chan<- types.StreamChunk) bool {
	entityID1 := fmt.Sprintf("%d", characterID)
	entities := []string{entityID1}

	// 获取此功能的配置
	setting1, err := s.GetPlanFeatureSetting(userActivePlanID, featureKey)
	if err != nil {
		// 处理获取配置失败的错误
		s.logger.Errorf("获取配置失败: %v", err)
		chunkChan <- types.StreamChunk{Err: fmt.Errorf(types.ErrSystemConfigError), IsFinal: true}
		return true
	}

	// 检查用户是否超过限制
	allowed, message, err := s.ProcessUsageLogic(userId, userActivePlanID, setting1, featureKey, entities...)
	if err != nil {
		s.logger.Errorf("获取用量失败: %v", err)
		chunkChan <- types.StreamChunk{Err: fmt.Errorf(types.ErrSystemConfigError), IsFinal: true}
	}
	if !allowed {
		s.logger.Errorf("用户超过限制: %s", message)
		return true
	}
	return false
}

// publishUsageUpdateEvent 模拟发送用量更新事件到消息队列
func (s *SubscriptionAuthService) publishUsageUpdateEvent(
	userID int64,
	planID,
	featureKey,
	entityID string,
	timestamp time.Time,
	currentRedisCount int64, // Redis INCR 后的新计数值，或 -1 表示 Redis 错误
	isUnlimited bool,
	limitForFeature int64, // 当前 featureKey 的数值限制 (如果适用)
) {
	forceSync := false // 默认不强制同步

	if !isUnlimited { // 只对非无限用量的用户考虑强制同步DB的条件
		if currentRedisCount == -1 { // Redis 错误，需要DB介入记录这次使用
			forceSync = true
		} else if currentRedisCount == 1 { // 首次在 Redis 创建新Key (针对某个 user-feature-period)，强制同步一次DB
			forceSync = true
		}
		// 未来可以为 !isUnlimited 的情况添加更复杂的强制同步逻辑, e.g.:
		// else if limitForFeature > 0 && currentRedisCount > 0 && float64(currentRedisCount) >= float64(limitForFeature)*0.9 {
		//  forceSync = true // 达到限制的90%
		// }
	}
	// 对于 isUnlimited == true 的情况，forceSync 始终为 false (根据当前策略)

	task := types.FeatureUsageUpdateTask{
		UserID:            userID,
		PlanIDAtUsage:     planID,
		FeatureKey:        featureKey,
		EntityID:          entityID,
		Timestamp:         timestamp,
		CurrentRedisCount: currentRedisCount,
		IsUnlimitedUsage:  isUnlimited,
		UsagePeriodStart:  timestamp.Truncate(24 * time.Hour).Format("2006-01-02"),
		ForceDBSync:       forceSync,
	}

	select {
	case s.svcCtx.FeatureUsageUpdateQueue <- task:
		s.logger.Infof("任务已发送到 FeatureUsageUpdateQueue: %+v", task)
	default:
		s.logger.Errorf("FeatureUsageUpdateQueue 已满，任务被丢弃: %+v", task)
	}
}
