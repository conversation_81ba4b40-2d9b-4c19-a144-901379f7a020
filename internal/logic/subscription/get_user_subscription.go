package subscription

import (
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"time"

	"context"

	"github.com/jzero-io/jzero-contrib/condition"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"xai.backend/internal/svc"
	"xai.backend/internal/types"
	"xai.backend/internal/util"
	"xai.backend/internal/util/ctxutil"
)

type GetUserSubscription struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	r      *http.Request
}

// 获取当前用户的有效订阅信息
func NewGetUserSubscription(ctx context.Context, svcCtx *svc.ServiceContext, r *http.Request) *GetUserSubscription {
	return &GetUserSubscription{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
		r:      r,
	}
}

func (l *GetUserSubscription) GetUserSubscription() (resp *types.UserSubscription, err error) {
	// 1. 获取用户ID
	userID, err := ctxutil.GetUserIDFromCtx(l.ctx)
	if err != nil {
		l.Errorf("获取用户ID失败: %v", err)
		return nil, types.ErrGetUserIDFailed
	}

	// 查找用户最新的有效订阅
	sub, err := l.svcCtx.Model.SubscriptionsModel.FindOneByCondition(l.ctx, nil,
		condition.Condition{Field: "user_id", Operator: condition.Equal, Value: userID},
		condition.Condition{Field: "status", Operator: condition.In, Value: []string{"active", "trialing", "past_due"}},
		condition.Condition{Operator: condition.OrderBy, Value: "created_at DESC"})
	if err != nil {
		if errors.Is(err, sqlc.ErrNotFound) {
			l.Logger.Infof("用户 %d 没有找到有效的订阅", userID)
			return nil, types.ErrMsgNotSubscribed
		}
		l.Logger.Errorf("Failed to query db for user %d subscription: %v", userID, err)
		return nil, types.ErrMsgDBQueryFailed
	}

	if sub == nil {
		l.Logger.Infof("用户 %d 没有找到有效的订阅", userID)
		return nil, types.ErrMsgNotSubscribed
	}

	// 检查订阅是否已过期
	currentTime := time.Now()
	expireAt := sql.NullTime{Time: sub.CurrentPeriodEnd.Time, Valid: true}
	if expireAt.Valid && sub.CurrentPeriodEnd.Time.Before(currentTime) && sub.Status != types.SubscriptionStatusExpired {
		l.Logger.Infof("用户 %d 的订阅 %d 已过期", userID, sub.Id)
		sub.Status = types.SubscriptionStatusExpired
		err := l.svcCtx.Model.SubscriptionsModel.Update(l.ctx, nil, sub)
		if err != nil {
			l.Logger.Errorf("Failed to update subscription status for user %d subscription: %v", userID, err)
			return nil, err
		}
		//更新失败了也继续往下走
		//return nil, types.ErrMsgNotSubscribed
	}

	// 获取计划名称 (可选, 可以在模型查询中加入)
	plan, err := l.svcCtx.Model.SubscriptionPlansModel.FindOneWithCache(l.ctx, nil, sub.PlanId)
	planName := ""
	if err != nil {
		l.Logger.Errorf("Failed to find plan name for plan %s (sub %s): %v", sub.PlanId, sub.Id, err)
	} else {
		planName = plan.Name
	}

	resp = &types.UserSubscription{
		ID:                 strconv.FormatInt(sub.Id, 10),
		PlanID:             sub.PlanId,
		PlanName:           planName,
		Status:             sub.Status,
		PaymentProvider:    sub.PaymentProvider,
		CurrentPeriodStart: util.FormatNullTime(sub.CurrentPeriodStart),
		CurrentPeriodEnd:   util.FormatNullTime(sub.CurrentPeriodEnd),
		TrialEndsAt:        util.FormatNullTime(sub.TrialEndsAt),
		CancelAtPeriodEnd:  sub.CancelAtPeriodEnd,
		CanceledAt:         util.FormatNullTime(sub.CanceledAt),
		EndedAt:            util.FormatNullTime(sub.EndedAt),
	}

	return resp, nil
}
