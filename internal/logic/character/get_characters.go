package character

import (
	"context"
	"net/http"
	"slices"
	_06_likes "xai.backend/internal/model/06_likes"

	"github.com/jzero-io/jzero-contrib/condition"
	"github.com/zeromicro/go-zero/core/logx"
	"xai.backend/internal/converter"
	_03_characters "xai.backend/internal/model/03_characters" // 引入 model
	"xai.backend/internal/svc"
	"xai.backend/internal/types"
	"xai.backend/internal/util/ctxutil"
)

type GetCharacters struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	r      *http.Request
}

// NewGetCharacters 获取角色列表-瀑布流
func NewGetCharacters(ctx context.Context, svcCtx *svc.ServiceContext, r *http.Request) *GetCharacters {
	return &GetCharacters{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
		r:      r,
	}
}

// GetCharacters 获取角色列表-瀑布流
//
// 1. 如果 page == 1，获取最近聊天的两个角色，并将其排在最前面
// 2. 获取按上架时间排序的其他角色，过滤掉 步骤 1 中已经获取到的那两个最近聊天的角色
// 3. 合并结果
// 4. 计算是否有更多页
// 5. 查询哪些角色已经点赞
// 6. 转换结果
// 7. 返回响应
func (l *GetCharacters) GetCharacters(req *types.GetCharactersReq) (resp *types.CharacterListResp, err error) {
	var finalCharacters []*_03_characters.Characters
	var recentCharacterIDs []int64 // 存储最近聊过的角色ID，用于后续过滤

	// 1. 获取用户ID
	userId, err := ctxutil.GetUserIDFromCtx(l.ctx)
	if err != nil {
		l.Logger.Infof("Get user ID failed: %v", err)
		// 允许未登录用户访问
		//return nil, types.ErrGetUserIDFailed
	}

	// 2. 获取最近聊过的两个角色 (仅当 page == 1 且 userId > 0 时获取)
	if req.Page == 1 && userId > 0 {
		twoRecentCharIDs, err := l.svcCtx.Model.ExtendedCharactersModel.FindRecentChatCharacters(l.ctx, userId, 2)
		logx.WithContext(l.ctx).Infof("获取最近聊过的两个角色: %v", twoRecentCharIDs)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("获取最近聊过的两个角色失败: %v", err)
			return nil, types.ErrCharacterListFailed
		}

		if len(twoRecentCharIDs) > 0 {
			recentCharacterIDs = append(recentCharacterIDs, twoRecentCharIDs...)
			for _, charID := range twoRecentCharIDs {
				character, err := l.svcCtx.Model.CharactersModel.FindOneWithCache(l.ctx, nil, charID)
				if err != nil {
					logx.WithContext(l.ctx).Errorf("查询角色失败: %v", err)
					return nil, types.ErrCharacterNotFound
				}
				finalCharacters = append(finalCharacters, character)
			}
		}
	}
	l.Logger.Infof("recentCharacterIDs: %v", recentCharacterIDs)

	// 3. 获取按上架时间排序的其他角色,
	// 过滤掉 步骤 1 中已经获取到的那两个最近聊过的角色
	if req.Page == 0 {
		req.Page = types.DefaultPageSize
	}
	if req.Limit == 0 {
		req.Limit = types.DefaultLimitSize
	}
	limit := req.Limit
	offset := (req.Page - 1) * req.Limit

	// 如果是第一页，并且已经获取了最近聊天的角色，需要调整 limit
	// 例如，如果已经获取了2个最近聊天的，并且 pageSize 是 20，那么这一步只需要再获取 18 个
	// 但为了简化分页逻辑，这里先按 pageSize 取，最后再截断。
	// 更优化的方式是在查询时就调整 limit，但会使分页计算复杂一些。

	var conditions []condition.Condition

	conditions = append(conditions, condition.Condition{Operator: condition.OrderBy, Value: "created_at DESC"})
	conditions = append(conditions, condition.Condition{Operator: condition.Offset, Value: offset})
	conditions = append(conditions, condition.Condition{Operator: condition.Limit, Value: limit})

	if req.Page == 1 && len(recentCharacterIDs) > 0 {
		conditions = append(conditions, condition.Condition{Field: "id", Operator: condition.NotIn, Value: recentCharacterIDs})
	}

	otherCharacters, total, err := l.svcCtx.Model.CharactersModel.PageByCondition(l.ctx, nil, conditions...)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("获取角色列表失败: %v", err)
		return nil, types.ErrCharacterListFailed
	}

	// --- 4. 合并结果 ---
	// 将其他角色追加到 finalCharacters (如果 page = 1)
	// 或者直接使用 otherCharacters (如果 page > 1)

	// 如果是第一页，finalCharacters 已经包含了最近聊天的
	// otherCharacters 此时获取的是排除掉最近聊天的、按时间排序的
	if req.Page == 1 {
		finalCharacters = append(finalCharacters, otherCharacters...)
	} else {
		// 如果不是第一页，则不需要最近聊天的，直接用 otherCharacters
		finalCharacters = otherCharacters
	}

	// 5. 计算是否有更多页
	hasMore := req.Page*req.Limit < total

	// 6. 查询哪些角色已经点赞
	finalCharacterIDs := make([]int64, 0, len(finalCharacters))
	for _, dbChar := range finalCharacters {
		finalCharacterIDs = append(finalCharacterIDs, dbChar.Id)
	}
	l.Logger.Infof("finalCharacterIDs: %v", finalCharacterIDs)

	var likedCharacters []*_06_likes.Likes
	if userId > 0 {
		likedCharacters, err = l.svcCtx.Model.LikesModel.FindByCondition(l.ctx, nil,
			condition.Condition{Field: "user_id", Operator: condition.Equal, Value: userId},
			condition.Condition{Field: "character_id", Operator: condition.In, Value: finalCharacterIDs},
		)
		if err != nil {
			logx.WithContext(l.ctx).Errorf("获取角色点赞状态失败: %v", err)
			return nil, types.ErrCharacterLikeListQueryFailed
		}
	}

	likedCharacterIDs := make([]int64, 0, len(likedCharacters))
	for _, likedChar := range likedCharacters {
		likedCharacterIDs = append(likedCharacterIDs, likedChar.CharacterId)
	}
	l.Logger.Infof("likedCharacterIDs: %v", likedCharacterIDs)

	// 7. 查询用户和角色最近一次聊天记录的场景背景图片
	backgroundImageURLs, err := l.svcCtx.Model.ExtendedCharactersModel.FindChatCharactersBackgroundImageURL(l.ctx, userId, finalCharacterIDs)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("获取角色背景图片失败: %v", err)
		return nil, types.ErrCharacterListFailed
	}

	// 8. 转换结果
	respCharacters := make([]types.Character, 0, len(finalCharacters))
	for _, dbChar := range finalCharacters {
		apiChar := converter.ConvertCharacterDBToCharacterAPI(dbChar)
		if apiChar != nil {
			if slices.Contains(recentCharacterIDs, apiChar.ID) {
				apiChar.Chatted = true
			}
			if slices.Contains(likedCharacterIDs, apiChar.ID) {
				apiChar.Liked = true
			}
			if backgroundImageURLs[apiChar.ID] != "" {
				apiChar.BackgroundImageURL = backgroundImageURLs[apiChar.ID]
			}
			respCharacters = append(respCharacters, *apiChar) // 注意 API 返回的是 []Character 而不是 []*Character
		}
	}

	// 8. 返回响应
	resp = &types.CharacterListResp{
		Characters: respCharacters,
		Total:      total,
		HasMore:    hasMore,
	}

	l.Logger.Infof("GetCharacters Resp: %v", resp)

	return resp, nil
}
