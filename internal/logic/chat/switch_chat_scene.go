package chat

import (
	"database/sql"
	"encoding/json"
	"net/http"
	"slices"
	"time"

	"context"

	"xai.backend/internal/svc"
	"xai.backend/internal/types"
	"xai.backend/internal/util"
	"xai.backend/internal/util/ctxutil"

	"github.com/huandu/go-sqlbuilder"
	"github.com/jzero-io/jzero-contrib/condition"
	"github.com/zeromicro/go-zero/core/logx"
	_ "xai.backend/internal/model/03_characters"
	_04_chats "xai.backend/internal/model/04_chats"
)

type SwitchChatScene struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	r      *http.Request
}

// NewSwitchChatScene 切换对话到某个场景
func NewSwitchChatScene(ctx context.Context, svcCtx *svc.ServiceContext, r *http.Request) *SwitchChatScene {
	return &SwitchChatScene{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
		r:      r,
	}
}

func (l *SwitchChatScene) SwitchChatScene(req *types.SwitchChatSceneReq) (resp *types.Chat, err error) {
	const maxRetries = types.ChatSystemPromptMaxRetries // 最大重试次数
	var retryCount int                                  // 内部重试计数器

	chatID := req.ID

	var updateWithRetry func(chatID int64) (resp *types.Chat, err error)
	updateWithRetry = func(chatID int64) (resp *types.Chat, err error) {
		if retryCount >= maxRetries {
			l.Errorf("ChatID %d: 达到最大重试次数: %d", chatID, maxRetries)
			return nil, types.ErrChatUpgradeMaxRetriesFailed
		}
		// 1. 获取用户ID
		userID, err := ctxutil.GetUserIDFromCtx(l.ctx)
		if err != nil {
			l.Errorf("获取用户ID失败: %v", err)
			return nil, types.ErrUnauthorized
		}

		// 2. 获取对话
		chat, err := l.svcCtx.Model.ChatsModel.FindOne(l.ctx, nil, chatID)
		if err != nil {
			l.Errorf("ChatID %d: 获取对话失败: %v", chatID, err)
			return nil, types.ErrChatQueryFailed
		}

		// 3. 判断是否越权
		if chat.UserId != userID {
			l.Errorf("ChatID %d: 用户ID不匹配", chatID)
			return nil, types.ErrChatPermissionCheckFailed
		}

		// 4. 解析可用场景列表
		var availableSceneIds []int64
		err = json.Unmarshal([]byte(chat.AvailableSceneIds), &availableSceneIds)
		if err != nil {
			l.Errorf("ChatID %d: 解析对话可用场景ID失败: %v", chatID, err)
			return nil, types.ErrMsgJsonUnmarshalFailed
		}

		// 5. 检查对话可用场景列表中是否包含目标场景ID
		if !slices.Contains(availableSceneIds, req.SID) {
			l.Errorf("ChatID %d: 对话可用场景ID列表中不包含当前可用场景ID列表中: %v", chatID, availableSceneIds)
			return nil, types.ErrMsgSceneCantSwitch
		}

		// 6. 获取待切换的目标场景
		targetScene, err := l.svcCtx.Model.ScenesModel.FindOneWithCache(l.ctx, nil, req.SID)
		if err != nil {
			l.Errorf("ChatID %d, SceneID %d: 获取当前场景失败: %v", chatID, chat.SceneId, err)
			return nil, types.ErrChatSceneQueryFailed
		}

		// 7. 从 targetScene.Relationship 里面解析出第一个/最后一个人物关系描述
		var targetSceneRelationshipList []types.SceneRelationship
		if targetScene.Relationship.Valid && targetScene.Relationship.String != "" {
			err = json.Unmarshal([]byte(targetScene.Relationship.String), &targetSceneRelationshipList)
			if err != nil || len(targetSceneRelationshipList) == 0 {
				l.Errorf("ChatID %d, NextSceneID %d: Failed to unmarshal 场景关系列表 or list is empty: %v", chatID, targetScene.Id, err)
				return nil, types.ErrMsgJsonUnmarshalFailed
			}
		} else {
			l.Errorf("ChatID %d, NextSceneID %d: 新场景关系数据为空", chatID, targetScene.Id)
			return nil, types.ErrChatNewSceneRelationshipDataEmpty
		}

		// 8. 处理场景升级和切换时的关系获取
		var newCurrentRelationship types.SceneRelationship
		var isUpgradeScene bool

		if targetScene.Id > chat.HighestSceneId { //升级场景
			isUpgradeScene = true
			newCurrentRelationship = targetSceneRelationshipList[0]
		} else { // 非场景升级
			isUpgradeScene = false
			// 切换到曾经的最高聊天场景，取记录的关系
			if targetScene.Id == chat.HighestSceneId {
				var found bool
				for _, r := range targetSceneRelationshipList {
					if r.StageName == chat.HighestSceneRelationshipName.String {
						newCurrentRelationship = r
						found = true
						break
					}
				}
				if !found { // 如果没找到取第一个作为默认的
					l.Errorf("ChatID %d, NextSceneID %d: 未找到最高场景关系名称 %s 在目标场景关系列表中", chatID, targetScene.Id, chat.HighestSceneRelationshipName.String)
					newCurrentRelationship = targetSceneRelationshipList[0]
				}
			} else {
				// 切换到非最高聊过的的场景，取最后一个关系
				newCurrentRelationship = targetSceneRelationshipList[len(targetSceneRelationshipList)-1]
			}
		}
		newCurrentRelBytes, err := json.Marshal(newCurrentRelationship)
		if err != nil {
			l.Errorf("ChatID %d, NextSceneID %d: Failed to marshal 新的当前关系: %v", chatID, targetScene.Id, err)
			return nil, types.ErrMsgJsonMarshalFailed
		}
		newCurrentRelationshipJson := string(newCurrentRelBytes)

		// 9. 获取目标场景的对话总结和用户性格
		summaryListJson, userPersonalityJson := l.getTargetSceneSummaryAndUserPersonality(chatID, chat, targetScene.Id)

		// 10. --------------------- 模版处理 start ---------------------
		// Step 1: 获取基础模板字符串
		baseTemplateContent := chat.SystemPromptTemplate.String
		if !chat.SystemPromptTemplate.Valid || baseTemplateContent == "" {
			l.Errorf("ChatID %d: 基础模板内容为空或无效", chatID)
			return nil, types.ErrChatSystemPromptTemplateEmpty
		}

		// Step 2: 创建 TemplateManager 实例
		tm, err := util.NewTemplateManager(baseTemplateContent)
		if err != nil {
			l.Errorf("ChatID %d: Failed to create TemplateManager: %v", chatID, err)
			return nil, types.ErrTemplateManagerInitFailed
		}

		// Step 3: 加载当前对话的模板数据快照
		if chat.TemplateData.Valid && chat.TemplateData.String != "" {
			err = tm.LoadFromJson(chat.TemplateData.String)
			if err != nil {
				l.Errorf("ChatID %d: Failed to load current template data snapshot: %v", chatID, err)
				// Decide if this is a fatal error or if we can proceed with defaults loaded by NewTemplateManager
				return nil, types.ErrChatTemplateDataLoadFailed
			}
		} else {
			l.Infof("ChatID %d: No existing template data snapshot found. TemplateManager will use its defaults.", chatID)
		}

		// Step 4: 准备更新
		templateUpdates := map[string]interface{}{
			"relationship_char_current_setting": newCurrentRelationship.CharCSetting,
			"relationship_desc":                 newCurrentRelationship.RelationDesc,
			"allow_forbidden_behavior":          newCurrentRelationship.AllowForbiddenBehavior,
			"scene_name":                        targetScene.Name,
			"scene_content":                     targetScene.Description,
			"scene_limit":                       targetScene.Limit.String,
			"scene_guidance":                    targetScene.Guidance.String,
			"scene_img_gen":                     targetScene.ImgGenList.String,
			"scene_dialogue_example":            targetScene.DialogueExample.String,
		}
		if summaryListJson != "" && summaryListJson != "null" {
			templateUpdates["user_character_chat_summary"] = summaryListJson
		}
		if userPersonalityJson != "" && userPersonalityJson != "null" {
			templateUpdates["relationship_chat_summary"] = userPersonalityJson
		}

		newChatSystemPrompt, err := tm.UpdateTemplate(templateUpdates)
		if err != nil {
			l.Errorf("ChatID %d, NextSceneID %d: Failed to generate new system prompt using template: %v", chatID, targetScene.Id, err)
			return nil, types.ErrChatSystemPromptTemplateUpdateFailed
		}

		// Get updated template data snapshot
		updatedTemplateDataSnapshot := tm.GetCurrentData()
		updatedTemplateDataJsonBytes, err := json.Marshal(updatedTemplateDataSnapshot)
		if err != nil {
			l.Errorf("ChatID %d: Failed to marshal updated TemplateData snapshot: %v", chatID, err)
			return nil, types.ErrMsgJsonMarshalFailed
		}
		updatedTemplateDataJsonString := string(updatedTemplateDataJsonBytes)
		// 10. --------------------- 模版处理 end ---------------------

		// 11. 获取当前的阶段名称拼接 curToNextStage
		curStageName := newCurrentRelationship.StageName
		nextStageName := newCurrentRelationship.NextStageName
		var curToNextStage string
		if nextStageName != "" {
			curToNextStage = curStageName + " -> " + nextStageName
		} else {
			curToNextStage = curStageName
		}

		// 12. ---------------------场景切换时的亲密度记录 start---------------------
		var sceneSwitchIntimacyRecord []types.SceneSwitchIntimacyRecord
		var sceneSwitchIntimacyRecordJson string
		if chat.SceneSwitchIntimacyRecord.Valid && chat.SceneSwitchIntimacyRecord.String != "" {
			err = json.Unmarshal([]byte(chat.SceneSwitchIntimacyRecord.String), &sceneSwitchIntimacyRecord)
			if err != nil {
				l.Errorf("ChatID %d: 解析场景切换时的亲密度记录失败: %v", chatID, err)
				return nil, types.ErrMsgJsonUnmarshalFailed
			}
		}
		// 获取当前亲密度
		var currentIntimacy int64
		currentIntimacy, err = l.svcCtx.Model.ExtChatsModel.GetIntimacyLevelFromCache(l.ctx, chatID)
		if err != nil {
			l.Errorf("ChatID %d: 获取当前场景的亲密度失败: %v", chatID, err)
			return nil, types.ErrMsgCacheGetFailed
		}
		if isUpgradeScene { // 场景升级，把当前场景的亲密度记录加到历史记录中
			sceneSwitchIntimacyRecord = append(sceneSwitchIntimacyRecord, types.SceneSwitchIntimacyRecord{
				SceneID:  chat.SceneId,
				Intimacy: currentIntimacy,
			})
		} else {
			// 场景切换，更新亲密度记录
			var foundRecord bool
			for i := range sceneSwitchIntimacyRecord {
				//把当前场景的亲密度写到数据库中
				if sceneSwitchIntimacyRecord[i].SceneID == chat.SceneId {
					foundRecord = true
					sceneSwitchIntimacyRecord[i].Intimacy = currentIntimacy
				}
				// 把缓存里的亲密度设置成数据库中记录的要切换到的场景的亲密度值
				if sceneSwitchIntimacyRecord[i].SceneID == targetScene.Id {
					// 把缓存中的Intimacy改为记录的亲密度
					err := l.svcCtx.Model.ExtChatsModel.UpdateIntimacyLevelCache(l.ctx, chatID, sceneSwitchIntimacyRecord[i].Intimacy)
					if err != nil {
						l.Errorf("ChatID %d: 更新亲密度缓存失败: %v", chatID, err)
						return nil, types.ErrMsgCacheSetFailed
					}
				}
			}
			if !foundRecord { //高场景往回切的时候数据库中还没有记录，就应该把当前场景的亲密度记录加到历史记录中
				sceneSwitchIntimacyRecord = append(sceneSwitchIntimacyRecord, types.SceneSwitchIntimacyRecord{
					SceneID:  chat.SceneId,
					Intimacy: currentIntimacy,
				})
			}
		}
		sceneSwitchIntimacyRecordJsonBytes, err := json.Marshal(sceneSwitchIntimacyRecord)
		if err != nil {
			l.Errorf("ChatID %d: Failed to marshal 场景切换时的亲密度记录: %v", chatID, err)
			return nil, types.ErrMsgJsonMarshalFailed
		}
		sceneSwitchIntimacyRecordJson = string(sceneSwitchIntimacyRecordJsonBytes)
		// 12. ---------------------场景切换时的亲密度记录 end---------------------

		// 13. 构建更新数据
		updateData := map[string]any{
			"scene_id":                     targetScene.Id,
			"current_relationship":         newCurrentRelationshipJson,
			"scene_relationship":           targetScene.Relationship.String,
			"img_list":                     targetScene.ImageList.String,
			"chat_system_prompt":           newChatSystemPrompt,
			"template_data":                updatedTemplateDataJsonString, // Save the new snapshot
			"prompt_version":               sqlbuilder.Raw("prompt_version + 1"),
			"cur_to_next_stage":            sql.NullString{String: curToNextStage, Valid: curToNextStage != ""},
			"scene_switch_intimacy_record": sql.NullString{String: sceneSwitchIntimacyRecordJson, Valid: true},
		}
		if isUpgradeScene {
			updateData["highest_scene_id"] = targetScene.Id
			updateData["highest_scene_relationship_name"] = sql.NullString{String: curStageName, Valid: true}
		}

		rowsAffected, err := l.svcCtx.Model.ExtChatsModel.UpdateFieldsByConditionWithRowsAffected(l.ctx, nil, updateData,
			condition.Condition{Field: "id", Operator: condition.Equal, Value: chatID},
			condition.Condition{Field: "prompt_version", Operator: condition.Equal, Value: chat.PromptVersion})

		if err != nil {
			l.Errorf("ChatID %d: 更新数据库失败: %v. RowsAffected: %d", chatID, err, rowsAffected)
			if rowsAffected == 0 {
				retryCount++
				l.Infof("ChatID %d: 乐观锁失败或未更新行，第%d次重试", chatID, retryCount)
				return updateWithRetry(chatID)
			}
			return nil, types.ErrChatUpgradeSceneFailed
		}

		if rowsAffected == 0 {
			retryCount++
			l.Infof("ChatID %d: 更新时影响行数为0 (无错误，但可能并发冲突)，第%d次重试", chatID, retryCount)
			return updateWithRetry(chatID)
		}

		l.Infof("ChatID %d: 成功切换场景到 %d (NextSceneID)", chatID, targetScene.Id)
		// 删除Chat的缓存
		v, err := l.svcCtx.Model.ExtChatsModel.DelCache(l.ctx, chatID)
		if err != nil {
			l.Errorf("ChatID %d: 删除缓存失败: %v", chatID, err)
		} else {
			l.Infof("ChatID %d: 删除缓存成功, 影响行数: %d", chatID, v)
		}

		updatedChat, err := l.svcCtx.Model.ExtChatsModel.FindOneWithCache(l.ctx, nil, chatID)
		if err != nil {
			l.Errorf("ChatID %d: 切换后重新获取对话信息失败: %v", chatID, err)
			return nil, types.ErrChatQueryFailed
		}

		interactionCount, lastMessageJson, intimacyLevel, err := l.svcCtx.Model.ExtChatsModel.GetChatPreviewCache(l.ctx, chatID)
		if err != nil {
			l.Errorf("ChatID %d: 获取对话预览缓存失败: %v", chatID, err)
			return nil, types.ErrChatPreviewCacheFailed
		}

		resp = &types.Chat{
			ID:                   updatedChat.Id,
			UserID:               updatedChat.UserId,
			CharacterID:          updatedChat.CharacterId,
			SceneId:              updatedChat.SceneId,
			CurToNextStage:       updatedChat.CurToNextStage.String,
			IntimacyLevel:        intimacyLevel,
			PersonaID:            updatedChat.PersonaId.Int64,
			Title:                updatedChat.Title.String,
			LastMessage:          lastMessageJson,
			InteractionCount:     interactionCount,
			Share:                updatedChat.Share,
			LastMessageTimestamp: updatedChat.LastMessageTimestamp.Format(time.RFC3339),
			CreatedAt:            updatedChat.CreatedAt.Format(time.RFC3339),
		}

		// 如果是场景升级，则需要发送场景切换后的初始消息
		if isUpgradeScene {
			// 保存场景变换后的初始消息
			l.sendSaveChatTask(chatID, updatedChat, targetScene.Greeting)

			// ------ 生成对话总结 ------
			l.sendGenerateSummaryTask(chatID, updatedChat, targetScene.Id)
		}

		return resp, nil
	}

	return updateWithRetry(chatID)
}

func (l *SwitchChatScene) getTargetSceneSummaryAndUserPersonality(chatID int64, chat *_04_chats.Chats, targetSceneID int64) (string, string) {
	// 切换模版中的user_character_chat_summary
	var summaryListJson string
	var chatSummaryList []types.SummaryWithScene
	if chat.ChatSummary.Valid && chat.ChatSummary.String != "" {
		err := json.Unmarshal([]byte(chat.ChatSummary.String), &chatSummaryList)
		if err != nil {
			l.Logger.Errorf("SummaryWorker: Failed to unmarshal existing chat summary for chat %d: %v", chatID, err)
			chatSummaryList = []types.SummaryWithScene{}
		}
	}
	// 获取目标场景的对话总结
	var summaryList = []string{}
	for _, s := range chatSummaryList {
		if s.SceneID == targetSceneID {
			summaryList = append(summaryList, s.Summary)
		}
	}
	// 准备用于 TemplateManager 的更新
	summaryListBytes, marshalErr := json.Marshal(summaryList)
	if marshalErr == nil {
		summaryListJson = string(summaryListBytes)
	} else {
		l.Logger.Errorf("Failed to marshal chat summary list for chat %d: %v", chatID, marshalErr)
	}

	// 切换模版中的relationship_chat_summary
	var userPersonalityJson string
	var userProfileList []types.PersonalityWithScene
	if chat.ChatUserPersonality.Valid && chat.ChatUserPersonality.String != "" {
		err := json.Unmarshal([]byte(chat.ChatUserPersonality.String), &userProfileList)
		if err != nil {
			l.Logger.Errorf("Failed to unmarshal user profile list for chat %d: %v", chatID, err)
			userProfileList = []types.PersonalityWithScene{}
		}
	}
	//获取目标场景的人物性格
	var personalityList = []string{}
	for _, s := range userProfileList {
		if s.SceneID == targetSceneID {
			personalityList = append(personalityList, s.Personality)
		}
	}
	// 准备用于 TemplateManager 的更新
	userPersonalityBytes, marshalErr := json.Marshal(personalityList)
	if marshalErr == nil {
		userPersonalityJson = string(userPersonalityBytes)
	} else {
		l.Logger.Errorf("Failed to marshal chat summary list for chat %d: %v", chatID, marshalErr)
	}
	return summaryListJson, userPersonalityJson
}

func (l *SwitchChatScene) sendGenerateSummaryTask(chatID int64, chat *_04_chats.Chats, sceneID int64) {
	generateSummaryQueue := l.svcCtx.GenerateSummaryQueue

	latestMessages, err := l.svcCtx.Model.ExtChatsModel.GetRecentMessagesFromCacheFilteredBySceneID(l.ctx, chatID, sceneID)
	if err != nil {
		l.Errorf("ChatID %d: 获取对话历史消息失败: %v", chatID, err)
		return
	}

	generateSummaryQueue <- types.GenerateSummaryTask{
		ChatID:           chatID,
		UserID:           chat.UserId,
		CharacterID:      chat.CharacterId,
		LastMessagesList: latestMessages,
	}
}

func (l *SwitchChatScene) sendSaveChatTask(chatID int64, chat *_04_chats.Chats, greeting string) {
	saveChatQueue := l.svcCtx.SaveChatQueue
	saveChatQueue <- types.SaveChatTask{
		ChatID:           chatID,
		UserID:           chat.UserId,
		CharacterID:      chat.CharacterId,
		Role:             types.ChatMessageRoleAssistant,
		Content:          greeting,
		SceneID:          chat.SceneId,
		InteractionCount: chat.InteractionCount,
		Type:             types.ChatMessageTypeMessage,
		NeedUnlock:       types.ChatMessageNoUnlock,
		UnlockContent:    "",
	}
}
