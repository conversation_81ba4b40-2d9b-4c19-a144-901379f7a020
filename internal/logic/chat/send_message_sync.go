package chat

import (
	"database/sql"
	"encoding/json"
	"github.com/huandu/go-sqlbuilder"
	"github.com/jzero-io/jzero-contrib/condition"
	"io"
	"net/http"
	"slices"
	"strings"
	"time"
	dbChats "xai.backend/internal/model/04_chats"
	"xai.backend/internal/service"
	"xai.backend/internal/util"
	"xai.backend/internal/util/ctxutil"
	"xai.backend/internal/util/llmutil"

	"context"

	"xai.backend/internal/svc"
	"xai.backend/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SendMessageSync struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	r      *http.Request
}

// NewSendMessageSync 发送消息(同步消息)
func NewSendMessageSync(ctx context.Context, svcCtx *svc.ServiceContext, r *http.Request) *SendMessageSync {
	return &SendMessageSync{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
		r:      r,
	}
}

func (l *SendMessageSync) SendMessageSync(req *types.SendMessageReq) (resp *types.SendMessageSyncResp, err error) {
	reqCtx := l.ctx                       // l.ctx 是原始请求上下文，与客户端连接相关
	processingCtx := context.Background() // 用于必须完成的后端处理

	startTime := time.Now()
	// -- 1. 解析 Chat ID --
	chatId := req.ID
	if chatId <= 0 {
		l.Logger.Errorf("无效的对话ID格式: %d", chatId)
		// 通过 channel 发送错误并结束
		return nil, types.ErrChatNotFound
	}

	if strings.TrimSpace(req.Content) == "" ||
		strings.TrimSpace(req.Content) == "（）" || strings.TrimSpace(req.Content) == "()" {
		l.Logger.Errorf("无效的消息内容: %s", req.Content)
		return nil, types.ErrorInvalidMessageContent
	}

	// -- 2. 获取 UserID (提前获取，用于用户消息任务) --
	userId, userIdErr := ctxutil.GetUserIDFromCtx(l.ctx)
	if userIdErr != nil {
		l.Logger.Errorf("无法从上下文获取 UserID: %v. 中止.", userIdErr)
		return nil, types.ErrGetUserIDFailed
	}

	// -- 3 检查用户是否为用户的对话，是否越权 --
	// -- 3.1. 获取 Chat Info --
	chatInfo, err := l.svcCtx.Model.ChatsModel.FindOne(processingCtx, nil, chatId)
	if err != nil {
		l.Logger.Errorf("获取对话信息失败: %v", err)
		return nil, types.ErrMsgDBQueryFailed
	}
	if chatInfo.UserId != userId {
		l.Logger.Errorf("用户 %d 越权访问对话 %d", userId, chatId)
		return nil, types.ErrChatPermissionCheckFailed
	}
	// -- 3.2. 获取场景信息 --
	currentScene, err := l.svcCtx.Model.ScenesModel.FindOneWithCache(processingCtx, nil, chatInfo.SceneId)
	if err != nil {
		l.Logger.Errorf("获取场景信息失败: %v", err)
		return nil, types.ErrChatSceneQueryFailed
	}
	sceneIntimacyMin := currentScene.IntimacyMin
	sceneIntimacyMax := currentScene.IntimacyMax

	// -- 4. 获取用户订阅计划 --
	authService := service.NewSubscriptionAuthService(processingCtx, l.svcCtx)
	userActivePlanID, err := authService.GetUserActivePlanID(userId)
	if err != nil {
		l.Logger.Errorf("获取用户订阅状态失败: %v", err)
		return nil, types.ErrorGetUserActivePlanIDFailed
	}
	// -- 5. 用量检查 (检查 非VIP用户 每日消息数) --
	featureKey := types.FeatureChatMessageLimitPerDayPerCharacter
	err, reachedLimit := authService.CheckUsageLimitForSyncMessage(userId, req.CharacterID, userActivePlanID, featureKey)
	if err != nil || reachedLimit {
		l.Logger.Errorf("用量检查失败: %v", err)
		userPrompt := types.UserPrompt{IntimacyLevel: 0, UserPrompt: types.MessagesLimitPrompt}
		userPromptJson, err := json.Marshal(userPrompt)
		if err != nil {
			l.Logger.Errorf("Failed to marshal 亲密度变化: %v", err)
		}
		response := &types.SendMessageSyncResp{
			Events: []types.EventInfo{
				{Type: types.StreamChunkEventMessagesReachedLimit, Content: string(userPromptJson)},
			},
		}
		return response, types.ErrorUserOverLimit
	}

	// -- 6. 获取 LLM 配置 (使用工具类) --
	llmConfig, err := llmutil.GetLLMConfig(processingCtx, req.CharacterID, l.svcCtx.Model.LLMConfigCharacterModel, l.svcCtx.Model.LLMConfigModel)
	if err != nil {
		l.Logger.Errorf("获取 LLM 配置失败: %v", err)
		return nil, types.ErrorSystemConfigError
	}

	// -- 8. 构造 LLM 请求体 (调用新方法) --
	// -- 8.1. 从缓存查询对话的最近20轮消息, 带场景ID过滤 --
	lastMessages, err := l.svcCtx.Model.ExtChatsModel.GetRecentMessagesFromCacheFilteredBySceneID(processingCtx, chatId, chatInfo.SceneId)
	if err != nil {
		l.Logger.Errorf("获取对话历史消息失败: %v", err)
		return nil, types.ErrMsgCacheGetFailed
	}

	logx.Infof("lastMessages: %+v", lastMessages)

	// -- 8.2. 发送用户消息保存任务 (使用公共方法) --
	// user 消息， InteractionCount用不到，传入 -1
	l.queueSaveChatMessageTask(chatId, userId, req.CharacterID, types.ChatMessageRoleUser, req.Content, -1,
		chatInfo.SceneId, types.ChatMessageTypeMessage, types.ChatMessageNoUnlock, "")

	// -- 8.3. 调用新方法构建 Payload --
	payloadBytes, err := llmutil.BuildLLMRequestPayload(chatId, chatInfo.ChatSystemPrompt.String, lastMessages, req.Content, llmConfig.ModelName, false, true)
	if err != nil {
		return nil, types.ErrChatLLMMessageBuildFailed
	}

	// -- 9. 发送请求 (使用工具类) --
	logx.Infof("start send request to llm: %s", llmConfig.ApiEndpoint)
	llmResp, err := llmutil.ExecuteLLMHttpRequest(processingCtx, llmConfig.ApiEndpoint, llmConfig.ApiKey, payloadBytes)
	if err != nil {
		l.Logger.Errorf("调用 LLM API 失败: %v", err)
		return nil, types.ErrorChatSendMessageFailed
	}
	// 注意: ExecuteLLMHttpRequest 不负责关闭 Body 和检查 StatusCode，由调用方处理

	logx.Infof("llm request success, status code: %d", llmResp.StatusCode)
	if llmResp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(llmResp.Body) // 读取错误信息
		l.Logger.Errorf("LLM API 返回非 OK 状态: %d - 主体: %s", llmResp.StatusCode, string(bodyBytes))
		err := llmResp.Body.Close()
		if err != nil {
			l.Logger.Errorf("<UNK> LLM API <UNK>: %v", err)
		} // 手动关闭 body
		return nil, types.ErrorLLMServiceReturnErrorStatus
	}

	// -- 10. 处理成功的响应 --
	capturedChatInfo := chatInfo
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			l.Logger.Errorf("Failed to close LLM response body: %v", err)
		}
	}(llmResp.Body)
	bodyBytes, err := io.ReadAll(llmResp.Body)
	if err != nil {
		l.Logger.Errorf("Failed to read LLM API response body: %v", err)
		return nil, types.ErrorLLMServiceReturnErrorStatus
	}
	l.Logger.Infof("LLM Raw Response: %s", string(bodyBytes))

	var response types.ChatCompletionResponse
	err = json.Unmarshal(bodyBytes, &response)
	if err != nil {
		l.Logger.Errorf("Failed to unmarshal LLM API response: %v", err)
		return nil, types.ErrMsgJsonUnmarshalFailed
	}
	responseContent := response.Choices[0].Message.Content
	l.Logger.Infof("LLM Response Content: %s", responseContent)
	assistantReply := types.AssistantReply{}
	err = json.Unmarshal([]byte(responseContent), &assistantReply)
	if err != nil {
		l.Logger.Errorf("Failed to unmarshal assistant reply response: %v", err)
		return nil, types.ErrMsgJsonUnmarshalFailed
	}
	l.Logger.Infof("Assistant Reply: %+v", assistantReply)

	// -- 11. 保存助手消息 (如果流未出错) --
	l.queueSaveChatMessageTask(chatId, userId, req.CharacterID,
		types.ChatMessageRoleAssistant, assistantReply.ReplyText, chatInfo.InteractionCount,
		chatInfo.SceneId, types.ChatMessageTypeMessage, types.ChatMessageNoUnlock, "")

	intimacyChanged := assistantReply.Score
	imgGenStr := assistantReply.PictureName
	l.Logger.Infof("intimacyChanged: %d, imgGenStr: %s", intimacyChanged, imgGenStr)
	var events []types.EventInfo

	// -- 12. 处理亲密度变化和配图 --
	cachedIntimacyLevel, err := l.svcCtx.Model.ExtChatsModel.GetIntimacyLevelFromCache(processingCtx, chatId)
	if err != nil {
		l.Logger.Errorf("Failed to get 亲密度 from cache: 对话ID: %d: %v", chatId, err)
		return
	}
	var computedNewIntimacyLevel = cachedIntimacyLevel
	// 更新亲密度和提示词
	if intimacyChanged != 0 {
		computedNewIntimacyLevel = cachedIntimacyLevel + intimacyChanged
		// 亲密度等级, 最低值为当前场景的亲密度下限
		if computedNewIntimacyLevel < sceneIntimacyMin {
			computedNewIntimacyLevel = sceneIntimacyMin
		}
		// 亲密度等级, 最高值为当前场景的亲密度上限
		if computedNewIntimacyLevel > sceneIntimacyMax {
			computedNewIntimacyLevel = sceneIntimacyMax
		}
		l.Logger.Infof("cachedIntimacyLevel: %d, computedNewIntimacyLevel: %d", cachedIntimacyLevel, computedNewIntimacyLevel)
		if computedNewIntimacyLevel != cachedIntimacyLevel {
			l.Logger.Infof("computedNewIntimacyLevel: %d, cachedIntimacyLevel: %d", computedNewIntimacyLevel, cachedIntimacyLevel)
			eventInfos, err := l.updateIntimacyAndChatSystemPrompt(processingCtx, reqCtx, chatId, computedNewIntimacyLevel, cachedIntimacyLevel, capturedChatInfo)
			if err != nil {
				return nil, err
			}
			if len(eventInfos) > 0 {
				for _, eventInfo := range eventInfos {
					if eventInfo != nil {
						events = append(events, *eventInfo)
					}
				}
			}
		}

		// 如果亲密度达到当前关系最大值，发送 提示信息给 前端
		if computedNewIntimacyLevel == l.getChatSceneRelationshipIntimacyMax(processingCtx, capturedChatInfo) {
			// 判断角色是否有多场景，当前场景是否是最后一个场景，如果不是最后一个场景则发送消息给前端提示升级场景
			eventInfo, err := l.handleSceneRelationshipUpgrade(processingCtx, reqCtx, chatId, capturedChatInfo, computedNewIntimacyLevel)
			if err != nil {
				return nil, err
			}
			if eventInfo != nil {
				events = append(events, *eventInfo)
			}
		}

	}

	// 处理配图 (传递 userId)
	l.Logger.Infof("handleImageGeneration, imgGenStr: %s", imgGenStr)
	if imgGenStr != "" && imgGenStr != "无" {
		eventInfo, err := l.handleImageGeneration(processingCtx, reqCtx, chatId, userId, computedNewIntimacyLevel, imgGenStr, userActivePlanID, capturedChatInfo, authService)
		if err != nil {
			return nil, err
		}
		if eventInfo != nil {
			events = append(events, *eventInfo)
		}
	}

	resp = &types.SendMessageSyncResp{
		Message: types.ChatMessage{
			Role:       types.ChatMessageRoleAssistant,
			Content:    assistantReply.ReplyText,
			Type:       types.ChatMessageTypeMessage,
			SceneId:    chatInfo.SceneId,
			NeedUnlock: types.ChatMessageNoUnlock,
		},
		Events: events,
	}

	l.Logger.Infof("SendMessageSync 主函数返回, 对话ID: %d, resp: %+v", chatId, resp)
	l.Logger.Infof("SendMessageSync 总耗时: %v", time.Now().Sub(startTime))
	return resp, nil
}

// -- 新增：排队保存聊天消息任务的公共方法 --
func (l *SendMessageSync) queueSaveChatMessageTask(chatID int64, userID int64, characterID int64, role string, content string, interactionCount int64, sceneID int64, msgType string, needUnlock int64, unlockContent string) {
	if content == "" {
		l.Logger.Infof("跳过队列空 %s 消息: 对话ID: %d", role, chatID)
		return
	}
	// 如果 needUnlock 为 0 不需要解锁，则 unlockContent 为空
	if needUnlock == types.ChatMessageNoUnlock {
		unlockContent = ""
	}

	saveTask := types.SaveChatTask{
		ChatID:           chatID,
		UserID:           userID,
		CharacterID:      characterID,
		Role:             role,
		Content:          content,
		SceneID:          sceneID,
		InteractionCount: interactionCount,
		Type:             msgType,
		NeedUnlock:       needUnlock,
		UnlockContent:    unlockContent,
	}

	select {
	case l.svcCtx.SaveChatQueue <- saveTask:
		l.Logger.Infof("队列 %s SaveChatTask: 对话ID: %d, 内容: %s", role, chatID, content)
	default:
		l.Logger.Infof("SaveChatQueue 已满, 丢弃 %s 任务: 对话ID: %d, 内容: %s", role, chatID, content)
	}
}

func (l *SendMessageSync) updateIntimacyAndChatSystemPrompt(processingCtx, reqCtx context.Context, chatId int64,
	newIntimacyLevel int64, cachedIntimacyLevel int64, chatInfo *dbChats.Chats) ([]*types.EventInfo, error) {
	l.Logger.Infof("处理亲密度更新: 对话ID: %d, 新亲密度: %d", chatId, newIntimacyLevel)
	events := make([]*types.EventInfo, 0)

	// 1. 检查是否需要更新亲密度
	if newIntimacyLevel == cachedIntimacyLevel {
		l.Logger.Infof("对话ID: %d 已经是所需亲密度等级.", chatId)
		return nil, nil
	}

	// 2. 需要更新, 继续
	// 首先尝试反序列化当前关系
	var currentRelationshipBeforeUpdate types.SceneRelationship
	isValidCurrentRelationship := false
	if chatInfo.CurrentRelationship.Valid && chatInfo.CurrentRelationship.String != "" {
		err := json.Unmarshal([]byte(chatInfo.CurrentRelationship.String), &currentRelationshipBeforeUpdate)
		if err != nil {
			l.Logger.Errorf("Failed to unmarshal 当前关系: 对话ID: %d: %v. 将视为无有效当前关系.", chatId, err)
			// isValidCurrentRelationship 保持 false
		} else {
			l.Logger.Infof("currentRelationshipBeforeUpdate: %+v", currentRelationshipBeforeUpdate)
			isValidCurrentRelationship = true
		}
	} else {
		l.Logger.Infof("对话ID: %d 没有当前关系设置或为空.", chatId)
	}

	// 3. 根据是否有有效的当前关系以及亲密度是否在其区间内来决定如何更新
	if isValidCurrentRelationship && (newIntimacyLevel < currentRelationshipBeforeUpdate.IntimacySubMin || newIntimacyLevel > currentRelationshipBeforeUpdate.IntimacySubMax) {
		// 亲密度不在当前关系区间内 (或者当前关系无效)，则遍历场景关系列表，找到适合的亲密度区间
		l.Logger.Infof("对话ID: %d 不在当前关系亲密度区间内或当前关系无效. 尝试寻找新关系.", chatId)

		eventInfos, err := l._updateIntimacyAndPromptForNewRelationship(processingCtx, reqCtx, chatId, newIntimacyLevel, cachedIntimacyLevel, chatInfo)
		if err != nil {
			return nil, err
		}
		if len(eventInfos) > 0 {
			events = append(events, eventInfos...)
		}
	} else if isValidCurrentRelationship {
		// 亲密度在当前关系区间内 (且当前关系有效)，则直接更新亲密度
		l.Logger.Infof("对话ID: %d 亲密度在当前关系区间内. 更新亲密度等级.", chatId)
		eventInfo, err := l._updateIntimacyLevelOnly(processingCtx, chatId, newIntimacyLevel)
		if err != nil {
			l.Logger.Errorf("Failed to update 缓存亲密度: 对话 ID: %d. 错误: %v", chatId, err)
		} else if eventInfo != nil {
			events = append(events, eventInfo)
		}
	} else {
		// 当前关系无效，且上面没有进入寻找新关系的逻辑（这种情况理论上已经被第一个if分支覆盖，但作为保障）
		// 通常，如果没有当前关系，我们可能也需要走寻找新关系的逻辑，或者至少更新亲密度。
		// 这里的逻辑分支可能需要根据业务需求细化：如果没有当前关系，是否默认尝试寻找一个新关系？
		// 当前实现下，如果isValidCurrentRelationship为false，会跳过两个更新分支。
		// 如果希望没有当前关系时也尝试寻找新关系，可以将上面的if条件调整或在此处调用 _updateIntimacyAndPromptForNewRelationship
		l.Logger.Infof("对话ID: %d 没有有效的当前关系，且未触发寻找新关系（新亲密度可能仍在旧的无效区间内，或无场景列表）. 仅记录，不直接更新.", chatId)
	}
	if len(events) > 0 {
		return events, nil
	}
	return nil, nil
}

// _updateIntimacyAndPromptForNewRelationship 处理亲密度和系统提示的更新，当找到新的关系时。
func (l *SendMessageSync) _updateIntimacyAndPromptForNewRelationship(processingCtx, reqCtx context.Context, chatId int64,
	newIntimacyLevel int64, cachedIntimacyLevel int64, chatInfo *dbChats.Chats) ([]*types.EventInfo, error) {
	const maxRetries = types.ChatSystemPromptMaxRetries // 最大重试次数
	var retryCount int                                  // 内部重试计数器
	var events []*types.EventInfo

	// chatInfoForRetry is the state of chat for the current attempt
	var updateWithRetry func(processingCtx, reqCtx context.Context, chatId int64, newIntimacyLevel int64, chatInfoForRetry *dbChats.Chats)
	updateWithRetry = func(processingCtx, reqCtx context.Context, chatId int64, newIntimacyLevel int64, chatInfoForRetry *dbChats.Chats) {
		var sceneRelationshipList []types.SceneRelationship
		if chatInfoForRetry.SceneRelationship.Valid && chatInfoForRetry.SceneRelationship.String != "" {
			err := json.Unmarshal([]byte(chatInfoForRetry.SceneRelationship.String), &sceneRelationshipList)
			if err != nil {
				l.Logger.Errorf("Failed to unmarshal 场景关系列表: 对话ID: %d: %v", chatId, err)
				eventInfo, err := l._updateIntimacyLevelOnly(processingCtx, chatId, newIntimacyLevel)
				if err != nil {
					return
				}
				if eventInfo != nil {
					events = append(events, eventInfo)
				}
				return
			}
		} else {
			l.Logger.Infof("对话ID: %d 没有场景关系列表定义. 尝试仅更新亲密度.", chatId)
			eventInfo, err := l._updateIntimacyLevelOnly(processingCtx, chatId, newIntimacyLevel)
			if err != nil {
				return
			}
			if eventInfo != nil {
				events = append(events, eventInfo)
			}
			return
		}

		foundNewRelationship := false
		for i := range sceneRelationshipList {
			relationship := &sceneRelationshipList[i]
			if newIntimacyLevel >= relationship.IntimacySubMin && newIntimacyLevel <= relationship.IntimacySubMax {
				l.Logger.Infof("找到亲密度区间: %d-%d 匹配关系: %s", relationship.IntimacySubMin, relationship.IntimacySubMax, relationship.SN)
				foundNewRelationship = true

				l.Logger.Infof("对话ID: %d 更新亲密度等级为 %d.", chatId, newIntimacyLevel)
				eventInfo, err := l._updateIntimacyLevelOnly(processingCtx, chatId, newIntimacyLevel)
				if err != nil {
					return
				}
				if eventInfo != nil {
					events = append(events, eventInfo)
				}

				// 如果亲密度掉出/进入新的区间段，发送 提示信息给 前端
				eventInfo2 := l.sendUserEventWhenIntimacyLevelChangedWithSubScene(newIntimacyLevel, cachedIntimacyLevel, chatInfo)
				if eventInfo2 != nil {
					events = append(events, eventInfo2)
				}

				// 在尝试更新前刷新 ChatInfo 以获取最新的 PromptVersion 和模板数据
				refreshedChatInfo, err := l.svcCtx.Model.ChatsModel.FindOne(processingCtx, nil, chatId)
				if err != nil {
					l.Logger.Errorf("Failed to refresh chatInfo before updating prompt for new relationship: 对话ID: %d: %v", chatId, err)
					// Fallback to potentially stale info if refresh fails, with a log.
					if chatInfoForRetry == nil {
						l.Logger.Errorf("对话ID: %d: chatInfoForRetry is nil after refresh failure, cannot proceed with template update logic.", chatId)
						return // Critical error
					}
					refreshedChatInfo = chatInfoForRetry
					l.Logger.Infof("对话ID: %d: 使用可能过时的 chatInfo 数据进行模板更新，因为刷新失败。", chatId) // Warnf changed to Infof
				}

				// --- 使用 TemplateManager 更新系统提示词和模板数据 ---
				var newSystemPrompt string
				var updatedTemplateDataJsonString string

				baseSystemPrompt := refreshedChatInfo.SystemPromptTemplate.String
				if !refreshedChatInfo.SystemPromptTemplate.Valid || baseSystemPrompt == "" {
					l.Logger.Errorf("对话ID: %d: 基础系统提示词模板为空或无效，无法使用TemplateManager更新关系相关的提示词部分。", chatId)
					newSystemPrompt = refreshedChatInfo.ChatSystemPrompt.String           // Keep existing rendered prompt
					updatedTemplateDataJsonString = refreshedChatInfo.TemplateData.String // Keep existing template data
				} else {
					tm, tmErr := util.NewTemplateManager(baseSystemPrompt)
					if tmErr != nil {
						l.Logger.Errorf("对话ID: %d: 创建TemplateManager失败: %v. 将使用旧的提示词和模板数据。", chatId, tmErr)
						newSystemPrompt = refreshedChatInfo.ChatSystemPrompt.String
						updatedTemplateDataJsonString = refreshedChatInfo.TemplateData.String
					} else {
						// 加载现有的模板数据（如果存在）
						if refreshedChatInfo.TemplateData.Valid && refreshedChatInfo.TemplateData.String != "" {
							if loadErr := tm.LoadFromJson(refreshedChatInfo.TemplateData.String); loadErr != nil {
								l.Logger.Infof("对话ID: %d: 加载现有TemplateData失败: %v. TemplateManager将使用其默认/初始数据。", chatId, loadErr) // Warnf changed to Infof
							}
						}

						templateUpdates := map[string]interface{}{
							"relationship_desc":                 relationship.RelationDesc,
							"relationship_char_current_setting": relationship.CharCSetting,
							"allow_forbidden_behavior":          relationship.AllowForbiddenBehavior,
						}

						var updateErr error
						newSystemPrompt, updateErr = tm.UpdateTemplate(templateUpdates)
						if updateErr != nil {
							l.Logger.Errorf("对话ID: %d: 使用TemplateManager更新提示词失败: %v. 将使用旧的提示词。", chatId, updateErr)
							newSystemPrompt = refreshedChatInfo.ChatSystemPrompt.String
							updatedTemplateDataJsonString = refreshedChatInfo.TemplateData.String
						} else {
							updatedTemplateDataSnapshot := tm.GetCurrentData()
							updatedTemplateDataJsonBytes, marshalErr := json.Marshal(updatedTemplateDataSnapshot)
							if marshalErr != nil {
								l.Logger.Errorf("对话ID %d: Failed to marshal updated TemplateData snapshot for new relationship: %v. 将使用旧的模板数据。", chatId, marshalErr)
								updatedTemplateDataJsonString = refreshedChatInfo.TemplateData.String
							} else {
								updatedTemplateDataJsonString = string(updatedTemplateDataJsonBytes)
								l.Logger.Infof("对话ID: %d: TemplateManager 成功生成新的提示词和模板数据。", chatId)
							}
						}
					}
				}
				// --- TemplateManager 逻辑结束 ---

				l.Logger.Infof("新的 newSystemPrompt (来自TemplateManager): %s", newSystemPrompt)
				l.Logger.Infof("新的 updatedTemplateDataJsonString: %s", updatedTemplateDataJsonString)

				newCurrentRelBytes, err := json.Marshal(relationship)
				if err != nil {
					l.Logger.Errorf("Failed to marshal 新的当前关系 SN %s: 对话ID: %d: %v", relationship.SN, chatId, err)
					eventInfo, err := l._updateIntimacyLevelOnly(processingCtx, chatId, newIntimacyLevel)
					if err != nil {
						return
					}
					if eventInfo != nil {
						events = append(events, eventInfo)
					}
					return
				}
				newCurrentRelJson := string(newCurrentRelBytes)
				l.Logger.Infof("新的 newCurrentRelJson: %s", newCurrentRelJson)

				curStageName := relationship.StageName
				nextStageName := relationship.NextStageName
				var curToNextStage string
				if nextStageName != "" {
					curToNextStage = curStageName + " -> " + nextStageName
				} else {
					curToNextStage = curStageName
				}

				updateData := map[string]any{
					"current_relationship": newCurrentRelJson,
					"intimacy_level":       newIntimacyLevel,
					"chat_system_prompt":   newSystemPrompt,
					"template_data":        updatedTemplateDataJsonString,
					"prompt_version":       sqlbuilder.Raw("prompt_version + 1"),
					"cur_to_next_stage":    curToNextStage,
				}
				//如果当前场景的ID是最高曾经到达的场景，则更新最高场景的人物关系名称
				if chatInfo.HighestSceneId == chatInfo.SceneId {
					updateData["highest_scene_relationship_name"] = sql.NullString{String: curStageName, Valid: true}
				}

				rowsAffected, dbUpdateErr := l.svcCtx.Model.ExtChatsModel.UpdateFieldsByConditionWithRowsAffected(processingCtx, nil, updateData,
					condition.Condition{Field: "id", Operator: condition.Equal, Value: chatId},
					condition.Condition{Field: "prompt_version", Operator: condition.Equal, Value: refreshedChatInfo.PromptVersion})

				if dbUpdateErr != nil {
					l.Logger.Errorf("Failed to update 系统提示/关系/亲密度/模板数据: 对话ID: %d: %v", chatId, dbUpdateErr)
					if rowsAffected == 0 {
						if retryCount >= maxRetries {
							l.Logger.Errorf("乐观锁重试次数超过最大限制(%d次), 对话ID: %d", maxRetries, chatId)
							return
						}
						retryCount++
						l.Logger.Infof("数据库更新失败且影响行数为0 (可能乐观锁)，第%d次重试: 对话ID: %d", retryCount, chatId)
						latestChatInfo, refreshErr := l.svcCtx.Model.ChatsModel.FindOne(processingCtx, nil, chatId)
						if refreshErr != nil {
							l.Logger.Errorf("Failed to refresh chatInfo after optimistic lock failure for retry: 对话ID: %d: %v", chatId, refreshErr)
							return
						}
						updateWithRetry(processingCtx, reqCtx, chatId, newIntimacyLevel, latestChatInfo)
						return
					}
					return
				} else { // dbUpdateErr is nil , update succeed
					if rowsAffected == 0 {
						if retryCount >= maxRetries {
							l.Logger.Errorf("乐观锁重试次数超过最大限制(%d次) (0行受影响), 对话ID: %d", maxRetries, chatId)
							return
						}
						retryCount++
						l.Logger.Infof("乐观锁失败 (0行受影响)，第%d次重试: 对话ID: %d", retryCount, chatId)
						latestChatInfo, refreshErr := l.svcCtx.Model.ChatsModel.FindOne(processingCtx, nil, chatId)
						if refreshErr != nil {
							l.Logger.Errorf("Failed to refresh chatInfo after optimistic lock failure for retry (0 rows): 对话ID: %d: %v", chatId, refreshErr)
							return
						}
						updateWithRetry(processingCtx, reqCtx, chatId, newIntimacyLevel, latestChatInfo)
						return
					}

					l.Logger.Infof("成功更新 系统提示/关系/亲密度/模板数据: 对话ID: %d.", chatId)
					// chatInfo is the parameter of the outer _updateIntimacyAndPromptForNewRelationship function.
					// It represents the capturedChatInfo from the calling goroutine, which needs to be updated.
					chatInfo.ChatSystemPrompt = sql.NullString{String: newSystemPrompt, Valid: newSystemPrompt != "" || (refreshedChatInfo.SystemPromptTemplate.Valid && refreshedChatInfo.SystemPromptTemplate.String != "")}
					chatInfo.TemplateData = sql.NullString{String: updatedTemplateDataJsonString, Valid: updatedTemplateDataJsonString != "" || (refreshedChatInfo.TemplateData.Valid && refreshedChatInfo.TemplateData.String != "")}
					chatInfo.CurrentRelationship = sql.NullString{String: newCurrentRelJson, Valid: true}
					chatInfo.IntimacyLevel = newIntimacyLevel

					// 删除Chat的缓存
					v, err := l.svcCtx.Model.ExtChatsModel.DelCache(processingCtx, chatId)
					if err != nil {
						l.Errorf("ChatID %d: 删除缓存失败: %v", chatId, err)
					} else {
						l.Infof("ChatID %d: 删除缓存成功, 影响行数: %d", chatId, v)
					}
					// PromptVersion is updated in DB via sqlbuilder.Raw, no direct field in chatInfo struct to update here for it.
					// send user Event to frontend
					l.Logger.Infof("发送前端事件 StreamChunkEventRelationChanged : Chat ID: %d, newIntimacyLevel: %d, curToNextStage: %s", chatId, newIntimacyLevel, curToNextStage)
					userPrompt := types.UserPrompt{
						IntimacyLevel: int(newIntimacyLevel),
						UserPrompt:    curToNextStage,
					}
					userPromptJson, err := json.Marshal(userPrompt)
					if err != nil {
						l.Logger.Errorf("Failed to marshal 亲密度变化: %v", err)
					} else {
						l.Logger.Infof("Sent chunk: %s", string(userPromptJson))
					}
					eventInfo := &types.EventInfo{Type: types.StreamChunkEventRelationChanged, Content: string(userPromptJson)}
					events = append(events, eventInfo)
				}
				break // Found and processed new relationship
			}
		}

		if !foundNewRelationship {
			l.Logger.Infof("对话ID: %d 未找到匹配的新关系场景. 尝试仅更新亲密度.", chatId)
			eventInfo, err := l._updateIntimacyLevelOnly(processingCtx, chatId, newIntimacyLevel)
			if err != nil {
				return
			}
			if eventInfo != nil {
				events = append(events, eventInfo)
			}
		}
	}

	// 开始第一次更新尝试，使用最初传入的 (captured) chatInfo
	updateWithRetry(processingCtx, reqCtx, chatId, newIntimacyLevel, chatInfo)
	if len(events) > 0 {
		return events, nil
	}
	return nil, nil
}

func (l *SendMessageSync) sendUserEventWhenIntimacyLevelChangedWithSubScene(newIntimacyLevel int64,
	cachedIntimacyLevel int64, chatInfo *dbChats.Chats) *types.EventInfo {
	l.Logger.Infof("对话亲密度跳跃子场景, 对话ID: %d 亲密度等级变化: %d -> %d", chatInfo.Id, cachedIntimacyLevel, newIntimacyLevel)
	eventInfo := &types.EventInfo{}
	if newIntimacyLevel < cachedIntimacyLevel {
		l.Logger.Infof("亲密度下降了, 请注意调整你的行为哦")
		userPrompt := types.UserPrompt{IntimacyLevel: int(newIntimacyLevel), UserPrompt: types.IntimacyDownPrompt}
		userPromptJson, err := json.Marshal(userPrompt)
		if err != nil {
			l.Logger.Errorf("Failed to marshal 亲密度变化: %v", err)
		} else {
			l.Logger.Infof("Sent chunk: %s", string(userPromptJson))
		}
		eventInfo = &types.EventInfo{Type: types.StreamChunkEventPromptInfo, Content: string(userPromptJson)}
	} else if newIntimacyLevel > cachedIntimacyLevel {
		l.Logger.Infof("可以尝试做一些亲密的动作, 加油哦")
		userPrompt := types.UserPrompt{IntimacyLevel: int(newIntimacyLevel), UserPrompt: types.IntimacyUpPrompt1}
		userPromptJson, err := json.Marshal(userPrompt)
		if err != nil {
			l.Logger.Errorf("Failed to marshal 亲密度变化: %v", err)
		} else {
			l.Logger.Infof("Sent chunk: %s", string(userPromptJson))
		}
		eventInfo = &types.EventInfo{Type: types.StreamChunkEventPromptInfo, Content: string(userPromptJson)}
	}
	return eventInfo
}

// _updateIntimacyLevelOnly handles updating only the intimacy level.
func (l *SendMessageSync) _updateIntimacyLevelOnly(processingCtx context.Context, chatId int64, newIntimacyLevel int64) (*types.EventInfo, error) {
	l.Logger.Infof("对话ID: %d 仅更新亲密度等级为 %d.", chatId, newIntimacyLevel)
	updateErr := l.svcCtx.Model.ExtChatsModel.UpdateIntimacyLevelCache(processingCtx, chatId, newIntimacyLevel)
	eventInfo := &types.EventInfo{}
	if updateErr != nil {
		l.Logger.Errorf("Failed to update 亲密度等级缓存: 对话ID: %d: %v", chatId, updateErr)
		return nil, updateErr
	}
	eventInfo.Type = types.StreamChunkEventIntimacyChanged
	userPromptJson, err := json.Marshal(types.UserPrompt{IntimacyLevel: int(newIntimacyLevel), UserPrompt: types.IntimacyChangedNoPrompt})
	if err != nil {
		l.Logger.Errorf("Failed to marshal 亲密度变化: %v", err)
	} else {
		l.Logger.Infof("Sent chunk: %s", string(userPromptJson))
	}
	eventInfo.Content = string(userPromptJson)
	return eventInfo, nil
}

// 处理配图 (签名增加 userId)
func (l *SendMessageSync) handleImageGeneration(processingCtx, reqCtx context.Context, chatId int64, userId int64, intimacyLevel int64, imgGenStr string,
	userActivePlanID string, chatInfo *dbChats.Chats, authService *service.SubscriptionAuthService) (*types.EventInfo, error) {
	l.Logger.Infof("处理图片请求: 对话ID: %d, 亲密度: %d,  imgGenStr: %s", chatId, intimacyLevel, imgGenStr)

	var eventInfo *types.EventInfo

	// a & b. 解析图片列表和已发送图片列表
	imgListJson := chatInfo.ImgList.String
	if imgListJson == "" {
		l.Logger.Infof("对话ID: %d 没有图片列表定义.", chatId)
		return nil, nil
	}

	// 解析图片列表
	var sceneImages []types.SceneImage
	err := json.Unmarshal([]byte(imgListJson), &sceneImages)
	if err != nil {
		l.Logger.Errorf("Failed to unmarshal 图片列表 JSON: 对话ID: %d: %v", chatId, err)
		return nil, err
	}
	l.Logger.Infof("图片列表: %v", sceneImages)

	// 解析已发送图片列表
	imgSendListJson := chatInfo.ImgSendList.String
	var imgSendList []types.SceneImageSendList // 初始化空切片
	sentImageSet := make(map[string]bool)      // 使用集合进行高效查找
	if imgSendListJson != "" {
		err := json.Unmarshal([]byte(imgSendListJson), &imgSendList)
		if err != nil {
			l.Logger.Errorf("Failed to unmarshal 已发送图片列表 JSON: 对话ID: %d: %v", chatId, err)
		} else {
			// 填充已发送图片集合
			for _, sentImg := range imgSendList {
				sentImageSet[sentImg.Name] = true
			}
		}
	}
	l.Logger.Infof("已发送图片列表: %v", imgSendList)

	// c. 找到合适的、未发送的图片并发送URL
	imageSent := false // 标记确保我们只发送一张图片请求
	for _, sceneImage := range sceneImages {
		l.Logger.Infof("图片: %v", sceneImage)
		if sceneImage.IntimacySubMin <= intimacyLevel && sceneImage.IntimacySubMax >= intimacyLevel && imgGenStr == sceneImage.Name {
			l.Logger.Infof("对话ID: %d 图片 '%s' 匹配亲密度区间和名称.", chatId, sceneImage.Name)

			// 检查是否已发送
			if _, alreadySent := sentImageSet[sceneImage.Name]; !alreadySent {
				l.Logger.Infof("图片 '%s' 未发送: 对话ID: %d. 发送 URL.", sceneImage.Name, chatId)
				imgUrl := sceneImage.URL
				if imgUrl == "" {
					l.Logger.Errorf("图片 '%s' 对话ID: %d 没有 URL.", sceneImage.Name, chatId)
					continue // 跳过没有URL的图片
				}

				// 检查用户plan，图片发送量
				// -- 4. 用量检查 (检查 非VIP用户 每日消息数) --
				featureKey := types.FeatureImageGenerationLimitPerDay
				err, reachedLimit := authService.CheckUsageLimitForSyncMessage(userId, chatInfo.CharacterId, userActivePlanID, featureKey)
				if err != nil {
					l.Logger.Errorf("用量检查失败: %v", err)
					return nil, err
				}
				var intimacyImageContent types.IntimacyImageContent
				if reachedLimit && userActivePlanID == types.PlanIDFree {
					chatImageLockURL := l.svcCtx.Config.ImageBucketPrefix + types.LockImagePath
					l.Logger.Infof("Chat ID: %d, Lock Image URL: %s", chatId, chatImageLockURL)
					intimacyImageContent = types.IntimacyImageContent{Content: chatImageLockURL, NeedUnlock: types.ChatMessageNeedUnlock}
					intimacyImageContentJson, err := json.Marshal(intimacyImageContent)
					if err != nil {
						l.Logger.Errorf("Failed to marshal 锁定的图片URL: 对话ID: %d: %v", chatId, err)
						return nil, err
					}
					eventInfo = &types.EventInfo{
						Type:    types.StreamChunkEventIntimacyImage,
						Content: string(intimacyImageContentJson),
					}
					// 免费用户达到上限， 发送 带锁的图片 URL
				} else if reachedLimit && userActivePlanID != types.PlanIDFree {
					// 通过 VIP 达到发送上限, 直接返回，不再发送图片URL
					return nil, nil
				} else if !reachedLimit {
					intimacyImageContent = types.IntimacyImageContent{Content: imgUrl, NeedUnlock: types.ChatMessageNoUnlock}
					intimacyImageContentJson, err := json.Marshal(intimacyImageContent)
					if err != nil {
						l.Logger.Errorf("Failed to marshal 解锁的图片URL: 对话ID: %d: %v", chatId, err)
						return nil, err
					}
					eventInfo = &types.EventInfo{
						Type:    types.StreamChunkEventIntimacyImage,
						Content: string(intimacyImageContentJson),
					}
					// 没有达到上限， 发送 URL
				}

				// 保存助手消息 (使用公共方法)
				l.queueSaveChatMessageTask(chatId, userId, chatInfo.CharacterId, types.ChatMessageRoleAssistant, intimacyImageContent.Content, chatInfo.InteractionCount,
					chatInfo.SceneId, types.ChatMessageTypeImage, intimacyImageContent.NeedUnlock, imgUrl)

				// d. 更新已发送列表
				imgSendList = append(imgSendList, types.SceneImageSendList{Name: sceneImage.Name, URL: imgUrl})
				newImgSendListBytes, marshalErr := json.Marshal(imgSendList)
				if marshalErr != nil {
					l.Logger.Errorf("Failed to marshal 更新后的已发送图片列表 JSON: 对话ID: %d: %v", chatId, marshalErr)
					// 继续不更新DB, 记录并标记图片为已发送
					sentImageSet[sceneImage.Name] = true // 本地标记为已发送以避免在此循环中重新发送
				} else {
					newImgSendListJsonStr := string(newImgSendListBytes)
					updateImgSendListErr := l.svcCtx.Model.ChatsModel.UpdateFieldsByCondition(processingCtx, nil, map[string]any{
						"img_send_list": newImgSendListJsonStr,
					}, condition.Condition{Field: "id", Operator: condition.Equal, Value: chatId})
					if updateImgSendListErr != nil {
						l.Logger.Errorf("Failed to update 已发送图片列表 in DB: 对话ID: %d: %v", chatId, updateImgSendListErr)
						// 本地标记为已发送
						sentImageSet[sceneImage.Name] = true
					} else {
						l.Logger.Infof("成功更新 已发送图片列表: 对话ID: %d.", chatId)
						sentImageSet[sceneImage.Name] = true // 确认已发送状态
					}
				}
				imageSent = true
				break // 发送完一张图片后停止
			} else {
				l.Logger.Infof("图片 '%s' 已发送: 对话ID: %d.", sceneImage.Name, chatId)
			}
		}
	}

	if !imageSent {
		l.Logger.Infof("没有合适的未发送图片: 对话ID: %d, 名称: %s, 亲密度: %d", chatId, imgGenStr, intimacyLevel)
	}
	if eventInfo != nil {
		return eventInfo, nil
	}
	return nil, nil
}

// getChatSceneRelationshipIntimacyMax 获取对话的场景关系亲密度最大值
func (l *SendMessageSync) getChatSceneRelationshipIntimacyMax(processingCtx context.Context, chatInfo *dbChats.Chats) int64 {
	var sceneRelationshipList []types.SceneRelationship
	err := json.Unmarshal([]byte(chatInfo.SceneRelationship.String), &sceneRelationshipList)
	if err != nil {
		l.Logger.Errorf("Failed to unmarshal 场景关系列表 JSON: 对话ID: %d: %v", chatInfo.Id, err)
		return 0
	}

	// 遍历场景关系列表，找到亲密度最大值
	intimacyMax := int64(0)
	for _, sceneRelationship := range sceneRelationshipList {
		if sceneRelationship.IntimacySubMax > intimacyMax {
			intimacyMax = sceneRelationship.IntimacySubMax
		}
	}
	return intimacyMax
}

// handleSceneRelationshipUpgrade 处理场景关系升级
func (l *SendMessageSync) handleSceneRelationshipUpgrade(processingCtx, reqCtx context.Context, chatId int64, chatInfo *dbChats.Chats,
	newIntimacyLevel int64) (*types.EventInfo, error) {
	l.Logger.Infof("处理场景关系升级: 对话ID: %d, 亲密度等级: %d, chatInfo: %+v", chatId, newIntimacyLevel, chatInfo)

	eventInfo := &types.EventInfo{}

	// 当前场景是否是最后一个场景，如果不是最后一个场景则发送消息给前端提示升级场景
	chatCurrentSceneId := chatInfo.SceneId
	// 获取当前场景
	currentScene, err := l.svcCtx.Model.ScenesModel.FindOneWithCache(processingCtx, nil, chatCurrentSceneId)
	if err != nil {
		l.Logger.Errorf("Failed to find 当前场景: 对话ID: %d: %v", chatId, err)
		return nil, err
	}
	var nextSceneId = currentScene.NextSceneId.Int64
	// 判断当前场景是否是最后一个场景
	if nextSceneId == 0 {
		l.Logger.Infof("当前场景ID: %d 没有下一个场景ID", chatCurrentSceneId)
		return nil, err
	}

	// 解析可用场景列表
	var availableSceneIds []int64
	err = json.Unmarshal([]byte(chatInfo.AvailableSceneIds), &availableSceneIds)
	if err != nil {
		logx.Errorf("解析对话可用场景ID失败 (对话ID: %d): %v", chatInfo.Id, err)
		return nil, err
	}

	//检查对话可用场景列表中是否包含下一个场景ID
	if !slices.Contains(availableSceneIds, nextSceneId) {
		l.Logger.Infof("对话ID: %d 已经存在下一个可更新场景ID: %d", chatId, nextSceneId)
		newAvailableSceneIds := append(availableSceneIds, nextSceneId)
		newAvailableScenesJsonBytes, err := json.Marshal(newAvailableSceneIds)
		if err != nil {
			l.Errorf("Failed to marshal available scenes: %v", err)
			return nil, err
		}
		newAvailableScenesJsonStr := string(newAvailableScenesJsonBytes)
		l.Logger.Infof("oldAvailableScenesJsonStr: %s, newAvailableScenesJsonStr: %s", chatInfo.AvailableSceneIds, newAvailableScenesJsonStr)

		// 更新对话的 available_scene_ids
		err = l.svcCtx.Model.ChatsModel.UpdateFieldsByCondition(processingCtx, nil, map[string]any{
			"available_scene_ids": newAvailableScenesJsonStr,
		}, condition.Condition{Field: "id", Operator: condition.Equal, Value: chatId})
		if err != nil {
			l.Logger.Errorf("Failed to update 对话的 upgrade_available_scene_id: 对话ID: %d: %v", chatId, err)
		}
		_, err = l.svcCtx.Model.ExtChatsModel.DelCache(processingCtx, chatId)
		if err != nil {
			l.Logger.Errorf("Failed to delete cache for chat %d after updating upgrade_available_scene_id: %v", chatId, err)
		}
		chatInfo.AvailableSceneIds = newAvailableScenesJsonStr

		// 发送消息给前端提示升级场景
		l.Logger.Infof("发送前端事件 StreamChunkEventSceneUpgrade : Chat ID: %d, newIntimacyLevel: %d", chatId, newIntimacyLevel)
		userPromptJson, err := json.Marshal(types.UserPrompt{IntimacyLevel: int(newIntimacyLevel), UserPrompt: types.IntimacyMaxPrompt})
		if err != nil {
			l.Logger.Errorf("Failed to marshal 亲密度变化: %v", err)
		} else {
			l.Logger.Infof("Sent chunk: %s", string(userPromptJson))
		}
		eventInfo = &types.EventInfo{Type: types.StreamChunkEventSceneUpgrade, Content: string(userPromptJson)}
		return eventInfo, nil
	}

	l.Logger.Infof("处理场景关系升级: 对话ID: %d, 下一个场景ID: %d, 可用场景列表: %v", chatId, nextSceneId, availableSceneIds)
	return nil, nil
}
