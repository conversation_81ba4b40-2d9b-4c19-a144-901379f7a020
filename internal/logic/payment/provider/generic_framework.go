package provider

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"xai.backend/internal/config"
	"xai.backend/internal/logic/payment" // Import the parent payment package
	models "xai.backend/internal/model/12_subscription"
	"xai.backend/internal/svc"
	"xai.backend/internal/types"
	"xai.backend/internal/util/signutil"

	"github.com/huandu/go-sqlbuilder"
	"github.com/jzero-io/jzero-contrib/condition"
	"github.com/zeromicro/go-zero/core/logx"
)

// TempRetInfo Temporary RetInfo structure if types.BaseResponseData is not defined as expected
type TempRetInfo struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Desc string `json:"desc,omitempty"`
}

// BuyItemRequest Structures for Generic Framework BuyItem API
type BuyItemRequest struct {
	AppID           string `json:"appid"`
	Nonce           string `json:"nonce"`
	T               string `json:"t"`
	Sign            string `json:"sign"`
	OpenAppOrderID  string `json:"open_app_order_id"`
	BuyerUserID     string `json:"buyer_user_id"`
	RecipientUserID string `json:"recipient_user_id"`
	ItemType        int    `json:"item_type"`
	ItemID          string `json:"item_id"`
	ItemQuantity    int    `json:"item_quantity"`
	Memo            string `json:"memo,omitempty"`
	PaymentType     int    `json:"payment_type"`
	PaymentChannel  string `json:"payment_channel"`
	PaymentAmount   string `json:"payment_amount"`
}

type BuyItemData struct {
	PayURL          string `json:"pay_url"`
	PlatformOrderID string `json:"platform_order_id"`
	TransactionID   string `json:"transaction_id"`
}

type BuyItemReply struct {
	Ret  TempRetInfo `json:"ret"` // Changed from types.BaseResponseData to TempRetInfo
	Data BuyItemData `json:"data,omitempty"`
}

type GenericFrameworkWebhookPayload struct {
	AppID           string `json:"appid"`
	Nonce           string `json:"nonce"`
	Timestamp       string `json:"t"`
	Sign            string `json:"sign"`
	OpenAppOrderID  string `json:"open_app_order_id"`
	PlatformOrderID string `json:"platform_order_id"`
	PaymentStatus   string `json:"payment_status"`
	FailReason      string `json:"fail_reason"`
	PaymentAmount   string `json:"payment_amount"`
	TransactionID   string `json:"transaction_id,omitempty"`
}

type GenericFrameworkPaymentProvider struct {
	Config     config.Config
	HTTPClient *http.Client
}

func NewGenericFrameworkPaymentProvider(cfg config.Config) payment.PaymentProvider {
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	logx.Infof("NewGenericFrameworkPaymentProvider: Initializing with HTTPClient: %v (is nil: %v)", client, client == nil)

	return &GenericFrameworkPaymentProvider{
		Config:     cfg,
		HTTPClient: client,
	}
}

func (p *GenericFrameworkPaymentProvider) Name() string {
	return types.PaymentProviderZhongxin
}

func (p *GenericFrameworkPaymentProvider) InitiatePayment(
	ctx context.Context,
	svcCtx *svc.ServiceContext,
	userID int64,
	priceDetails *models.PlanProviderDetails,
	openAppOrderID string,
) (*types.ProviderInitiationResult, error) {
	logger := logx.WithContext(ctx)

	if p.HTTPClient == nil {
		logger.Errorf("CRITICAL: GenericFrameworkPaymentProvider.HTTPClient is nil at the beginning of InitiatePayment!")
		return nil, fmt.Errorf("内部服务器错误: 支付提供商 HTTP客户端未初始化")
	}

	apiURL := p.Config.XGroupPaymentGateway.BuyItemURL
	appID := p.Config.XGroupPaymentGateway.AppId
	appKey := p.Config.XGroupPaymentGateway.AppKey

	if apiURL == "" || appID == "" || appKey == "" {
		return nil, fmt.Errorf("GenericFrameworkPaymentProvider: 缺少关键配置 (URL, AppId, 或 AppKey)")
	}

	timestampStr := signutil.GenerateTimestamp()
	nonce := signutil.GenerateNonce(30)
	buyerUserIDStr := strconv.FormatInt(userID, 10)

	itemTypeForSubscription := types.ItemTypeSubscription // 物品类型 1:代币，2:物品，3：订阅
	defaultPaymentType := types.PaymentTypeZhongxin       //   支付方式 1:众信，2:微信，3:支付宝

	defaultPaymentChannel := types.PaymentChannelDefault
	memo := fmt.Sprintf("用户%d购买%s", userID, priceDetails.PlanId)

	requestPayload := BuyItemRequest{
		AppID:           appID,
		Nonce:           nonce,
		T:               timestampStr,
		OpenAppOrderID:  openAppOrderID,
		BuyerUserID:     buyerUserIDStr,
		RecipientUserID: buyerUserIDStr,
		ItemType:        itemTypeForSubscription,
		ItemID:          priceDetails.PlanId,
		ItemQuantity:    1,
		PaymentType:     defaultPaymentType,
		PaymentChannel:  defaultPaymentChannel,
		PaymentAmount:   fmt.Sprintf("%.8f", priceDetails.Price),
		Memo:            memo,
	}

	paramsForSign := map[string]string{
		"appid":             requestPayload.AppID,
		"nonce":             requestPayload.Nonce,
		"t":                 requestPayload.T,
		"open_app_order_id": requestPayload.OpenAppOrderID,
		"buyer_user_id":     requestPayload.BuyerUserID,
		"recipient_user_id": requestPayload.RecipientUserID,
		"item_type":         strconv.Itoa(requestPayload.ItemType),
		"item_id":           requestPayload.ItemID,
		"item_quantity":     strconv.Itoa(requestPayload.ItemQuantity),
		"payment_type":      strconv.Itoa(requestPayload.PaymentType),
		"payment_channel":   requestPayload.PaymentChannel,
		"payment_amount":    requestPayload.PaymentAmount,
		"memo":              requestPayload.Memo,
	}

	signature := signutil.AppSign(appID, appKey, paramsForSign)
	requestPayload.Sign = signature

	logx.Infof("requestPayload: %+v", requestPayload)

	payloadBytes, err := json.Marshal(requestPayload)
	if err != nil {
		logger.Errorf("Failed to marshal BuyItemRequest: %v", err)
		return nil, fmt.Errorf("构建支付请求体失败: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", apiURL, bytes.NewBuffer(payloadBytes))
	if err != nil {
		logger.Errorf("Failed to create BuyItem HTTP request: %v", err)
		return nil, fmt.Errorf("构建支付 HTTP 请求失败: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	if p.HTTPClient == nil {
		logger.Errorf("CRITICAL: GenericFrameworkPaymentProvider.HTTPClient became nil before calling Do in InitiatePayment!")
		return nil, fmt.Errorf("内部服务器错误: 支付提供商 HTTP客户端在调用前变为空")
	}
	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		logger.Errorf("Failed to call GenericFramework BuyItem API: %v", err)
		return nil, fmt.Errorf("支付提供商通信错误: %w", err)
	}
	defer resp.Body.Close()

	respBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Errorf("Failed to read BuyItem API response body: %v", err)
		return nil, fmt.Errorf("读取支付提供商响应失败: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		logger.Errorf("GenericFramework BuyItem API 返回非 OK 状态 %d. Body: %s", resp.StatusCode, string(respBodyBytes))
		return nil, fmt.Errorf("支付提供商返回错误 (状态 %d)", resp.StatusCode)
	}

	var buyItemReply BuyItemReply
	if err := json.Unmarshal(respBodyBytes, &buyItemReply); err != nil {
		logger.Errorf("Failed to unmarshal BuyItem API response. Body: %s, Error: %v", string(respBodyBytes), err)
		return nil, fmt.Errorf("解析支付提供商响应失败: %w", err)
	}

	if buyItemReply.Ret.Code != 0 {
		logger.Errorf("GenericFramework BuyItem API 业务错误. Code: %d, Msg: %s, Desc: %s", buyItemReply.Ret.Code, buyItemReply.Ret.Msg, buyItemReply.Ret.Desc)
		return nil, fmt.Errorf("支付提供商业务错误: %s (代码 %d)", buyItemReply.Ret.Msg, buyItemReply.Ret.Code)
	}

	if buyItemReply.Data.PayURL == "" {
		logger.Errorf("GenericFramework BuyItem API 未返回 pay_url. 响应: %+v", buyItemReply)
		return nil, fmt.Errorf("支付提供商未返回支付 URL")
	}

	logger.Infof("GenericFramework InitiatePayment 成功准备为 OpenAppOrderID %s 返回结果. PayURL: %s, PlatformOrderID: %s", openAppOrderID, buyItemReply.Data.PayURL, buyItemReply.Data.PlatformOrderID)

	return &types.ProviderInitiationResult{
		PayURL:          buyItemReply.Data.PayURL,
		PlatformOrderId: buyItemReply.Data.PlatformOrderID,
		TransactionID:   buyItemReply.Data.TransactionID,
		OpenAppOrderId:  openAppOrderID,
	}, nil
}

func (p *GenericFrameworkPaymentProvider) HandleWebhook(
	ctx context.Context,
	svcCtx *svc.ServiceContext,
	req *types.WebHookReq,
) error {
	logger := logx.WithContext(ctx)
	logger.Infof("接受来自 GenericFramework 的 webhook. Body: %v", req)

	// Implement actual signature validation based on GenericFramework's docs.
	appID := p.Config.XGroupPaymentGateway.AppId
	appKey := p.Config.XGroupPaymentGateway.AppKey
	paramsToVerifySign := map[string]string{
		"appid":             req.AppID,
		"nonce":             req.Nonce,
		"t":                 req.Timestamp,
		"open_app_order_id": req.OpenAppOrderID,
		"platform_order_id": req.PlatformOrderID,
		"payment_status":    req.PaymentStatus,
		"fail_reason":       req.FailReason,
		"payment_amount":    req.PaymentAmount,
		"transaction_id":    req.TransactionID,
		"finish_time":       req.FinishTime,
	}
	expectedSign := signutil.AppSign(appID, appKey, paramsToVerifySign)
	if expectedSign != req.Sign {
		logger.Errorf("Webhook 签名验证失败. 预期: %s, 实际: %s", expectedSign, req.Sign)
		return fmt.Errorf("webhook 签名验证失败")
	}
	logger.Info("Webhook 签名验证成功.")

	openAppOrderID := req.OpenAppOrderID
	if openAppOrderID == "" {
		logger.Errorf("Webhook 负载缺少 OpenAppOrderID. 无法处理. Payload: %+v", req)
		return fmt.Errorf("webhook 缺少订单标识符")
	}

	if svcCtx.Model.PaymentTransactionsModel == nil {
		logger.Errorf("svcCtx.PaymentTransactionsModel 为 nil, 无法找到交易 %s", openAppOrderID)
		return fmt.Errorf("payment transaction model not initialized")
	}

	transaction, err := svcCtx.Model.PaymentTransactionsModel.FindOneByCondition(ctx, nil,
		condition.Condition{Field: "open_app_order_id", Operator: condition.Equal, Value: openAppOrderID},
	)
	if err != nil {
		if err == models.ErrNotFound {
			logger.Errorf("Payment transaction with openAppOrderID '%s' (from webhook OpenAppOrderID) not found.", openAppOrderID)
			return fmt.Errorf("交易未找到: %s", openAppOrderID)
		}
		logger.Errorf("Failed to retrieve payment transaction '%s': %v", openAppOrderID, err)
		return fmt.Errorf("db 错误检索交易: %w", err)
	}

	if transaction.Status == "succeeded" || transaction.Status == "failed" {
		logger.Infof("交易 %s 已处于终端状态 %s. 再次收到 webhook 事件 %s.", openAppOrderID, transaction.Status, req.PaymentStatus)
		if req.PaymentStatus == strconv.Itoa(types.PaymentStatusSuccess) {
			return nil
		}
	}

	var newStatus string
	var errorMessage sql.NullString

	switch strings.ToLower(req.PaymentStatus) {
	case strconv.Itoa(types.PaymentStatusSuccess):
		newStatus = "succeeded"
	case strconv.Itoa(types.PaymentStatusFailed):
		newStatus = "failed"
		errorMessage = sql.NullString{String: req.FailReason, Valid: req.FailReason != ""}
	default:
		newStatus = "unknown"
		logger.Infof("Warning: Received unhandled webhook status/event type '%s' for transaction %s. Payload: %+v", req.PaymentStatus, openAppOrderID, req)
		// return fmt.Errorf("未处理的 webhook 状态: %s", req.PaymentStatus)
	}

	providerSpecificTxID := sql.NullString{String: req.TransactionID, Valid: req.TransactionID != ""}
	processedAt := time.Now()

	if svcCtx.Model.PaymentTransactionsModel == nil {
		logger.Errorf("svcCtx.PaymentTransactionsModel 为 nil, 无法更新交易 %s", openAppOrderID)
		return fmt.Errorf("在更新之前 payment transaction model 未初始化")
	}
	err = svcCtx.Model.PaymentTransactionsModel.UpdateFieldsByCondition(ctx, nil, map[string]interface{}{
		"status":                  newStatus,
		"provider_transaction_id": providerSpecificTxID,
		"error_message":           errorMessage,
		"processed_at":            sql.NullTime{Time: processedAt, Valid: true},
		"payment_method_details":  nil,
	},
		condition.Condition{Field: "open_app_order_id", Operator: condition.Equal, Value: openAppOrderID},
	)

	if err != nil {
		logger.Errorf("Failed to update payment_transaction %s status to %s: %v", openAppOrderID, newStatus, err)
		return fmt.Errorf("db 错误更新交易状态: %w", err)
	}
	logger.Infof("Payment transaction %s 状态已更新为 %s.", openAppOrderID, newStatus)

	if newStatus == "succeeded" {
		if transaction.ItemType == "subscription" {
			if svcCtx.Model.PlanProviderDetailsModel == nil || svcCtx.Model.SubscriptionsModel == nil {
				logger.Errorf("[Critical] 未初始化用于订阅处理的模型 for tx %s", openAppOrderID)
				return fmt.Errorf("未初始化用于订阅处理的模型")
			}
			err := p.activateOrUpdateSubscription(ctx, svcCtx, transaction, req)
			if err != nil {
				logger.Errorf("[Critical] 支付成功 for tx %s, 但无法激活/更新订阅: %v", openAppOrderID, err)
				return fmt.Errorf("订阅更新失败后支付: %w", err)
			}
			logger.Infof("Subscription successfully processed for transaction %s.", openAppOrderID)
			err = p.UnlockAllChatMessages(ctx, svcCtx, transaction.UserId)
			if err != nil {
				logger.Errorf("Failed to unlock all chat messages for user %d: %v", transaction.UserId, err)
				return fmt.Errorf("解锁消息内容失败: %w", err)
			}
			logger.Infof("All chat messages unlocked for user %d.", transaction.UserId)
		} else {
			logger.Infof("Payment succeeded for non-subscription item type %s for tx %s. No subscription action taken.", transaction.ItemType, openAppOrderID)
		}
	}
	return nil
}

func (p *GenericFrameworkPaymentProvider) activateOrUpdateSubscription(
	ctx context.Context,
	svcCtx *svc.ServiceContext,
	tx *models.PaymentTransactions,
	webhookPayload *types.WebHookReq,
) error {
	logger := logx.WithContext(ctx)
	userID := tx.UserId
	planID := tx.ItemId

	if svcCtx.Model.PlanProviderDetailsModel == nil || svcCtx.Model.SubscriptionsModel == nil {
		logger.Errorf("未初始化 in activateOrUpdateSubscription for user %d, plan %s", userID, planID)
		return fmt.Errorf("未初始化 in subscription activation")
	}

	planProviderDetails, err := svcCtx.Model.PlanProviderDetailsModel.FindOneByCondition(ctx, nil,
		condition.Condition{
			Field:    "plan_id",
			Operator: condition.Equal,
			Value:    planID,
		},
		condition.Condition{
			Field:    "provider",
			Operator: condition.Equal,
			Value:    p.Name(),
		},
	)
	if err != nil {
		logger.Errorf("Failed to find PlanProviderDetails for plan_id %s and provider %s: %v", planID, p.Name(), err)
		return fmt.Errorf("无法找到 plan provider details for plan %s: %w", planID, err)
	}
	if planProviderDetails == nil {
		return fmt.Errorf("no plan provider details found for plan %s, provider %s", planID, p.Name())
	}

	existingSub, err := svcCtx.Model.SubscriptionsModel.FindOneByUserId(ctx, nil, userID)
	if err != nil && !errors.Is(err, models.ErrNotFound) {
		logger.Errorf("Error fetching existing subscription for user %d: %v", userID, err)
		return fmt.Errorf("db error fetching subscription: %w", err)
	}

	now := time.Now()
	var newPeriodEnd time.Time
	switch planProviderDetails.Interval {
	case "day":
		newPeriodEnd = now.AddDate(0, 0, int(planProviderDetails.IntervalCount))
	case "week":
		newPeriodEnd = now.AddDate(0, 0, 7*int(planProviderDetails.IntervalCount))
	case "month":
		newPeriodEnd = now.AddDate(0, int(planProviderDetails.IntervalCount), 0)
	case "season":
		newPeriodEnd = now.AddDate(0, 3*int(planProviderDetails.IntervalCount), 0)
	case "year":
		newPeriodEnd = now.AddDate(int(planProviderDetails.IntervalCount), 0, 0)
	case "one_time":
		logger.Infof("Processing one-time purchase for plan %s, user %d. No standard subscription period extension.", planID, userID)
		return nil
	default:
		return fmt.Errorf("unknown plan interval: %s", planProviderDetails.Interval)
	}

	if existingSub == nil || existingSub.Status == "canceled" || existingSub.Status == "expired" || (existingSub.Status == "active" && existingSub.CurrentPeriodEnd.Valid && existingSub.CurrentPeriodEnd.Time.Before(now)) {
		newSub := models.Subscriptions{
			UserId:                  userID,
			PlanId:                  planID,
			Status:                  "active",
			PaymentProvider:         p.Name(),
			ProviderSubscriptionRef: sql.NullString{String: webhookPayload.PlatformOrderID, Valid: webhookPayload.PlatformOrderID != ""},
			ProviderCustomerRef:     sql.NullString{},
			CurrentPeriodStart:      sql.NullTime{Time: now, Valid: true},
			CurrentPeriodEnd:        sql.NullTime{Time: newPeriodEnd, Valid: true},
			CancelAtPeriodEnd:       false,
		}
		_, err = svcCtx.Model.SubscriptionsModel.InsertWithCache(ctx, nil, &newSub)
		if err != nil {
			logger.Errorf("Failed to insert new subscription for user %d, plan %s: %v", userID, planID, err)
			return fmt.Errorf("db error creating subscription: %w", err)
		}
		logger.Infof("New subscription created for user %d, plan %s. Ends at %v", userID, planID, newPeriodEnd)
	} else {
		if existingSub.PlanId == planID && existingSub.Status == "active" {
			existingSub.CurrentPeriodStart = sql.NullTime{Time: now, Valid: true}
			existingSub.CurrentPeriodEnd = sql.NullTime{Time: newPeriodEnd, Valid: true}
			existingSub.Status = "active"
			existingSub.PaymentProvider = p.Name()
			existingSub.ProviderSubscriptionRef = sql.NullString{String: webhookPayload.PlatformOrderID, Valid: webhookPayload.PlatformOrderID != ""}
			existingSub.CancelAtPeriodEnd = false
			existingSub.CanceledAt = sql.NullTime{Valid: false}
			existingSub.EndedAt = sql.NullTime{Valid: false}

			err = svcCtx.Model.SubscriptionsModel.UpdateWithCache(ctx, nil, existingSub)
			if err != nil {
				logger.Errorf("Failed to update existing subscription %d for user %d, plan %s: %v", existingSub.Id, userID, planID, err)
				return fmt.Errorf("db error updating subscription: %w", err)
			}
			logger.Infof("Subscription %d renewed for user %d, plan %s. New end date: %v", existingSub.Id, userID, planID, newPeriodEnd)
		} else if existingSub.PlanId != planID && existingSub.Status == "active" {
			logger.Infof("Plan change detected for user %d: from %s to %s. Current end: %v. New end: %v",
				userID, existingSub.PlanId, planID, existingSub.CurrentPeriodEnd.Time, newPeriodEnd)
			existingSub.PlanId = planID
			existingSub.CurrentPeriodStart = sql.NullTime{Time: now, Valid: true}
			existingSub.CurrentPeriodEnd = sql.NullTime{Time: newPeriodEnd, Valid: true}
			existingSub.Status = "active"
			existingSub.PaymentProvider = p.Name()
			existingSub.ProviderSubscriptionRef = sql.NullString{String: webhookPayload.PlatformOrderID, Valid: webhookPayload.PlatformOrderID != ""}
			existingSub.CancelAtPeriodEnd = false
			existingSub.CanceledAt = sql.NullTime{Valid: false}
			existingSub.EndedAt = sql.NullTime{Valid: false}

			err = svcCtx.Model.SubscriptionsModel.UpdateWithCache(ctx, nil, existingSub)
			if err != nil {
				logger.Errorf("Failed to update subscription %d for plan change (user %d, new plan %s): %v", existingSub.Id, userID, planID, err)
				return fmt.Errorf("db error updating subscription for plan change: %w", err)
			}
			logger.Infof("Subscription %d successfully changed to plan %s for user %d.", existingSub.Id, planID, userID)

		} else {
			logger.Infof("Warning: Unhandled existing subscription state for user %d: status %s, plan %s. Payment for plan %s received.",
				userID, existingSub.Status, existingSub.PlanId, planID)
			existingSub.PlanId = planID
			existingSub.CurrentPeriodStart = sql.NullTime{Time: now, Valid: true}
			existingSub.CurrentPeriodEnd = sql.NullTime{Time: newPeriodEnd, Valid: true}
			existingSub.Status = "active"
			existingSub.PaymentProvider = p.Name()
			existingSub.ProviderSubscriptionRef = sql.NullString{String: webhookPayload.PlatformOrderID, Valid: webhookPayload.PlatformOrderID != ""}
			err = svcCtx.Model.SubscriptionsModel.UpdateWithCache(ctx, nil, existingSub)
			if err != nil {
				logger.Errorf("Failed to update subscription %d for user %d from status %s to active plan %s: %v", existingSub.Id, userID, existingSub.Status, planID, err)
				return fmt.Errorf("db error updating subscription from %s: %w", existingSub.Status, err)
			}
			logger.Infof("Subscription %d for user %d (status %s) updated to active with plan %s. New end date: %v", existingSub.Id, userID, existingSub.Status, planID, newPeriodEnd)
		}
	}
	return nil
}

func (p *GenericFrameworkPaymentProvider) UnlockAllChatMessages(
	ctx context.Context,
	svcCtx *svc.ServiceContext,
	userID int64,
) error {
	logger := logx.WithContext(ctx)
	logger.Infof("Unlocking all chat messages for user %d", userID)

	// 解锁所有对话中的锁定内容
	chats, err := svcCtx.Model.ChatsModel.FindByCondition(ctx, nil,
		condition.Condition{Field: "user_id", Operator: condition.Equal, Value: userID})
	if err != nil {
		logger.Errorf("解锁消息内容失败: %v", err)
		return fmt.Errorf("db error querying chats: %w", err)
	}
	for _, chat := range chats {
		updateData := map[string]any{
			"need_unlock": types.ChatMessageNoUnlock,
			"content":     sqlbuilder.Raw("unlock_content"),
		}
		err := svcCtx.Model.ChatMessagesModel.UpdateFieldsByCondition(ctx, nil, updateData,
			condition.Condition{Field: "chat_id", Operator: condition.Equal, Value: chat.Id},
			condition.Condition{Field: "need_unlock", Operator: condition.Equal, Value: types.ChatMessageNeedUnlock},
		)
		if err != nil {
			logger.Errorf("解锁消息内容失败: %v", err)
			return fmt.Errorf("db error updating chat messages: %w", err)
		}
	}
	return nil
}

func (p *GenericFrameworkPaymentProvider) CancelSubscription(
	ctx context.Context,
	svcCtx *svc.ServiceContext,
	localSubscription *models.Subscriptions,
) error {
	logger := logx.WithContext(ctx)
	logger.Infof("Attempting to cancel subscription %d via GenericFrameworkPaymentProvider (provider_ref: %s)", localSubscription.Id, localSubscription.ProviderSubscriptionRef.String)

	if svcCtx.Model.SubscriptionsModel == nil {
		logger.Errorf("svcCtx.SubscriptionsModel is nil, cannot cancel subscription %d", localSubscription.Id)
		return fmt.Errorf("subscriptions model not initialized")
	}

	if localSubscription.Status == "active" {
		localSubscription.CancelAtPeriodEnd = true
		err := svcCtx.Model.SubscriptionsModel.UpdateWithCache(ctx, nil, localSubscription)
		if err != nil {
			logger.Errorf("Failed to mark subscription %d for local cancellation: %v", localSubscription.Id, err)
			return fmt.Errorf("db error marking subscription for cancellation: %w", err)
		}
		logger.Infof("Subscription %d marked for cancellation at period end.", localSubscription.Id)
		return nil
	}
	logger.Infof("Warning: Subscription %d is not in 'active' state (current: %s), cannot mark for cancellation at period end.", localSubscription.Id, localSubscription.Status)
	return fmt.Errorf("subscription not active, cannot cancel")
}

var _ payment.PaymentProvider = (*GenericFrameworkPaymentProvider)(nil)
