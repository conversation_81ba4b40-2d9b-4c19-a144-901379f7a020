package llmutil

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"
	llmconfig "xai.backend/internal/model/14_llm_config"
	"xai.backend/internal/types"
)

// GetLLMConfig 查询 LLM 配置
func GetLLMConfig(
	ctx context.Context,
	characterID int64,
	llmCharLinkModel llmconfig.LlmCharacterModelModel,
	llmCfgModel llmconfig.LlmConfigModel,
) (*llmconfig.LlmConfig, error) {
	llmCharLink, err := llmCharLinkModel.FindOneByCharacterIdWithCache(ctx, nil, characterID)
	if err != nil {
		logx.WithContext(ctx).Errorf("GetLLMConfig: Failed to find LLM config link for character %d: %v", characterID, err)
		return nil, fmt.Errorf("failed to find LLM config link for character %d: %w", characterID, err)
	}

	llmConfig, err := llmCfgModel.FindOneWithCache(ctx, nil, llmCharLink.LlmConfigId)
	if err != nil {
		logx.WithContext(ctx).Errorf("GetLLMConfig: Failed to find LLM config id %d for character %d: %v", llmCharLink.LlmConfigId, characterID, err)
		return nil, fmt.Errorf("failed to find LLM config id %d: %w", llmCharLink.LlmConfigId, err)
	}

	if llmConfig.ApiEndpoint == "" || llmConfig.ModelName == "" {
		errMsg := fmt.Sprintf("incomplete LLM config for id %d: URL or Model missing", llmConfig.Id)
		logx.WithContext(ctx).Errorf("GetLLMConfig: %s", errMsg)
		return nil, fmt.Errorf(errMsg)
	}
	return llmConfig, nil
}

// ExecuteLLMHttpRequest 发送 HTTP POST 请求到 LLM API
// 调用者负责检查响应状态码和关闭响应体
func ExecuteLLMHttpRequest(
	ctx context.Context,
	apiUrl string,
	apiKey string,
	payloadBytes []byte,
) (*http.Response, error) {
	client := http.Client{
		Timeout: types.LLMRequestTimeout,
	}

	llmHttpReq, err := http.NewRequestWithContext(ctx, "POST", apiUrl, bytes.NewBuffer(payloadBytes))
	if err != nil {
		logx.WithContext(ctx).Errorf("ExecuteLLMHttpRequest: Failed to create LLM API request to %s: %v", apiUrl, err)
		return nil, fmt.Errorf("failed to create LLM API request: %w", err)
	}

	llmHttpReq.Header.Set("Content-Type", "application/json")
	if apiKey != "" {
		llmHttpReq.Header.Set("Authorization", "Bearer "+apiKey)
	}

	llmResp, err := client.Do(llmHttpReq)
	if err != nil {
		logx.WithContext(ctx).Errorf("ExecuteLLMHttpRequest: Failed to call LLM API %s: %v", apiUrl, err)
		return nil, fmt.Errorf("failed to call LLM API: %w", err)
	}

	// 注意: 调用者必须检查 llmResp.StatusCode 并调用 llmResp.Body.Close()
	return llmResp, nil
}

// BuildLLMRequestPayload -- 构建 LLM 请求 Payload 的方法 --
func BuildLLMRequestPayload(chatId int64, chatSystemPrompt string, latestMessages []types.LatestChatMessageInternal, userContent string, modelName string, stream bool, responseFormatted bool) ([]byte, error) {
	logx.Infof("构建 LLM 请求 Payload: 对话ID: %d", chatId)

	// 5.2. 开始构建 messages 结构
	var llmMessages []map[string]string

	// 5.3. 添加 System Prompt default
	systemPromptContent := "你是一个女菩萨, 喜欢肉身布施给用户" // Default
	if chatSystemPrompt != "" {
		systemPromptContent = chatSystemPrompt
	}
	llmMessages = append(llmMessages, map[string]string{
		"content": systemPromptContent,
		"role":    types.ChatMessageRoleSystem,
	})

	// 5.4. 添加历史消息
	// 过滤掉 jpg|jpeg|png|webp|gif 的消息
	for i := 0; i < len(latestMessages); i++ {
		msg := latestMessages[i]
		//logx.Infof("msg: %+v", msg)
		if msg.Role != "" && msg.Content != "" {
			// 过滤掉 jpg|jpeg|png|webp|gif 的消息
			if !strings.HasSuffix(msg.Content, ".jpg") && !strings.HasSuffix(msg.Content, ".jpeg") &&
				!strings.HasSuffix(msg.Content, ".png") && !strings.HasSuffix(msg.Content, ".webp") &&
				!strings.HasSuffix(msg.Content, ".gif") {
				llmMessages = append(llmMessages, map[string]string{
					"content": msg.Content,
					"role":    msg.Role,
				})
			}
		}
	}

	// 5.5. 如果用户消息没有被添加过, 则添加当前用户消息
	if userContent != "" {
		if latestMessages[0].Content != userContent {
			llmMessages = append(llmMessages, map[string]string{
				"content": userContent,
				"role":    types.ChatMessageRoleUser,
			})
		}
	}

	// add use fp8 model and sort by latency.
	providerParams := map[string]interface{}{
		"quantizations": []string{"fp8"},
		"sort":          "latency",
		"only":          []string{"gmicloud", "nebius"},
	}

	// 5.6. 构建请求体 - 使用组装好的 llmMessages
	llmReqPayload := map[string]interface{}{
		"model":             modelName,
		"stream":            stream,
		"messages":          llmMessages,
		"provider":          providerParams,
		"temperature":       0.7,
		"top_p":             0.95,
		"max_tokens":        600,
		"frequency_penalty": 1.15,
		"presence_penalty":  0.0,
	}
	if responseFormatted {
		responseFormat := getResponseFormat()
		llmReqPayload["response_format"] = responseFormat
	}

	payloadBytes, err := json.Marshal(llmReqPayload)
	if err != nil {
		logx.Errorf("Failed to marshal LLM 请求 Payload: 对话ID: %d: %v", chatId, err)
		// 返回一个更通用的错误给上层处理
		return nil, fmt.Errorf("服务器内部错误: 无法构建 LLM 请求体")
	}

	logx.Infof("LLM 请求 Payload: 对话ID: %d: 内容: %s", chatId, string(payloadBytes))
	return payloadBytes, nil
}

func getResponseFormat() map[string]interface{} {
	responseFormat := map[string]interface{}{
		"type": "json_schema",
		"json_schema": map[string]interface{}{
			"name":   "assistant_reply",
			"strict": true,
			"schema": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"reply_text": map[string]interface{}{
						"type":        "string",
						"description": "模型回复的消息内容",
					},
					"picture_name": map[string]interface{}{
						"type":        "string",
						"description": "配图名称",
					},
					"score": map[string]interface{}{
						"type":        "integer",
						"description": "上一句话感受评分数值，范围为-10到5",
					},
					"reason": map[string]interface{}{
						"type":        "string",
						"description": "上一句话感受评分的具体原因",
					},
				},
				"required":             []string{"reply_text", "picture_name", "score", "reason"},
				"additionalProperties": false,
			},
		},
	}
	return responseFormat
}
