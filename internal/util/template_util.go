package util

import (
	"bytes"
	"encoding/json"
	"fmt"
	"sync"
	"text/template"
)

// TemplateData 存储模板数据
type TemplateData struct {
	// 字段值
	CharDefine                     string `json:"char_define"`
	UserDefine                     string `json:"user_define"`
	UserCharacterChatSummary       string `json:"user_character_chat_summary"`
	RelationshipDesc               string `json:"relationship_desc"`
	RelationshipCharCurrentSetting string `json:"relationship_char_current_setting"`
	RelationshipChatSummary        string `json:"relationship_chat_summary"`
	AllowForbiddenBehavior         string `json:"allow_forbidden_behavior"`
	SceneName                      string `json:"scene_name"`
	SceneContent                   string `json:"scene_content"`
	SceneLimit                     string `json:"scene_limit"`
	SceneGuidance                  string `json:"scene_guidance"`
	SceneImgGen                    string `json:"scene_img_gen"`
	SceneDialogueExample           string `json:"scene_dialogue_example"`
}

// Global cache for parsed templates
var (
	templateCache = make(map[string]*template.Template)
	cacheLock     = sync.RWMutex{}
)

// TemplateManager 管理模板的更新
type TemplateManager struct {
	// 模板
	tpl *template.Template
	// 当前数据
	currentData TemplateData
}

// minT is a helper for logging, to avoid excessively long log lines.
func minT(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// NewTemplateManager 创建一个新的模板管理器，并使用缓存机制
func NewTemplateManager(templateContent string) (*TemplateManager, error) {
	cacheLock.RLock()
	tpl, found := templateCache[templateContent]
	cacheLock.RUnlock()

	if found {
		// Optional: Log cache hit. Consider a more structured logger if this util package has one.
		// logx.Infof("Template cache hit for template starting with: %s...", templateContent[:min_t(30, len(templateContent))])
		return &TemplateManager{
			tpl:         tpl,
			currentData: TemplateData{}, // currentData is instance-specific, initialized empty
		}, nil
	}

	// Cache miss, parse and store
	// logx.Infof("Template cache miss for template starting with: %s... Parsing and caching.", templateContent[:min_t(30, len(templateContent))])

	// The name of the template ("chat" here) should ideally be unique if multiple distinct templates
	// could potentially have the exact same starting content used for the name in template.New().
	// However, since we are caching based on the full templateContent, this specific name isn't critical for cache correctness.
	newTpl, err := template.New("chatTemplate_" + templateContent[:minT(10, len(templateContent))]).Parse(templateContent)
	if err != nil {
		return nil, fmt.Errorf("解析模板失败: %v", err)
	}

	cacheLock.Lock()
	templateCache[templateContent] = newTpl
	cacheLock.Unlock()

	return &TemplateManager{
		tpl:         newTpl,
		currentData: TemplateData{}, // Initialize with empty data for this new manager instance
	}, nil
}

// UpdateTemplate 更新模板内容
func (tm *TemplateManager) UpdateTemplate(updates map[string]interface{}) (string, error) {
	// 1. 应用更新到 currentData (newData is a copy of tm.currentData to be modified)
	newData := tm.currentData // Start with a copy of the current state for this instance

	for k, v := range updates {
		switch k {
		case "char_define":
			newData.CharDefine, _ = v.(string)
		case "user_define":
			newData.UserDefine, _ = v.(string)
		case "user_character_chat_summary":
			newData.UserCharacterChatSummary, _ = v.(string)
		case "relationship_desc":
			newData.RelationshipDesc, _ = v.(string)
		case "relationship_char_current_setting":
			newData.RelationshipCharCurrentSetting, _ = v.(string)
		case "relationship_chat_summary":
			newData.RelationshipChatSummary, _ = v.(string)
		case "allow_forbidden_behavior":
			newData.AllowForbiddenBehavior, _ = v.(string)
		case "scene_name":
			newData.SceneName, _ = v.(string)
		case "scene_content":
			newData.SceneContent, _ = v.(string)
		case "scene_limit":
			newData.SceneLimit, _ = v.(string)
		case "scene_guidance":
			newData.SceneGuidance, _ = v.(string)
		case "scene_img_gen":
			newData.SceneImgGen, _ = v.(string)
		case "scene_dialogue_example":
			newData.SceneDialogueExample, _ = v.(string)
			// default: // Removed logging for unknown keys to avoid linter/dependency issues in util package
			// 	logx.Warnf("TemplateManager.UpdateTemplate: 未知的更新键 '%s'", k)
		}
	}

	// 2. 更新此实例的 currentData 状态
	tm.currentData = newData

	// 3. 执行模板
	var buf bytes.Buffer
	if err := tm.tpl.Execute(&buf, tm.currentData); err != nil {
		return "", fmt.Errorf("执行模板失败: %v", err)
	}

	return buf.String(), nil
}

// GetCurrentData 获取当前数据
func (tm *TemplateManager) GetCurrentData() TemplateData {
	return tm.currentData
}

// ExportToJson 导出到JSON
func (tm *TemplateManager) ExportToJson() (string, error) {
	data, err := json.Marshal(tm.currentData)
	if err != nil {
		return "", fmt.Errorf("序列化数据失败: %v", err)
	}
	return string(data), nil
}

// LoadFromJson 从JSON加载
func (tm *TemplateManager) LoadFromJson(data string) error {
	var templateData TemplateData
	if err := json.Unmarshal([]byte(data), &templateData); err != nil {
		return fmt.Errorf("反序列化数据失败: %v", err)
	}
	tm.currentData = templateData
	return nil
}
