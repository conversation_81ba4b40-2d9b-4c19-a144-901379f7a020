package _03_characters

import (
	"context"

	"github.com/eddieowens/opts"
	"github.com/jzero-io/jzero-contrib/modelx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/cache" // Import sqlc for WithCache option
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	dbLLMConfig "xai.backend/internal/model/14_llm_config" // ++ Add import for llm config
	"xai.backend/internal/types"
)

type (
	// ExtendedCharactersModel 扩展接口
	ExtendedCharactersModel interface {
		charactersModel // 嵌入基础接口 (使用 gen 文件中的小写 c 接口名)
		CreateCharacterWithDefaults(ctx context.Context, data *Characters) (*Characters, error)
		FindCharactersByFilter(ctx context.Context, filter *types.CharacterFilter) ([]*Characters, int64, error)
		FindRecentChatCharacters(ctx context.Context, userId int64, limit int64) ([]int64, error)
		FindChatCharactersBackgroundImageURL(ctx context.Context, userId int64, characterIDs []int64) (map[int64]string, error)
		DelCache(ctx context.Context, id int64) (int, error)
	}

	// CustomCharactersModelImpl 实现 ExtendedCharactersModel 接口 (Exported)
	CustomCharactersModelImpl struct {
		*customCharactersModel                                    // Embed customCharactersModel to inherit jzero methods
		scenesModel            scenesModel                        // 假设 scenesModel 接口在同包定义
		promptTemplatesModel   PromptTemplatesModel               // 假设 PromptTemplatesModel 接口在同包定义
		llmConfigModel         dbLLMConfig.LlmConfigModel         // ++ Use base interface LlmConfigModel
		LlmCharacterModel      dbLLMConfig.LlmCharacterModelModel // ++ Add LlmCharacterModel field (use distinct name)
		RedisConn              *redis.Redis                       // ++ Add RedisConn field
	}
)

// NewExtendedCharactersModel 是新的构造函数，用于创建包含依赖的扩展模型实例
// 返回类型也应为接口类型，但暂时保持具体类型以匹配ServiceContext中的用法
// Change llmConfigModel parameter type to base interface LlmConfigModel
func NewExtendedCharactersModel(conn sqlx.SqlConn, c cache.CacheConf, scenesModel scenesModel, promptTemplatesModel PromptTemplatesModel, llmConfigModel dbLLMConfig.LlmConfigModel, llmCharacterModel dbLLMConfig.LlmCharacterModelModel) *CustomCharactersModelImpl {
	// Create the custom model which includes jzero methods
	// Use modelx.WithCacheConf if available to pass cache config correctly
	var modelOpts []opts.Opt[modelx.ModelOpts]
	if len(c) > 0 { // Check if cache config is provided
		modelOpts = append(modelOpts, modelx.WithCacheConf(c)) // Try modelx.WithCacheConf
	}
	customModel := NewCharactersModel(conn, modelOpts...) // Use the constructor from characters_model.go
	redisConn := redis.MustNewRedis(c[0].RedisConf)

	// Assert that customModel is actually *customCharactersModel to access fields later
	customModelConcrete, ok := customModel.(*customCharactersModel)
	if !ok {
		logx.Error("NewCharactersModel did not return *customCharactersModel as expected")
		return nil
	}

	return &CustomCharactersModelImpl{
		customCharactersModel: customModelConcrete, // Assign the created custom model
		scenesModel:           scenesModel,
		promptTemplatesModel:  promptTemplatesModel,
		llmConfigModel:        llmConfigModel, // Assign llmConfigModel (base interface)
		LlmCharacterModel:     llmCharacterModel,
		RedisConn:             redisConn,
	}
}
