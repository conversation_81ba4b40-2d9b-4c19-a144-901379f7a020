package _03_characters

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"github.com/jzero-io/jzero-contrib/condition"
	"github.com/zeromicro/go-zero/core/logx"

	// Import for chats model
	dbLLMCconfig "xai.backend/internal/model/14_llm_config" // Import for LlmCharacterModel type and model
	"xai.backend/internal/types"                            // 引入 types 包以使用 CharacterFilter
)

// --- Extended Characters Helper ---

// CreateCharacterWithDefaults 创建角色及其默认场景、提示词和 LLM 关联 (无事务)
func (m *CustomCharactersModelImpl) CreateCharacterWithDefaults(ctx context.Context, data *Characters) (*Characters, error) {
	var createdCharacter *Characters // Declare createdCharacter in the outer scope
	var res sql.Result               // Declare res and lastId outside the removed transaction scope
	var lastId int64
	var err error // Declare err in the outer scope

	logx.Infof("CreateCharacterWithDefaults: data: %+v", data)

	// 1. 如果提示词模版ID没有传入，查找默认的系统提示词模版
	if data.PromptTemplateId == 0 {
		defaultPrompt, err := m.promptTemplatesModel.FindOneByCondition(ctx, nil, condition.Condition{Field: "`is_default`", Operator: condition.Equal, Value: 1})
		if err != nil {
			logx.WithContext(ctx).Errorf("查找默认系统提示词模版失败: %v", err)
			return nil, fmt.Errorf("查找默认系统提示词模版失败: %w", err) // Return error immediately
		}
		data.PromptTemplateId = defaultPrompt.Id
	}

	// 2. 创建角色
	res, err = m.defaultCharactersModel.InsertWithCache(ctx, nil, data)
	if err != nil {
		logx.WithContext(ctx).Errorf("创建角色失败: %v", err)
		return nil, fmt.Errorf("创建角色失败: %w", err) // Return error immediately
	}
	lastId, err = res.LastInsertId()
	if err != nil {
		logx.WithContext(ctx).Errorf("获取角色新 ID 失败: %v", err)
		return nil, fmt.Errorf("获取角色新 ID 失败: %w", err) // Return error immediately
	}
	data.Id = lastId

	// 3. 创建默认 Story Scene 如果data.SceneIds为空 (NULL 或空字符串)，则创建默认场景
	if !data.SceneIds.Valid || data.SceneIds.String == "" {
		// 3.1 查找是否有默认场景
		var defaultScene *Scenes
		defaultScene, err = m.scenesModel.FindOneByCondition(ctx, nil, condition.Condition{Field: "`is_default`", Operator: condition.Equal, Value: 1})
		if err != nil {
			logx.WithContext(ctx).Errorf("查找默认场景失败: %v", err)
		}

		// 3.2 没有默认场景则创建默认场景
		if defaultScene == nil {
			defaultScene = &Scenes{
				CharacterId: data.Id,
				Name:        fmt.Sprintf("%s - 默认场景", data.Name),
				Description: "This is a normal scene",
				Greeting:    "你好，我是你的新朋友。",
				Stage:       1,
				IntimacyMin: 0,
				IntimacyMax: 10,
				Relationship: sql.NullString{
					String: `[{"relation_desc": "{char}和{user}刚认识不久, 是普通朋友关系。","char_csetting": "* 性格特点：温文尔雅 * 语言特点：温和,比较含蓄","im_threshold": "5"}]`,
					Valid:  true,
				},
				IsDefault: 1,
			}
			res, err = m.scenesModel.InsertWithCache(ctx, nil, defaultScene)
			if err != nil {
				logx.WithContext(ctx).Errorf("创建默认场景失败: %v", err)
				return nil, fmt.Errorf("创建默认场景失败: %w", err) // Return error immediately
			}
			defaultScene.Id, err = res.LastInsertId()
			if err != nil {
				logx.WithContext(ctx).Errorf("获取默认场景新 ID 失败: %v", err)
				return nil, fmt.Errorf("获取默认场景新 ID 失败: %w", err) // Return error immediately
			}
		}
		// 3.3 设置默认场景ID SceneIds是一个json数组，需要转换为字符串
		sceneIds := []int64{defaultScene.Id}
		sceneIdsJson, err := json.Marshal(sceneIds)
		if err != nil {
			logx.WithContext(ctx).Errorf("转换默认场景ID失败: %v", err)
			return nil, fmt.Errorf("转换默认场景ID失败: %w", err) // Return error immediately
		}
		data.SceneIds = sql.NullString{String: string(sceneIdsJson), Valid: true}

		// 3.4 更新角色的场景信息
		err = m.defaultCharactersModel.UpdateWithCache(ctx, nil, data)
		if err != nil {
			logx.WithContext(ctx).Errorf("更新角色场景信息失败: %v", err)
			return nil, fmt.Errorf("更新角色场景信息失败: %w", err) // Return error immediately
		}
	}

	// 4. 根据角色 NSFW 状态查找默认 LLM 配置
	isNsfwDefault := 1
	if data.IsNsfw == 1 {
		isNsfwDefault = 1
	} else {
		isNsfwDefault = 99
	}
	logx.Infof("CreateCharacterWithDefaults: isNsfwDefault: %v", isNsfwDefault)
	defaultLlm, err := m.llmConfigModel.FindOneByCondition(ctx, nil, condition.Condition{Field: "`is_default`", Operator: condition.Equal, Value: isNsfwDefault})
	logx.Infof("CreateCharacterWithDefaults: defaultLlm: %+v", defaultLlm)
	if err != nil {
		logx.WithContext(ctx).Errorf("查找默认 LLM 失败 (isNsfwDefault: %v): %v", isNsfwDefault, err)
		return nil, fmt.Errorf("查找默认 LLM 配置失败: %w", err) // Return error immediately
	}
	if defaultLlm == nil {
		logx.WithContext(ctx).Errorf("未找到 isNsfwDefault=%v 的默认 LLM 配置", isNsfwDefault)
		return nil, fmt.Errorf("未配置 isNsfwDefault=%v 的默认 LLM", isNsfwDefault) // Return error immediately
	}

	// 5. 创建 LLM 与角色的关联记录
	llm_character_model := &dbLLMCconfig.LlmCharacterModel{ // Use type from 14_llm_config
		CharacterId: lastId,
		LlmConfigId: defaultLlm.Id,
		Priority:    99,
		DeleteAt:    sql.NullTime{},
		DelState:    0,
	}
	_, err = m.LlmCharacterModel.InsertWithCache(ctx, nil, llm_character_model)
	if err != nil {
		logx.WithContext(ctx).Errorf("创建 LLM-角色关联失败 (charId: %d, llmId: %d): %v", lastId, defaultLlm.Id, err)
		return nil, fmt.Errorf("创建 LLM-角色关联失败: %w", err) // Return error immediately
	}

	// 6. 查询新创建的角色信息
	createdCharacter, err = m.defaultCharactersModel.FindOneWithCache(ctx, nil, lastId)
	if err != nil {
		logx.WithContext(ctx).Errorf("查询新创建的角色失败 (id: %d): %v", lastId, err)
		return nil, fmt.Errorf("查询新创建的角色失败: %w", err)
	}

	logx.WithContext(ctx).Infof("成功创建角色 %d 及其关联项 (无事务)", lastId)
	return createdCharacter, nil
}

// FindCharactersByFilter 根据过滤器、排序和分页查询角色列表 (使用 jzero PageByCondition 和 condition struct)
func (m *CustomCharactersModelImpl) FindCharactersByFilter(ctx context.Context, filter *types.CharacterFilter) ([]*Characters, int64, error) {
	// 默认值和限制
	limit := filter.Limit
	if limit <= 0 || limit > 100 { // 设置默认和最大限制
		limit = types.DefaultLimitSize
	}
	page := filter.Page
	if page <= 0 {
		page = types.DefaultPageSize
	}
	offset := (page - 1) * limit

	// 构建查询条件 - 使用正确的 condition.Condition struct 字段和字符串操作符
	var conds []condition.Condition

	conds = append(conds, condition.Condition{Field: "`visibility`", Operator: condition.Equal, Value: "public"})

	// 处理搜索关键词 (模糊匹配名称、标签、描述)
	if filter.Search != "" {
		searchTerm := "%" + filter.Search + "%"
		// 假设 Or 相关的字段构造 OR 条件组，操作符用字符串
		orCondition := condition.Condition{
			Or:          true,
			OrFields:    []string{"`name`", "`tags`", "`description`"},
			OrOperators: []condition.Operator{"like", "like", "like"},
			OrValues:    []any{searchTerm, searchTerm, searchTerm},
		}
		conds = append(conds, orCondition)
	}

	// 处理标签过滤
	if len(filter.Tags) > 0 {
		for _, tag := range filter.Tags {
			tagTerm := "%" + tag + "%"
			conds = append(conds, condition.Condition{Field: "`tags`", Operator: condition.Like, Value: tagTerm})
		}
	}

	// 构建排序 - 尝试将排序/分页表示为特殊的操作符字符串或查看是否有专用字段
	switch filter.SortBy {
	case "latest":
		conds = append(conds, condition.Condition{Operator: condition.OrderBy, Value: "created_at DESC"})
	case "name":
		conds = append(conds, condition.Condition{Operator: condition.OrderBy, Value: "name ASC"})
	case "popular":
		fallthrough // Fallthrough to default
	default:
		conds = append(conds, condition.Condition{Operator: condition.OrderBy, Value: "interaction_count DESC"})
	}

	// 添加分页
	conds = append(conds, condition.Condition{Operator: condition.Limit, Value: limit})
	conds = append(conds, condition.Condition{Operator: condition.Offset, Value: offset})

	// --- 执行查询 ---
	characters, total, err := m.customCharactersModel.PageByCondition(ctx, nil, conds...)
	if err != nil {
		logx.WithContext(ctx).Errorf("FindCharactersByFilter: PageByCondition failed: %v", err)
		return nil, 0, fmt.Errorf("查询角色列表失败: %w", err)
	}

	return characters, total, nil
}

// DelCache 删除角色缓存
func (m *CustomCharactersModelImpl) DelCache(ctx context.Context, id int64) (int, error) {
	v, delCacheErr := m.RedisConn.DelCtx(ctx, fmt.Sprintf("%s%d", cacheCharactersIdPrefix, id))
	if delCacheErr != nil {
		logx.WithContext(ctx).Errorf("删除角色缓存失败 (id: %d): %v", id, delCacheErr)
		return 0, fmt.Errorf("删除角色缓存失败: %w", delCacheErr)
	}
	logx.WithContext(ctx).Infof("删除角色缓存成功 (id: %d, 影响行数: %d)", id, v)
	return v, nil
}

// FindRecentChatCharacters 获取最近聊过的两个角色
func (m *CustomCharactersModelImpl) FindRecentChatCharacters(ctx context.Context, userId int64, limit int64) ([]int64, error) {

	// 根据消息表的记录，获取最近聊过的两个角色
	query := `
			SELECT character_id
			FROM chats
			WHERE user_id = ?
			GROUP BY character_id
			ORDER BY MAX(created_at) DESC
			LIMIT ?
			`
	args := []interface{}{userId, limit}

	var twoRecentCharIDs []int64

	err := m.conn.QueryRowsCtx(ctx, &twoRecentCharIDs, query, args...)
	if err != nil {
		logx.WithContext(ctx).Errorf("获取最近聊过的两个角色失败: %v", err)
		return nil, fmt.Errorf("获取最近聊过的两个角色失败: %w", err)
	}
	logx.WithContext(ctx).Infof("获取最近聊过的两个角色: %v", twoRecentCharIDs)

	return twoRecentCharIDs, nil
}

func (m *CustomCharactersModelImpl) FindChatCharactersBackgroundImageURL(ctx context.Context, userId int64, characterIDs []int64) (map[int64]string, error) {

	logx.WithContext(ctx).Infof("userId: %d, <characterIDs>: %v", userId, characterIDs)
	var convertedCharacterIDs []int
	for _, id := range characterIDs {
		convertedCharacterIDs = append(convertedCharacterIDs, int(id))
	}

	// 在chats表中查询所有角色的最近一次聊天记录，然后取这次聊天记录的场景ID
	query := fmt.Sprintf(`WITH ranked_chats AS (
				SELECT 
					id,
					user_id,
					character_id,
					scene_id,
					created_at,
					ROW_NUMBER() OVER (
						PARTITION BY user_id, character_id 
						ORDER BY created_at DESC
					) AS rn
				FROM %[1]s
				WHERE 
					user_id = ?
					AND character_id IN (?)
			)
			SELECT 
				id ,
				character_id,
				user_id ,
				scene_id ,
				created_at 
			FROM ranked_chats
			WHERE rn = 1;
			`, "chats")
	args := []interface{}{userId, convertedCharacterIDs}

	var result []*types.ChatCharactersQueryResult

	if len(convertedCharacterIDs) > 0 {
		err := m.conn.QueryRowsCtx(ctx, &result, query, args...)
		if err != nil {
			logx.WithContext(ctx).Errorf("查询用户和角色最近一次聊天记录失败: %v", err)
			return nil, fmt.Errorf("查询用户和角色最近一次聊天记录失败: %w", err)
		}
		logx.WithContext(ctx).Infof("获取用户和角色最近一次聊天记录: %v", result)
	}

	resultMap := make(map[int64]string)
	for _, chat := range result {
		sceneID := chat.SceneID
		sceneResp, err := m.scenesModel.FindOneWithCache(ctx, nil, sceneID)
		if err != nil {
			return nil, err
		}
		resultMap[chat.CharacterID] = sceneResp.BackgroundImageUrl.String
	}

	logx.WithContext(ctx).Infof("获取用户和角色最近一次聊天记录的场景背景图片: %v", resultMap)

	return resultMap, nil
}
