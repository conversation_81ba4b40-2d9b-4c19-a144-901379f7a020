## TODO
✅ 75. 图片放到脚本里面
✅ 76. OOC 增加重置对话提示，增加不可以乱提示
✅ 77. 修复过期时间的问题，改为UTC+8的今天235959
✅ 78. get的两个方法请求参数改为form，放在body体不符合REST规范

[] 80. 记录亲密度变化

[] . 消息增加已读/未读
[] . 给前端的图片返回尺寸
[] 99. 持续 -- 整理基于上线需要的SQL初始化语句

## TO Test：


## TO Determine
[] chat的统计数据从chats表分离成为另外一个表


## Done
✅ 1. 不同的模型返回流格式不同，需要做适配, 目前适配的是mistral、mistralai、deepseek
✅ 2. 亲密度下降到当前子场景之下的子场景时，才推送提示词
✅ 3. 亲密度上升到当前子场景的下一个子场景时，是否需要推送提示词
✅ 4. 场景间升级
[X]5. **前端展示场景列表**
[X]6. **按照新的界面修改样式**
✅ 7. 角色增加背景图片 -- 接口也增加
✅ 8. 增加场景Greeting，初次对话，有场景则用默认场景的Greeting，没有则用角色的Greeting
✅ 9. 对话 返回增加 亲密度，场景的 ID、名称、描述、Greeting
✅ 10. 场景升级，limit 和 guidance更新-- 再测试
✅ 11. 订阅表，user_id为 唯一索引
✅ 12. 重新整理SQL初始化语句
✅ 13. 对话的ChatSystemPrompt 并发问题
✅ 14. 按照新的Plan定义修改Plan数据库初始化脚本
✅ 15. 对付费用户的相关逻辑做控制
✅ 16. 对话中的最近消息、交互次数，分离到缓存中，不实时更新数据库，提升效率
✅ 17. 亲密度需要改为缓存方式，否则会频繁刷库
✅ 18. 增加瀑布流接口，初始两个角色为最近聊过的
✅ 19. 增加Openrouter的deepseekV3调用，并调试
✅ 20. 部署到aws服务器上
✅ 21. 拉一个新的分支来对接通用的账户和支付
✅ 22. 验证基于deepseekV3的对话总结
✅ 23. 基于路径配置nginx的路由
✅ 24. 配置PM2启动
✅ 25. 增加对话的 当前阶段->下一阶段， 返回给前端
✅ 26. 业务内部错误码的规划
✅ 27. lastmessages改为最近一条消息
✅ 28. 给前端的场景返回最大亲密度
✅ 29. 准备所需要的服务器资源清单
✅ 30. 消息返回，带场景过滤
✅ 31. 对话效率优化
✅ 32. 订阅计划的描述改json格式返回
✅ 33. 支付的对接，构造数据
✅ 34. 支付的幂等实现
✅ 35. 消息中增加类型、是否需要解锁、解锁内容，支持图片解锁功能实现
✅ 36. VIP配图上限50张
✅ 37. IntimacyImage事件改为json返回，带有这个url是否是lock
✅ 38. 订阅成功后消息全部解锁
✅ 39. 处理下一个可用更新场景更新，不要重复更新
✅ 40. 关系的描述，增加返回给前端的专用描述
✅ 41. 订阅模块的错误码标准化
✅ 42. 修复对话中异常返回没有关闭chunk的问题
✅ 43. 对话中的亲密度为负数/亲密度超出当前场景的最低亲密度，需要设定为当前场景的最低/最高
✅ 44. 对话和查询的越权处理
✅ 45. 增加用户的初始描述：姓名: 小王 \n 性别: 男 \n 年龄: 30 \n 性格: 开朗
✅ 46. 增加角色的卡片预览图
✅ 47. token接口返回昵称，放到上下文中，并且在创建对话时加载到对话的初始描述里面
✅ 48. 提示词中亲密度是个增量值，加减分
✅ 49. 每个角色只有一个对话 -- 根据角色获取对话列表已经有，先不改
✅ 50. 角色接口增加是否已经点赞
✅ 51. 设计角色和场景
✅ 52. 修正发送消息的逻辑
✅ 53. 角色查询接口不需要认证
✅ 54. 用脚本生成所有新的角色的sql脚本并执行到测试环境中
✅ 55. 每个角色每天20条
✅ 56. 带锁的图片配置
✅ 57. 图片切换到aws的s3上面
✅ 58. 增加事件告知前端，关系已经发生变化
✅ 59. 重新生成角色3的第三个场景，角色4/5
✅ 60. 修复图片计数的问题
✅ 61. 增加大模型的政治、反动、种族、凶杀、未成年等限制
✅ 61. 对OOC采取友好的提示词
✅ 62. 增加角色的经历
✅ 62. 改提示词去掉剧情引导
✅ 63. 对没有返回亲密度的进行大模型二次请求
✅ 64. 前端对话闪退没有记录的问题
✅ 65. VIP过期问题
✅ 66. github账号加运维
✅ 67. 图片可能会有被封的风险--用新加坡的s3
✅ 68. 修改运维文档 -- 继续
✅ 69. 修改一个角色为改为框架/抽象 引导词
✅ 70. 测试 丰富引导词、丰富角色经历、去掉引导词、改为框架/抽象 引导词 四种方式的优劣性
✅ 71. 增加场景往回切换的功能
✅ 72. 重新生成亲密度的对话取10条
✅ 73. 关系后面加上 需要亲密度
✅ 74. 确定最后的角色引导词版本